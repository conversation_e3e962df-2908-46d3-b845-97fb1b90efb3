using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using Medusa.Service.Modeling.Application.CRMReport;
using Medusa.Service.Modeling.Application.CRMReport.Dto;
using Medusa.Service.Modeling.Application.DataBaseManage;
using Medusa.Service.Modeling.Application.ProcessRelated;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Crypto.Tls;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// CRM报表
    /// </summary>
    [Route("v1/daily")]
    [ApiExplorerSettings(GroupName = "Daily.v1")]
    public class V1_DailyController : ProductControllerBase
    {
        #region //  服务注入
        readonly ICRMReportService _cRMReportService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_SETReportController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="cRMReportService">cRMReportService</param>
        public V1_DailyController(ICRMReportService cRMReportService)
        {
            _cRMReportService = cRMReportService;
        }
        #endregion

        /// <summary>
        /// 日报报表
        /// </summary>
        /// <param name="dto">查询</param>
        /// <returns>111</returns>
        [HttpPost("get")]
        public object GetDailyReport([FromBody] DailyReportQueryDto dto)
        {
            var data = _cRMReportService.GetDailyReport(dto);
            return new { cols = data.Item1, dt = data.Item2, total = data.Item3 };
        }

        /// <summary>
        /// 日报报表
        /// </summary>
        /// <param name="dto">查询</param>
        [HttpPost("export")]
        public void ExportDaily([FromBody] DailyReportQueryDto dto)
        {
            dto.IsAll = true;
            var data = _cRMReportService.ExportDaily(dto);
            var name = HttpUtility.UrlEncode($"{data.GetSheetName(0)}.xlsx", Encoding.UTF8);
            Response.Clear();
            Response.ContentType = "application/octet-stream";
            Response.Headers.Add("Content-Disposition", $"attachment; filename={name}");
            data.Write(Response.Body);
            data.Close();
        }
    }
}
