using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Medusa.Service.Modeling.Application.DataBaseManage.Dtos
{
    
    
    
    public class DataBaseDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        [Required]
        public string OnlyCode { get; set; }

        
        
        
        [Required]
        public string Name { get; set; }

        
        
        
        [Required]
        public string Description { get; set; }

        
        
        
        [Required]
        public string Localhost { get; set; }

        
        
        
        [Required]
        public string Account { get; set; }

        
        
        
        [Required]
        public string Password { get; set; }

        
        
        
        [Required]
        public string Type { get; set; }

        
        
        
        [Required]
        public bool IsOutSideBusinessBase { get; set; }

        
        
        
        public string Port { get; set; }
    }
}
