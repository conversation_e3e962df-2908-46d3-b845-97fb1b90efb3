using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 操作日志表
    /// </summary>
    [EntityTable("OperationLog")]
    public class OperationLogEntity
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid LogId { get; set; }

        /// <summary>
        /// 菜单名称
        /// </summary>
        public string Menu { get; set; }

        /// <summary>
        /// 操作按钮名称
        /// </summary>
        public string Action { get; set; }

        /// <summary>
        /// 主表名称
        /// </summary>
        public string MainTableName { get; set; }

        /// <summary>
        /// 主表数据Id
        /// </summary>
        public string MainTableDataId { get; set; }

        /// <summary>
        /// 表名称
        /// </summary>
        public string TableName { get; set; }

        /// <summary>
        /// 表数据Id
        /// </summary>
        public string TableDataId { get; set; }

        /// <summary>
        /// 数据内容
        /// </summary>
        public string DataContent { get; set; }

        /// <summary>
        /// 数据描述
        /// </summary>
        public string DataDescription { get; set; }

        /// <summary>
        /// 创建者Id
        /// </summary>
        public Guid CreateUserId { get; set; }

        /// <summary>
        /// 创建者名称
        /// </summary>
        public string CreateUserName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建者登录账号
        /// </summary>
        public string CreateUserLoginId { get; set; }
    }
}
