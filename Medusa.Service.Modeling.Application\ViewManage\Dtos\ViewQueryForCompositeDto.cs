using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ViewManage.Dtos
{
    
    
    
    public class ViewQueryForCompositeDto : PageQueryDtoBase
    {
        
        
        
        public Guid? DataBaseId { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string DataBaseName { get; set; }

        
        
        
        public string DataBaseType { get; set; }

        
        
        
        public bool? HasColumns { get; set; }

        
        
        
        public Guid? ApplicationId { get; set; }
    }
}
