using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// CRM月度任务电话拜访
    /// </summary>
    [SugarTable("crmtaskphonevisit")]
    public class CrmTaskPhoneVisit
    {
        /// <summary>
        /// 表中的唯一标识ID
        /// </summary>
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 任务主键ID
        /// </summary>
        [SugarColumn(ColumnName = "TaskId")]
        public Guid? TaskId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [SugarColumn(ColumnName = "CustomerName")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [SugarColumn(ColumnName = "ContactPerson")]
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系人职位
        /// </summary>
        [SugarColumn(ColumnName = "ContactPosition")]
        public string ContactPosition { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [SugarColumn(ColumnName = "ContactPhone")]
        public string ContactInfo { get; set; }

        /// <summary>
        /// 跟进/访问时间
        /// </summary>
        [SugarColumn(ColumnName = "ContactTime")]
        public DateTime? FollowUpTime { get; set; }

        /// <summary>
        /// 0=草稿，1=已提交，2=删除
        /// </summary>
        [SugarColumn(ColumnName = "Status")]
        public int Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "Remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [SugarColumn(ColumnName = "CreateUserId")]
        public string CreateUserId { get; set; }

        /// <summary>
        /// 创建用户姓名
        /// </summary>
        [SugarColumn(ColumnName = "CreateUserName")]
        public string CreateUserName { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "CreateDate")]
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 修改人员用户ID
        /// </summary>
        [SugarColumn(ColumnName = "ModifyUserId")]
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 修改人员姓名
        /// </summary>
        [SugarColumn(ColumnName = "ModifyUserName")]
        public string ModifyUserName { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "ModifyDate")]
        public DateTime? ModifyDate { get; set; }
    }
}
