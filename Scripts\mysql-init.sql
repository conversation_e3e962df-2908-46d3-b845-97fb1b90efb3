﻿CREATE DATABASE IF NOT EXISTS `BPM_Platform`;
USE `BPM_Platform`;
set FOREIGN_KEY_CHECKS=0;
DROP TABLE IF EXISTS `NotificationRules`;
CREATE TABLE `NotificationRules` ( `NotificationRuleId` bigint NOT NULL AUTO_INCREMENT COMMENT '主键', `Code` varchar(50) NOT NULL COMMENT '编码', `Name` varchar(50) NOT NULL COMMENT '名称', `Type` int NOT NULL COMMENT '类型（0立即发送，1到点发送，2循环发送）', `DelayTime` datetime NULL COMMENT '延迟的时间', `Cron` varchar(50) NOT NULL COMMENT '循环发送设置表达式', `FailCounts` int NOT NULL COMMENT '失败次数,循环消息不处理', `IsValid` tinyint NOT NULL COMMENT '是否有效', `CreateTime` datetime NOT NULL COMMENT '创建时间', PRIMARY KEY (`NotificationRuleId`) )   COMMENT = '消息规则';
DROP TABLE IF EXISTS `Attachments`;
CREATE TABLE `Attachments` ( `AttachmentId` char(36) NOT NULL COMMENT '主键', `IsDelete` int NULL DEFAULT ((0)) COMMENT '0未删除1删除', `Size` int NULL COMMENT '文件大小', `FileName` varchar(50) NULL COMMENT '文件展示名称', `State` int NULL DEFAULT ((0)) COMMENT '1激活0未激活', `UUName` varchar(200) NULL COMMENT 'UUname', `CreateTime` datetime NULL COMMENT '创建时间', `Creator` char(36) NULL COMMENT '创建人', `CreatorName` varchar(50) NULL COMMENT '创建人名称', `SystemType` varchar(50) NULL COMMENT '上传系统来源', `FilePath` varchar(200) NULL COMMENT '文件路径', PRIMARY KEY (`AttachmentId`) )   COMMENT = '附件';
DROP TABLE IF EXISTS `Menus`;
CREATE TABLE `Menus` ( `MenuId` char(36) NOT NULL COMMENT '主键', `ParentId` char(36) NULL COMMENT '父Id', `AppCode` varchar(10) NOT NULL COMMENT '应用Id', `PageType` char(1) NULL COMMENT '页面类型', `OrderNo` int NULL COMMENT '排序', `DataRights` text NULL, `OpenType` char(1) NULL DEFAULT ('S'), `InUse` tinyint NULL COMMENT '使用状态', `MODULERIGHTS` varchar(100) NULL, `MODIFYCOLUMNLASTOFINDEX` int NULL, `DELETECOLUMNLASTOFINDEX` int NULL, `F1` text NULL, `F2` text NULL, `F3` varchar(50) NULL, PRIMARY KEY (`MenuId`) )   COMMENT = '菜单';
DROP TABLE IF EXISTS `MenuDetails`;
CREATE TABLE `MenuDetails` ( `MenuDetailId` char(36) NOT NULL COMMENT '主键', `MenuName` varchar(100) NULL COMMENT '模块名称', `MenuEnglishName` varchar(50) NULL COMMENT '模块英文名称', `LanguageCode` varchar(10) NULL COMMENT '语言编码', `MenuId` char(36) NOT NULL COMMENT '菜单Id', `FileURL` varchar(200) NULL COMMENT '文件路径', `FunctionURL` varchar(100) NULL, `Hidden` tinyint NULL COMMENT '隐藏菜单', `IconURL1` varchar(255) NULL, `IconURL2` varchar(255) NULL, `IconURL3` varchar(255) NULL, PRIMARY KEY (`MenuDetailId`) )   COMMENT = '菜单明细';
DROP TABLE IF EXISTS `Dictionaries`;
CREATE TABLE `Dictionaries` ( `DictionaryId` char(36) NOT NULL COMMENT '主键', `Code` varchar(50) NULL COMMENT '字典编码', `Name` varchar(100) NULL COMMENT '字典名称', `Value` text NULL COMMENT '字典值', `OrderNum` int NULL COMMENT '排序', `Status` int NULL COMMENT '状态', `Remark` longtext NULL COMMENT '备注', `UpperId` char(36) NULL COMMENT '父Id', `TypeCode` varchar(50) NULL COMMENT '分组编码', `TypeName` varchar(20) NULL COMMENT '分组名称', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`DictionaryId`) )   COMMENT = '数据字典';
DROP TABLE IF EXISTS `JobTitles`;
CREATE TABLE `JobTitles` ( `JobTitleId` char(36) NOT NULL COMMENT '主键', `JobNo` varchar(20) NOT NULL COMMENT '职位编号', `Name` char(50) NOT NULL COMMENT '名称', `JobNameEn` varchar(100) NULL COMMENT '英文名称', `JobLevel` int NOT NULL COMMENT '职位级别', `JobCode` varchar(100) NULL COMMENT '职位编码', `JobCategory` varchar(50) NOT NULL COMMENT '职位类别', `Description` text NULL COMMENT '描述', `Status` int NULL COMMENT '状态', `F1` varchar(200) NULL, `F2` varchar(200) NULL, `F3` varchar(200) NULL, PRIMARY KEY (`JobTitleId`) )   COMMENT = '职位';
DROP TABLE IF EXISTS `Organizations`;
CREATE TABLE `Organizations` ( `OrganizationId` int NOT NULL COMMENT '主键', `Name` varchar(200) NOT NULL COMMENT '名称', `Name2` varchar(200) NULL COMMENT '名称', `DeptCode` varchar(50) NOT NULL COMMENT '部门编码', `CompanyId` int NOT NULL COMMENT '公司Id', `UpperId` int NULL COMMENT '上级Id', `DeptManager` char(36) NULL COMMENT '负责人', `DeptTelephone` varchar(50) NULL COMMENT '电话', `Level` int NULL COMMENT '层级', `Status` int NOT NULL COMMENT '状态', `Remark` longtext NULL COMMENT '备注', `CityId` int NULL COMMENT '城市Id', `IndependentRoll` tinyint NULL DEFAULT ((0)), `SortCode` int NULL, `ProcessLevel` int NULL COMMENT '流程组织级别', `ProcessType` int NULL COMMENT '流程组织类型', `FullPathText` text NULL COMMENT '全路径', `FullPathCode` text NULL COMMENT '全路径Code', `F1` text NULL, `F2` text NULL, `F3` text NULL, PRIMARY KEY (`OrganizationId`) )   COMMENT = '组织';
DROP TABLE IF EXISTS `Positions`;
CREATE TABLE `Positions` ( `PositionId` char(36) NOT NULL COMMENT '主键', `OrganizationId` int NOT NULL COMMENT '组织Id', `JobTitleId` char(36) NOT NULL COMMENT '职位Id', `OfficeId` int NULL, `PositionCode` varchar(100) NULL COMMENT '岗位编码', `Name` varchar(100) NULL COMMENT '名称', `Description` varchar(200) NULL COMMENT '描述', `UpperId` char(36) NULL COMMENT '父级Id', `IsActive` tinyint NOT NULL COMMENT '是否有效', `CompanyId` int NULL COMMENT '公司ID(岗位挂公司情况)', `Type` int NULL COMMENT 'null或0代表普通岗位，1代表分管领导', `F1` varchar(200) NULL, `F2` varchar(200) NULL, `F3` varchar(200) NULL, `F4` varchar(200) NULL, `F5` varchar(200) NULL, PRIMARY KEY (`PositionId`, `OrganizationId`, `JobTitleId`) )   COMMENT = '岗位';
DROP TABLE IF EXISTS `Users`;
CREATE TABLE `Users` ( `UserId` char(36) NOT NULL COMMENT '主键', `UserLoginId` varchar(50) NULL COMMENT '登录账号', `UserName` varchar(200) NULL COMMENT '用户名称', `UserAlias` varchar(50) NULL COMMENT '用户别名', `Password` varchar(100) NOT NULL COMMENT '登录密码', `UserType` char(1) NOT NULL COMMENT '用户类型', `SourceType` char(1) NULL COMMENT '数据源类型', `Source` varchar(20) NULL COMMENT '数据源', `FirstName` varchar(100) NULL COMMENT 'FIrst名称', `MiddleName` varchar(100) NULL COMMENT 'Middle名称', `LastName` varchar(100) NULL COMMENT 'Last名称', `PinyinFirstWord` varchar(20) NULL COMMENT '名称拼音首字母', `Gender` char(1) NULL COMMENT '性别', `Email` text NULL COMMENT '邮件', `Emailbake` text NULL, `BirthDay` datetime NULL COMMENT '生日', `Status` int NULL COMMENT '状态', `MobilePhone` varchar(50) NULL COMMENT '电话', `Extension` varchar(20) NULL, `Company` varchar(100) NULL COMMENT '公司', `EmployeeType1` varchar(50) NULL, `CostCenterDesc` varchar(200) NULL, `Remark` text NULL COMMENT '备注', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `MidifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', `LastestLoginDate` datetime NULL COMMENT '最新登录时间', `F1` text NULL, `F2` text NULL, `F3` text NULL, `F4` text NULL, `F5` text NULL, `F6` text NULL, `SubCompanyId` int NULL COMMENT '上级公司Id', `PasswordLockTime` datetime NULL, `NoLoginLockTime` datetime NULL, `ApproveDate` datetime NULL, `LockStatus` int NULL, `InstanceId` varchar(50) NULL, `WorkNumber` varchar(50) NULL COMMENT '工号', `IsStoreRequest` int NULL, `SortCode` int NULL, `UpperUserId` varchar(200) NULL, `Grade` varchar(200) NULL COMMENT '等级', `JobTitle` varchar(200) NULL COMMENT '职位', `CompanyId` int NULL COMMENT '公司Id', `OrganizationId` int NULL COMMENT '组织Id', `FullPathText` text NULL COMMENT '组织名称全路径', `FullPathCode` text NULL COMMENT '组织全路径', `CompanyName` varchar(100) NULL COMMENT '公司名称', `OrganizationName` varchar(100) NULL COMMENT '组织名称', PRIMARY KEY (`UserId`) )   COMMENT = '人员';
DROP TABLE IF EXISTS `UserPositionRelations`;
CREATE TABLE `UserPositionRelations` ( `UserPositionRelationId` char(36) NOT NULL COMMENT '主键', `PositionId` char(36) NOT NULL COMMENT '岗位Id', `UserId` char(36) NOT NULL COMMENT '用户Id', `PrimaryPosition` tinyint NOT NULL COMMENT '是否主岗', `StartDate` datetime NOT NULL COMMENT '开始日期', `EndDate` datetime NOT NULL COMMENT '结束日期', `IsActive` tinyint NOT NULL COMMENT '是否有效', `F1` varchar(200) NULL, `F2` varchar(200) NULL, `F3` varchar(200) NULL)   COMMENT = '岗位人员关系';
DROP TABLE IF EXISTS `ConfigSettings`;
CREATE TABLE `ConfigSettings` ( `Key` varchar(50) NOT NULL COMMENT '配置Key', `Value` varchar(210) NULL COMMENT '配置值', `Group` varchar(50) NULL COMMENT '分组', `Creator` char(36) NULL COMMENT '创建人', `CreatorName` varchar(50) NULL COMMENT '创建人名称', `CreatorTime` datetime NULL COMMENT '创建时间', PRIMARY KEY (`Key`) )   COMMENT = '系统配置';
DROP TABLE IF EXISTS `MenuActionRelations`;
CREATE TABLE `MenuActionRelations` ( `MenuActionRelationId` int NOT NULL AUTO_INCREMENT COMMENT '关联ID', `Code` varchar(10) NOT NULL COMMENT '功能编码', `Name` varchar(50) NULL COMMENT '功能名称', `MenuId` char(36) NOT NULL COMMENT '菜单Id', PRIMARY KEY (`MenuActionRelationId`) )   COMMENT = '菜单功能';
DROP TABLE IF EXISTS `NotificationTempletes`;
CREATE TABLE `NotificationTempletes` ( `Code` varchar(50) NOT NULL COMMENT '消息编码', `Name` varchar(50) NOT NULL COMMENT '消息名称', `Content` longtext NOT NULL COMMENT '内容', `Type` int NOT NULL COMMENT '消息类型（0邮件，1短信）', `IsValid` tinyint NOT NULL COMMENT '是否有效', `Subject` varchar(100) NOT NULL COMMENT '主题', PRIMARY KEY (`Code`) )   COMMENT = '消息模板';
DROP TABLE IF EXISTS `Companies`;
CREATE TABLE `Companies` ( `CompanyId` int NOT NULL COMMENT '主键', `CompanyCode` varchar(50) NOT NULL COMMENT '组织编码', `ComapnyName` varchar(100) NULL COMMENT '组织名称', `CompanyNameEn` varchar(100) NULL COMMENT '组织英文名称', `City` int NULL COMMENT '城市Id', `BusinessType` varchar(20) NULL COMMENT '业务类型', `Address` varchar(100) NULL, `ContactUser1` varchar(100) NULL, `ContactUser2` varchar(100) NULL, `PhoneNumber1` varchar(20) NULL, `PhoneNumber2` varchar(20) NULL, `Fax` varchar(20) NULL, `Status` int NULL COMMENT '状态', `Remark` text NULL COMMENT '备注', `CompanyManager` char(36) NULL COMMENT '负责人', `CompanyType` int NULL, `Domain` varchar(100) NULL, `UpperId` int NULL COMMENT '父级', `Level` int NULL COMMENT '层级', `SortCode` int NULL, `ShowInMDS` char(1) NULL, `ProcessLevel` int NULL COMMENT '流程组织级别', `ProcessType` int NULL COMMENT '流程组织类型', `FullPathText` text NULL COMMENT '组织名称全路径', `FullPathCode` text NULL COMMENT '组织全路径', `F1` text NULL, `F2` text NULL, `F3` varchar(10) NULL, `F4` varchar(10) NULL, `F5` text NULL, PRIMARY KEY (`CompanyId`) )   COMMENT = '公司';
DROP TABLE IF EXISTS `BusinessIdentityAccessGroupDetails`;
CREATE TABLE `BusinessIdentityAccessGroupDetails` ( `BusinessIdentityAccessGroupDetailId` char(36) NOT NULL, `BusinessIdentityAccessGroupId` char(36) NOT NULL, `PermissionType` int NULL, `FullPathCode` text NULL, `FullPathText` text NULL, `CreateUserID` char(36) NULL, `CreateDate` datetime NULL, `ModifyUserID` char(36) NULL, `ModifyDate` datetime NULL, `F1` varchar(50) NULL, `F2` varchar(50) NULL, `F3` varchar(50) NULL, `F4` varchar(50) NULL, `F5` varchar(50) NULL, `F6` varchar(50) NULL, PRIMARY KEY (`BusinessIdentityAccessGroupDetailId`) )   COMMENT = '行安全性明细';
DROP TABLE IF EXISTS `BusinessIdentityAccessGroups`;
CREATE TABLE `BusinessIdentityAccessGroups` ( `BusinessIdentityAccessGroupId` char(36) NOT NULL, `GroupName` varchar(50) NULL, `AccessType` int NULL, `Description` longtext NULL, `Status` int NULL, `CreateUserID` char(36) NULL, `CreateDate` datetime NULL, `ModifyUserID` char(36) NULL, `ModifyDate` datetime NULL, `F1` varchar(50) NULL, `F2` varchar(50) NULL, `F3` varchar(50) NULL, `F4` varchar(50) NULL, `F5` varchar(50) NULL, `F6` varchar(50) NULL, PRIMARY KEY (`BusinessIdentityAccessGroupId`) )   COMMENT = '行安全性组';
DROP TABLE IF EXISTS `BusinessIdentityAccessGroupUsers`;
CREATE TABLE `BusinessIdentityAccessGroupUsers` ( `BusinessIdentityAccessGroupUsersId` char(36) NOT NULL, `BusinessIdentityAccessGroupId` char(36) NOT NULL, `UserID` char(36) NOT NULL, `CreateUserID` char(36) NULL, `CreateDate` datetime NULL, `ModifyUserID` char(36) NULL, `ModifyDate` datetime NULL, `F1` varchar(50) NULL, `F2` varchar(50) NULL, `F3` varchar(50) NULL, `F4` varchar(50) NULL, `F5` varchar(50) NULL, `F6` varchar(50) NULL, PRIMARY KEY (`BusinessIdentityAccessGroupUsersId`) )   COMMENT = '行安全性人员';
DROP TABLE IF EXISTS `CommonRoles`;
CREATE TABLE `CommonRoles` ( `RoleId` char(36) NOT NULL COMMENT '主键', `RoleCode` varchar(50) NULL COMMENT '角色编码', `RoleType` varchar(10) NULL COMMENT '角色类型（S系统超级管理员）', `RoleName` varchar(255) NULL COMMENT '角色名称', `Description` longtext NULL COMMENT '描述', `Status` int NULL COMMENT '1=有效；0=无效', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`RoleId`) )   COMMENT = '标准角色';
DROP TABLE IF EXISTS `CommonRoleUserRelations`;
CREATE TABLE `CommonRoleUserRelations` ( `RoleUserRelationId` char(36) NOT NULL COMMENT '主键', `RoleId` char(36) NULL COMMENT '角色Id', `OrgCode` varchar(255) NULL COMMENT '组织Code', `UserId` char(36) NULL COMMENT '人员Id', `UserName` varchar(255) NULL COMMENT '人员名称', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`RoleUserRelationId`) )   COMMENT = '标准角色人员';
DROP TABLE IF EXISTS `CommonRoleDataRights`;
CREATE TABLE `CommonRoleDataRights` ( `RoleDataRightId` char(36) NOT NULL COMMENT '主键', `RoleId` char(36) NULL COMMENT '角色Id', `Type` int NULL COMMENT '类型（0数据范围，1数据行安全性，2自定义）', `DataRights` longtext NULL COMMENT '数据权限（自定义值 | 数据范围）', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`RoleDataRightId`) )   COMMENT = '标准角色数据权限';
DROP TABLE IF EXISTS `CommonRoleDataRightDetails`;
CREATE TABLE `CommonRoleDataRightDetails` ( `RoleDataRightDetailId` char(36) NOT NULL COMMENT '主键', `RoleId` char(36) NULL COMMENT '角色Id', `FullPathCode` longtext NULL COMMENT '组织全路径', `FullPathText` longtext NULL COMMENT '组织名称全路径', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`RoleDataRightDetailId`) )   COMMENT = '标准角色数据权限明细';
DROP TABLE IF EXISTS `CommonRoleMenuRelations`;
CREATE TABLE `CommonRoleMenuRelations` ( `RoleMenuRelationId` char(36) NOT NULL COMMENT '主键', `RoleId` char(36) NULL COMMENT '角色Id', `MenuId` char(36) NULL COMMENT '菜单Id', `DataRights` varchar(100) NULL COMMENT '数据权限', PRIMARY KEY (`RoleMenuRelationId`) )   COMMENT = '角色菜单关系';
DROP TABLE IF EXISTS `CommonRoleMenuActionRelations`;
CREATE TABLE `CommonRoleMenuActionRelations` ( `RoleMenuActionRelationId` char(36) NOT NULL, `RoleId` char(36) NULL, `MenuId` char(36) NULL, `ActionCode` varchar(50) NULL, PRIMARY KEY (`RoleMenuActionRelationId`) )   COMMENT = '角色菜单功能关系';
DROP TABLE IF EXISTS `TagCommonRoleRelations`;
CREATE TABLE `TagCommonRoleRelations` ( `TagCommonRelationId` char(36) NOT NULL COMMENT '主键', `TagId` char(36) NULL COMMENT '标签Id', `RoleId` char(36) NULL COMMENT '角色Id', PRIMARY KEY (`TagCommonRelationId`) )   COMMENT = '标准角色标签';
DROP TABLE IF EXISTS `Matrices`;
CREATE TABLE `Matrices` ( `MatrixId` char(36) NOT NULL COMMENT '主键', `Code` varchar(100) NULL COMMENT '矩阵编码', `Name` varchar(255) NULL COMMENT '矩阵名称', `Description` longtext NULL COMMENT '描述', `TableName` varchar(50) NULL COMMENT '表名', `Status` int NULL COMMENT '状态（-1草稿，0无效，1已发布）', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', `Type` int NULL, PRIMARY KEY (`MatrixId`) )   COMMENT = '矩阵';
DROP TABLE IF EXISTS `MatrixDimensionRelations`;
CREATE TABLE `MatrixDimensionRelations` ( `MatrixDimensionRelationId` char(36) NOT NULL COMMENT '主键', `MatrixId` char(36) NULL COMMENT '矩阵Id', `DimensionId` char(36) NULL COMMENT '维度Id', PRIMARY KEY (`MatrixDimensionRelationId`) )   COMMENT = '矩阵维度关系';
DROP TABLE IF EXISTS `MatrixRoleRelations`;
CREATE TABLE `MatrixRoleRelations` ( `MatrixRoleRelationId` char(36) NOT NULL COMMENT '主键', `MatrixId` char(36) NULL COMMENT '矩阵Id', `RoleId` char(36) NULL COMMENT '角色Id', PRIMARY KEY (`MatrixRoleRelationId`) )   COMMENT = '矩阵角色关系';
DROP TABLE IF EXISTS `Dimensions`;
CREATE TABLE `Dimensions` ( `DimensionId` char(36) NOT NULL COMMENT '主键', `Code` varchar(50) NULL COMMENT '维度编码', `Name` varchar(255) NULL COMMENT '文档名称', `Description` longtext NULL COMMENT '描述', `Type` int NULL COMMENT '数据来源', `Status` int NULL COMMENT '状态', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`DimensionId`) )   COMMENT = '矩阵维度';
DROP TABLE IF EXISTS `Tags`;
CREATE TABLE `Tags` ( `TagId` char(36) NOT NULL COMMENT '主键', `ParentId` char(36) NULL COMMENT '标签的父节点', `TagCode` varchar(50) NULL COMMENT '标签的编码', `TagName` varchar(255) NULL COMMENT '标签的名称', `Type` varchar(50) NULL COMMENT '标签分类：组织等级、业态', `TypeName` varchar(255) NULL COMMENT '标签分类：组织等级、业态', `Description` longtext NULL, `OrderIndex` int NOT NULL, `Status` int NOT NULL COMMENT '1=有效；0=无效', `CreateUserId` char(36) NULL COMMENT '创建人Id', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人Id', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`TagId`) )   COMMENT = '标签';
DROP TABLE IF EXISTS `Applications`;
CREATE TABLE `Applications` ( `ApplicationId` char(36) NOT NULL COMMENT '主键', `AppCode` varchar(50) NULL COMMENT '应用编码', `AppName` varchar(50) NULL COMMENT '应用名称', `AppType` varchar(50) NULL COMMENT '应用类型', `Url` varchar(255) NULL COMMENT '链接（接入外部系统服务地址）', `Description` varchar(255) NULL COMMENT '描述', `OrderNo` int NULL COMMENT '排序', `Status` int NULL COMMENT '状态', `CreateUserId` char(36) NULL COMMENT '创建人', `CreateDate` datetime NULL COMMENT '创建时间', `ModifyUserId` char(36) NULL COMMENT '修改人', `ModifyDate` datetime NULL COMMENT '修改时间', PRIMARY KEY (`ApplicationId`) )   COMMENT = '应用';
DROP TABLE IF EXISTS `TripartiteSystems`;
CREATE TABLE `TripartiteSystems` ( `TripartiteSystemId` char(36) NOT NULL, `SystemCode` varchar(100) NOT NULL, `Name` varchar(100) NOT NULL, `DomainType` varchar(50) NULL, `Domain` longtext NOT NULL, `SecretKey` varchar(255) NULL, `RequestMethod` varchar(255) NULL COMMENT 'get;post', `CreateResultURL` longtext NULL COMMENT 'BPM流程发起成功', `CreateResultParams` longtext NULL, `AuditURL` longtext NULL COMMENT 'BPM审批过程中（审批同意、退回发起人、发起人自己撤回），审批记录知会到业务系统', `AuditParams` longtext NULL, `ApproveCloseURL` longtext NULL COMMENT 'BPM流程审批结束', `ApproveCloseParams` longtext NULL, `Status` int NULL COMMENT '1=有效；-1=无效', `Description` longtext NULL, `CreateUserId` char(36) NULL, `CreateDate` datetime NULL, `ModifyUserId` char(36) NULL, `ModifyDate` datetime NULL, PRIMARY KEY (`TripartiteSystemId`) ) ;
DROP TABLE IF EXISTS `TagOrganizationRelations`;
CREATE TABLE `TagOrganizationRelations` ( `TagOrganizationRelationId` char(36) NOT NULL, `TagId` char(36) NULL, `OrgCode` varchar(36) NULL, PRIMARY KEY (`TagOrganizationRelationId`));
DROP TABLE IF EXISTS `Sequences`;
CREATE TABLE `Sequences` ( `SequenceId` char(36) NOT NULL COMMENT '主键', `FormType` varchar(36) NULL COMMENT '表单类型，如供应商(supplier)', `FormPrefix` varchar(50) NULL COMMENT '单据前缀：如LT', `FormNo` int NULL COMMENT '表单流水号：如999', `SequenceType` int NULL COMMENT '序列号类型：0：无更；1：日更；2：月更；3：年更', `SequenceLength` int NULL COMMENT '序列号长度：如6位，不足时自动补齐', `Description` varchar(200) NULL COMMENT '描述', `LastUpdateDate` datetime NULL COMMENT '最后一笔序列号生成时间', PRIMARY KEY (`SequenceId`) ) ;
DROP TABLE IF EXISTS `Drafts`;
CREATE TABLE `Drafts` ( `DraftId` int NOT NULL AUTO_INCREMENT COMMENT '主键', `ProcessId` char(36) NOT NULL COMMENT '流程id', `Topic` varchar(100) NULL COMMENT '主题', `Summary` varchar(50) NULL COMMENT '摘要', `Data` longtext NULL COMMENT '草稿的Json数据', `UserId` char(36) NULL COMMENT '发起人id', `UserName` varchar(50) NULL COMMENT '发起人名称', `HaveAttachments` tinyint NULL COMMENT '是否有附件', `OwnerUserId` char(36) NULL COMMENT '填写人', `OwnerUserName` varchar(50) NULL COMMENT '填写人名称', `AddTime` datetime NULL COMMENT '添加时间', `UpdateTime` datetime NULL COMMENT '更新时间', PRIMARY KEY (`DraftId`) )  COMMENT = '流程实例草稿';
DROP TABLE IF EXISTS `ApprovalComments`;
CREATE TABLE `ApprovalComments` ( `ApprovalCommentId` char(36) NOT NULL COMMENT '主键', `Title` varchar(50) NULL, `Content` varchar(255) NULL COMMENT '内容', `UserId` char(36) NULL COMMENT '所属用户Id', `UserName` varchar(50) NULL COMMENT '所属用户姓名', `CreateDate` datetime NULL COMMENT '创建时间', `OrderIndex` int NULL COMMENT '是否无效', PRIMARY KEY (`ApprovalCommentId`));

/*初始化数据*/
INSERT INTO `Users`(`UserId`, `UserLoginId`, `UserName`, `UserAlias`, `Password`, `UserType`, `SourceType`, `Source`, `FirstName`, `MiddleName`, `LastName`, `PinyinFirstWord`, `Gender`, `Email`, `Emailbake`, `BirthDay`, `Status`, `MobilePhone`, `Extension`, `Company`, `EmployeeType1`, `CostCenterDesc`, `Remark`, `CreateUserId`, `CreateDate`, `MidifyUserId`, `ModifyDate`, `LastestLoginDate`, `F1`, `F2`, `F3`, `F4`, `F5`, `F6`, `SubCompanyId`, `PasswordLockTime`, `NoLoginLockTime`, `ApproveDate`, `LockStatus`, `InstanceId`, `WorkNumber`, `IsStoreRequest`, `SortCode`, `UpperUserId`, `Grade`, `JobTitle`, `CompanyId`, `OrganizationId`, `FullPathText`, `FullPathCode`, `CompanyName`, `OrganizationName`) VALUES ('********-17E8-4D9A-8AB3-E055C366DC62', 'admin', N'管理员', 'admin', 'fCIvspJ9goryL1khNOiTJIBjfA0=', 'E', 'B', N'', '', '', '', '', 'M', '<EMAIL>', '<EMAIL>', NULL, 1, '18506258811', '', NULL, N'E', N'', N'', '********-17E8-4D9A-8AB3-E055C366DC62', '2017-03-21 10:25:33.943', '********-17E8-4D9A-8AB3-E055C366DC62', '2014-07-25 16:26:57.677', '2018-09-05 10:43:21.000', N'', N'', N'', N'', N'', N'', 950, NULL, NULL, NULL, 0, '', '2345', NULL, NULL, NULL, NULL, N'部门经理', 1, 1, N'测试公司_测试部门', '51BDBAD5-88E6-4474-BA70-D88EB00B2434_D9B5A723-A0B3-4473-95BE-971C9A6C3C47', N'测试公司', N'测试部门');
INSERT INTO `Users`(`UserId`, `UserLoginId`, `UserName`, `UserAlias`, `Password`, `UserType`, `SourceType`, `Source`, `FirstName`, `MiddleName`, `LastName`, `PinyinFirstWord`, `Gender`, `Email`, `Emailbake`, `BirthDay`, `Status`, `MobilePhone`, `Extension`, `Company`, `EmployeeType1`, `CostCenterDesc`, `Remark`, `CreateUserId`, `CreateDate`, `MidifyUserId`, `ModifyDate`, `LastestLoginDate`, `F1`, `F2`, `F3`, `F4`, `F5`, `F6`, `SubCompanyId`, `PasswordLockTime`, `NoLoginLockTime`, `ApproveDate`, `LockStatus`, `InstanceId`, `WorkNumber`, `IsStoreRequest`, `SortCode`, `UpperUserId`, `Grade`, `JobTitle`, `CompanyId`, `OrganizationId`, `FullPathText`, `FullPathCode`, `CompanyName`, `OrganizationName`) VALUES ('78C35188-FCED-4E06-899F-00B84B499D8A', 'eric.cheng', N'程国强', NULL, 'fCIvspJ9goryL1khNOiTJIBjfA0=', 'E', NULL, NULL, NULL, NULL, NULL, NULL, 'M', '<EMAIL>', NULL, NULL, 1, '18506258811', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SZ1005', NULL, NULL, NULL, NULL, N'部门经理', NULL, NULL, N'测试公司_测试部门', '51BDBAD5-88E6-4474-BA70-D88EB00B2434_D9B5A723-A0B3-4473-95BE-971C9A6C3C47', N'测试公司', N'测试部门');
INSERT INTO `CommonRoles`(`RoleId`, `RoleCode`, `RoleType`, `RoleName`, `Description`, `Status`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('DF5E0733-0C24-4C77-9C69-6DDBDC0A6765', 'superadmin', '1', N'管理员', N'管理员', 1, '********-17E8-4D9A-8AB3-E055C366DC62', '2020-02-11 21:59:14.613', '********-17E8-4D9A-8AB3-E055C366DC62', '2020-02-11 23:10:24.180');
INSERT INTO `CommonRoleUserRelations`(`RoleUserRelationId`, `RoleId`, `OrgCode`, `UserId`, `UserName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('C0BC407C-E91B-427A-8605-F7125FBCEDFD', 'DF5E0733-0C24-4C77-9C69-6DDBDC0A6765', NULL, '********-17E8-4D9A-8AB3-E055C366DC62', NULL, '********-17E8-4D9A-8AB3-E055C366DC62', '2020-05-11 09:26:06.527', NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('7E7E7017-878D-4B69-9823-03398A257AC8', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('2F02B81C-3B37-4F76-A3F8-03AC5D81799B', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '0', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('DC0C1DE4-3D2A-4BBF-97A2-066E59EF9F6F', 'EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', 'M', 3, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('E1D11C8A-E65A-4C32-9746-092975234FE6', '3AF7A7CB-038A-43AC-8355-F816D9842D38', '', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('90550F12-0E7E-42BE-B337-0D16AD53B4F8', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 6, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('36F88F01-31B8-4298-83E7-199132D3AD54', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('074B78B5-FFF9-4DBD-BC54-1A36D11886F0', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 7, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('E74E3EB7-81FE-42A2-B1A9-2438933DFD65', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('76A659F8-953F-4772-AAA2-24494AF3CC3D', 'C68424D6-C305-439A-A95A-3308163F8249', 'SearHR', 'M', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('D69022EA-19C7-462F-B734-24547264E2E0', '76A659F8-953F-4772-AAA2-24494AF3CC3D', 'SearHR', 'M', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('9FA103D2-7D34-4D24-BD28-293707C6D2A3', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('06CC11DD-B167-4617-A236-2E62AA76EA3D', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('8EC42A85-FEBD-4E31-B31F-3162C893D80F', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 11, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('BC733AC8-CFB2-4A52-B14F-325B9245D3EB', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('8169D475-C314-4608-999A-380CB12DBD52', 'C68424D6-C305-439A-A95A-3308163F8249', 'SearHR', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('39C79F2C-23E0-476D-AFAD-3926825D75E9', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 3, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('EDE658D2-9BB8-4DEE-977A-3DF9B809459C', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('A662CC1C-B11E-4F60-93DA-3E233C812017', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('716909B6-5C2F-4D99-902A-418A01DDA3A4', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('EA346FA6-A3E8-40DB-AD80-44C307BEEB93', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('0BAC816E-1F7E-40F4-A52D-45AD1BD95520', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('B9600A6E-7BCE-4E21-8847-463F3DE5630E', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 6, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('8CD91884-99C1-4C8A-804C-47706DF4D5E0', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('D576B728-C6F9-4B31-974B-48845FEB7A08', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('598250C1-9660-4F37-8A80-498E53274D50', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('07FA5C19-E6F4-417D-AF44-49A23ADC047E', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('BA9A7501-4C5E-4479-9420-4A105EF32A1B', '8169D475-C314-4608-999A-380CB12DBD52', 'HR', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('554A8F8D-9A26-46DC-8D55-4E204D153E3C', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('AA4F489E-AC04-4DF8-81BD-4F55C16AB08A', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('E3EF14D5-DBAE-4511-8F0D-530E034BD484', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('AFF2C0FC-D19B-48B5-8CA8-54A881472A74', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 0, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('352999C3-F6CA-43FF-9C0D-5CCF331CB45A', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('D56E4281-C515-4279-B0D0-64051C2CC160', '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('9A36430A-29D6-4A55-8614-68DEC1B5F77C', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('DBE944EB-46F8-4B1C-B0C0-6C0FB29D9F45', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('99C0DFA2-B13A-4AAB-BA1A-6C780D4100C6', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('6101692B-2AC2-40E7-B89C-6D8B0D0DA72F', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 8, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('32D2F95C-F32E-477F-9465-714007679B74', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('8D9720E2-A342-4C1A-A785-794F6EC8E398', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('97A75EBF-CAD7-4BE8-8494-7D0D3DBF527B', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 12, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('FB7E6186-407E-440B-AE56-7D6D416F9242', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('7940BD39-5868-4944-8CDB-7D8E305DE8AC', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('7D084C4C-7701-49DC-A260-7ED72FE50038', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('07C03644-FF61-49E4-B3AB-82F89B344F13', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('35F102C9-C959-468B-8C0F-83BCA83E78AE', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('FAB88C3A-BBDA-4F63-8A1B-8AA064CD1563', '3DB82C0E-4DB3-4042-8866-1FE07CF1B24C', 'platform', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', 'M', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('DD2530DD-DCCD-4962-A722-9912E99466B7', '8169D475-C314-4608-999A-380CB12DBD52', 'HR', 'M', 0, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('3F500497-8864-4DF9-B63D-991D74464830', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', -1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('CEF77C99-C116-4892-AA04-993BFB42B67D', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('D7659DFA-6A1B-41D5-91F3-995D9D181365', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 7, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('9C9AF639-BED1-46FC-A6E8-9B88BCB6E5B3', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('63C4302E-BDC6-443B-AC72-9DC602CA3452', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('092CDAD4-B6A6-4FD3-9BFE-9F89B798164C', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('DCCA2676-8912-41F8-BC0F-A03FB9480256', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('23938B57-BA3B-426B-888A-A857CAF9785C', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 6, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('82626B0F-D436-4799-ABF5-AE968383EA85', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('BEAA4AD1-31FC-4BEB-8C03-B42D6084A97C', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('3D1820CF-B900-49A2-983B-BA46CD064EE5', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('2973735E-AE52-4D99-9C73-BA6452E7652D', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', 'M', 2, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('A56EC975-F98C-4271-A77A-BF6CF73F1BD8', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('177E7D55-1C10-4266-9466-BFA652E5D84F', '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', 'platform', 'P', 3, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('C9D81832-BA20-42E9-B85C-C057A71E07FC', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 12, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('50126FFF-813F-40BC-B872-C39886999FD5', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 3, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('20E76C1D-9EE1-4D60-ABA9-C62B20D0E47C', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 11, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('598E1FF9-C622-4FBF-9DE8-C701D1BFA37F', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('32F564B4-8E4D-4FA4-BDCA-C813670ABBBE', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('EC748F3D-339B-406C-BC44-C8CEC80039F9', '90550F12-0E7E-42BE-B337-0D16AD53B4F8', 'platform', 'P', NULL, NULL, NULL, '0', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('F8C98398-FA5F-49A0-A364-C9FE7E2F948C', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('232FE2FC-E919-48FC-A83B-CC7D88D3143B', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('9C5CFF6C-B264-4A2B-B442-D321C97E3A6A', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('001A1C91-5FC4-4682-83F0-D6B5336E5E4B', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 10, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('442070C9-43DE-46DD-9830-DC9C7E778F8F', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('E418D5F9-1327-48E9-88AB-DF66595D7BAF', '49E23799-3147-43B7-B5D9-ED179E659275', 'platform', 'P', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('806B13FB-B241-41C0-8A16-E16728B29118', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 13, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('975DABBB-E2AA-47D9-8B93-E30C17049281', '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 8, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('F06EBB4D-A288-4289-B4EC-E6BB9DBDC1E2', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', 6, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('664E9B28-ED2B-4B16-958F-E950EC102536', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('E0BE4248-A0D1-4C8F-A185-E95774198907', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('49E23799-3147-43B7-B5D9-ED179E659275', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('901874E1-3867-4B06-832E-ED772758E9C4', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('0AAB450E-C442-4719-BAC0-F10409110918', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', 1, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('89269868-9E00-43F8-8493-F18A450C49E9', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 3, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('D05AFCCA-7425-43A2-AF9A-F3384E9206F2', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('6DAAA5D9-D3AD-4456-88F4-F3A2631A8833', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 3, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('020A9025-C7F7-46EF-8030-F40F14DB6D72', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('435851EA-72D0-4FB9-B391-F74907772790', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 2, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('3AF7A7CB-038A-43AC-8355-F816D9842D38', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 2, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'DC0C1DE4-3D2A-4BBF-97A2-066E59EF9F6F', 'platform', 'M', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO `menus`(`MenuId`, `ParentId`, `AppCode`, `PageType`, `OrderNo`, `DataRights`, `OpenType`, `InUse`, `MODULERIGHTS`, `MODIFYCOLUMNLASTOFINDEX`, `DELETECOLUMNLASTOFINDEX`, `F1`, `F2`, `F3`) VALUES ('0D8341FA-F696-4C94-A85C-FF2030D214B5', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('C712CE5A-2C64-4AE2-8EE9-00C5A651022C', N'流程测试', 'Process Test', NULL, 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', NULL, 'process-test', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('24D10151-1FC6-4ED4-9B4B-00FCF9BD90D8', N'流程编辑', 'edit', NULL, '2973735E-AE52-4D99-9C73-BA6452E7652D', '/bpm/process-management/process-list/edit/edit.module.ts', 'process-list/edit', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('0A042517-7977-47BA-98B2-02968DA61684', N'日志列表', 'Logs', NULL, 'EC748F3D-339B-406C-BC44-C8CEC80039F9', '/platform/log-management/log/log.module.ts', 'log', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('89FCE1A1-F9A0-44D3-8371-04382726D0E6', N'用户详情', 'User Detail', NULL, '554A8F8D-9A26-46DC-8D55-4E204D153E3C', '/platform/user-management/user/query/query.module.ts', 'user/query', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('065FCFB9-A080-417E-AFD9-064E8394E5A9', N'日志中心', 'Log Management', NULL, '90550F12-0E7E-42BE-B337-0D16AD53B4F8', NULL, 'log-management', '0', 'file', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('D657FF15-E2E8-4020-9ACE-08775B61CD5E', N'测试1', 'test1', NULL, '76A659F8-953F-4772-AAA2-24494AF3CC3D', NULL, 'test1', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('8D2904E5-D613-4B72-BA4F-0896EC7BF131', N'表单列表', 'Form List', NULL, '07FA5C19-E6F4-417D-AF44-49A23ADC047E', '/bpm/form-management/form/form.module.ts', 'form', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('D390DA33-B64E-44C6-A1AE-0B46E0ED0A6B', N'人员中心', 'Home', NULL, 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', NULL, 'home', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('C861175E-B72C-4A84-8592-1111525BC4BD', N'标准角色', 'Role Management', NULL, '3AF7A7CB-038A-43AC-8355-F816D9842D38', NULL, 'common-roles', '0', 'user', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('3FB3B7AE-94BD-43D0-940D-12ED5FBB7471', N'角色库', 'Role', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A', '/platform/user-management/role/index/index.module.ts', 'role', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('68EB8BAF-800D-451B-A0A0-13C066422F51', N'前台菜单管理', 'Front Menu Message', NULL, 'B239A85C-5A96-47D8-A8F1-0EFE0010BFE0', '/platform/system-management/front-menu/front-menu.module.ts', 'front-menu', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('1A5CFE11-E39F-4B96-BC66-1AC35977F820', N'测试模型', 'Test Model', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8', '/automator/process-test-model/index/index.module.ts', 'test-model', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('F1D78727-533A-4986-A073-1E5B62E9DD84', N'业务事项流程图', 'Process List', NULL, 'E1B3660A-EE2E-42A8-B320-C86B8FBFF4D4', '/bpm/process-management/process-list/index/index.module.ts', 'process-list', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('11BAD300-DB1F-4C0C-A953-1E963DE2E751', N'测试任务', 'Test Task', NULL, '32F564B4-8E4D-4FA4-BDCA-C813670ABBBE', '/automator/process-test-result/index/index.module.ts', 'test-result', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('B30B428A-12F6-43DA-A725-20952D6296B7', N'部门管理', 'Department', NULL, '435851EA-72D0-4FB9-B391-F74907772790', '/platform/organization-management/department/department.module.ts', 'department', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('F6A428AA-148F-4C93-A61F-21F81FF7D112', N'外部业务对象（待实现）', 'External Business', NULL, '177E7D55-1C10-4266-9466-BFA652E5D84F', '1', 'external-business', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('0469B2F4-CC2F-41CC-B936-22EF84FCD99C', N'业务管理', 'Business Management', NULL, '32D2F95C-F32E-477F-9465-714007679B74', NULL, 'business-management', '0', 'sync', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('33F40454-1D62-48CF-A055-310E3AE4EA66', N'流程查询（待实现）', 'Process', NULL, '020A9025-C7F7-46EF-8030-F40F14DB6D72', '1', 'process', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('355A0A49-4DE1-4711-8E4D-343F0A19F96B', N'用户详情', 'User Detail', NULL, '9C9AF639-BED1-46FC-A6E8-9B88BCB6E5B3', '/platform/user-management/user/query/query.module.ts', 'user/query', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('BFB3A428-F9A2-4981-99E4-************', N'创建测试模型', 'Create Model', NULL, '2F02B81C-3B37-4F76-A3F8-03AC5D81799B', '/automator/process-test-model/create-model/create-model.module.ts', 'test-model/create', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('2F6F3219-5CA9-4A55-995F-3653997CC17E', N'数据库对象（待实现）', 'Database Management', NULL, 'E3EF14D5-DBAE-4511-8F0D-530E034BD484', '1', 'database', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('F39E7E3D-05AF-4309-8262-384D3B80D87F', N'权限管理（待实现）', 'Rights Management', NULL, 'AFF2C0FC-D19B-48B5-8CA8-54A881472A74', '/platform/system-management/role/index/index.module.ts', 'rights', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('87E61F5B-8755-4022-B74C-3B70D5D1C86A', N'数据行安全性', 'Data Security', NULL, 'E1D11C8A-E65A-4C32-9746-092975234FE6', '/platform/user-management/data-security/data-security.module.ts', 'data-security', '1', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('2D741187-316B-4287-855F-403E54779E94', N'初始化配置（待实现）', 'Init Management', NULL, '23938B57-BA3B-426B-888A-A857CAF9785C', '1', 'init', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('8D05E41F-F4D1-4C11-8234-4076E9EEC7EF', N'矩阵角色（待实现）', 'Matrix Role Management', NULL, 'E74E3EB7-81FE-42A2-B1A9-2438933DFD65', '1', 'matrix-role', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('EDA9B0DE-AD67-4558-963F-4A400F997265', N'矩阵配置人员', 'Matrix User Setting', NULL, '598250C1-9660-4F37-8A80-498E53274D50', '/platform/user-management/matrix/user-setting/user-setting.module.ts', 'matrix/user-setting', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('EE19E9DE-7A47-420C-B7EA-4AB2B45A93EF', N'模板添加', 'Templete Add', NULL, '9FA103D2-7D34-4D24-BD28-293707C6D2A3', '/platform/message-management/templete/add/add.module.ts', 'templete/add', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('F6C84E6E-C382-496B-AC44-5558DB681CA2', N'矩阵编辑', 'Matrix Edit', NULL, 'F06EBB4D-A288-4289-B4EC-E6BB9DBDC1E2', '/platform/user-management/matrix/edit/edit.module.ts', 'matrix/edit', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('C47E425A-FD82-4FAE-A8EB-558806E50B6F', N'运维管理', 'Maintain Management', NULL, '3D1820CF-B900-49A2-983B-BA46CD064EE5', NULL, 'maintain-management', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('12F5585D-A609-401E-80BF-567152569BD3', N'消息规则', 'Message Rule', NULL, '8CD91884-99C1-4C8A-804C-47706DF4D5E0', '/platform/message-management/rule/index/index.module.ts', 'rule', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('3638E657-675D-4C11-8840-58CDF00B1555', N'应用管理（新）', 'App Management', NULL, '975DABBB-E2AA-47D9-8B93-E30C17049281', '/platform/system-management/application/application.module.ts', 'application', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('24C8A24F-3E9E-473F-93A9-5A9CF20B20A0', N'流程管理', 'One Stop Service', NULL, '442070C9-43DE-46DD-9830-DC9C7E778F8F', '/bpm/one-stop-management/index/index.module.ts', 'one-stop-service', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('0648C465-160A-45EF-92FB-5B2BFEF8EA21', N'流程管理', 'Process', NULL, 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', NULL, 'process', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('E17E1696-525A-4E69-9291-5B3F8D2027A5', N'业务事项分类', 'Business Category', NULL, '26631364-A83B-4FBC-BAA6-DC6ABFB716E8', '/bpm/process-management/process-category/index/index-process-category.module.ts', 'process-category', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('EBA1FB4B-1D43-429F-ACD8-5E8FF5E9B540', N'前台角色成员', 'front-role/member', NULL, '35D2BCA4-DF31-438D-9DC8-0F7177E8032A', '/platform/user-management/front-role/member/member.module.ts', 'front-role/member', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('DEA547BC-A045-4D37-9987-5FE63C7BF7AE', N'表单模型编辑', 'edit', NULL, '99C0DFA2-B13A-4AAB-BA1A-6C780D4100C6', '/bpm/form-management/model/form-edit/form-edit.module.ts', 'model/form-edit', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('F797B5A3-0B6A-45F1-9128-689A522FE5B1', N'业务事项列表', 'business-matters', NULL, 'B31A9167-8116-4C70-AAF1-BB2A03E0FF29', '/bpm/process-management/business-matters/business-matters.module.ts', 'business-matters', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('78068484-B28A-49EF-AF40-6A53FAD115A5', N'角色匹配人员', 'Common Role Users', NULL, '20E76C1D-9EE1-4D60-ABA9-C62B20D0E47C', '/platform/user-management/role/member/member.module.ts', 'role/member', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('A7795768-9B30-4791-945E-72ED74398DE7', N'标签列表', 'Tag List', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF', '/platform/tag-management/tag/tag.module.ts', 'tag', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('7E80638E-F1D8-498C-BD0E-742AE7F7B137', N'矩阵管理', 'Matrix', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13', '/platform/user-management/matrix/index/index.module.ts', 'matrix', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('8975A57D-CA00-4092-BAFF-7610627D9B6F', N'流程分类', 'Process Category', NULL, '901874E1-3867-4B06-832E-ED772758E9C4', '/bpm/process-management/process-category/index/index-process-category.module.ts', 'process-category', '1', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('74FAD1A6-2ADE-4EF5-9341-7618C8501D1D', N'菜单配置', 'Menus', NULL, '6101692B-2AC2-40E7-B89C-6D8B0D0DA72F', '/platform/system-management/role/menu/menu.module.ts', 'role/menu', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('9CBC3FC5-0EE5-4DE2-8A5B-766CDC323B08', N'异常统计（待实现）', 'Abnormal Report', NULL, 'DBE944EB-46F8-4B1C-B0C0-6C0FB29D9F45', '1', 'abnormal-report', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('B8A95979-6F08-4347-9EA9-76D22F8A7304', N'矩阵管理', 'Matrix Management', NULL, '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', NULL, 'matrix-management', '0', 'switcher', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('3E7BA1D6-7864-4077-BFD6-776FA2CCFBB2', N'消息中心', 'Message Management', NULL, 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', NULL, 'message-management', '0', 'mail', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('6E051E84-EF86-41B3-995E-7B29D668FF14', N'按角色', 'Match Common Role', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F', '/platform/user-management/common-role/match-role/match-role.module.ts', 'match-common-role', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('AF04087F-9318-42E9-841B-7F5055B476C5', N'按业态（待实现）', 'Format', NULL, '97A75EBF-CAD7-4BE8-8494-7D0D3DBF527B', '1', 'format', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('655FB751-8F7F-47B0-9847-7F515B42B713', N'矩阵配置人员', 'Matrix User Setting', NULL, '806B13FB-B241-41C0-8A16-E16728B29118', '/platform/common_roles/matrix/user-setting/user-setting.module.ts', 'matrix/user-setting', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('8EAB320D-1516-42CB-AEAC-8737B5AA7FD9', N'流程列表', 'Process List', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A', '/bpm/process-management/process-list/index/index.module.ts', 'process-list', '0', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('E018A612-0856-42D6-A975-8AA5186CBDA8', N'异常流程（待实现）', 'Abnormal process', NULL, '7940BD39-5868-4944-8CDB-7D8E305DE8AC', '1', 'abnormal', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('8C684C61-5DAC-4925-B521-8B0CB0394F4B', N'业务管理', 'Business Management', NULL, '50031EBE-FEC9-41D9-B072-F050CC59119B', NULL, 'business-management', '0', 'profile', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('78D44D57-C0D4-46DE-8767-8B3A1B9E5908', N'表单管理', 'Form Management', NULL, '0AAB450E-C442-4719-BAC0-F10409110918', NULL, 'form-management', '0', 'profile', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('00F993ED-2338-4C39-BB94-8D7FCFF2552F', N'公司管理', 'Company', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398', '/platform/organization-management/company/company.module.ts', 'company', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('CAFB8D3A-5FE1-43C9-AAF6-8DAB6A70FF71', N'外部系统注册', 'External System', NULL, 'D56E4281-C515-4279-B0D0-64051C2CC160', '/platform/system-management/external-system/external-system.module.ts', 'external-system', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('C0591CD2-5DA0-4019-95CD-8E2BED365140', N'业务类型', 'Business Type', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5', '/bpm/business-management/business-type/business-type.module.ts', 'business-type', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('39AF66DC-E3D5-416D-AD2B-8E4FC7EBB668', N'流程图', 'Process Design', NULL, '7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2', '/bpm/process-design/bpmn-designer/bpmn-designer.module.ts', 'process-design', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('5C7BEC0F-AC8F-4CD6-A096-922BD2CE4EFC', N'业务事项流程图编辑', 'Bpmn Designer', NULL, '343E4B8D-77BF-4FEA-A217-32522933BB01', '/bpm/process-design/bpmn-designer/bpmn-designer.module.ts', 'bpmn-designer', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('F96C9085-6CA3-40D6-8196-************', N'自动化测试', 'Automator', NULL, 'DC0C1DE4-3D2A-4BBF-97A2-066E59EF9F6F', NULL, 'automator', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('39FCE678-759D-42BB-B78A-9F5E316D0564', N'菜单管理', 'Menu Management', NULL, '3F500497-8864-4DF9-B63D-991D74464830', '/platform/system-management/menu/menu.module.ts', 'menu', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('72D34042-A7E4-447F-9C7B-A6CC52754836', N'系统管理', 'System Management', NULL, '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', NULL, 'system-management', '0', 'setting', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('18E60BDC-E3C9-416E-B64D-A99864309F18', N'后台角色成员', 'Role Member', NULL, 'D576B728-C6F9-4B31-974B-48845FEB7A08', '/platform/user-management/role/member/member.module.ts', 'role/member', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('174BBE80-C6A0-4737-A4A1-AA701D4313C5', N'测试2', 'test2', NULL, 'D69022EA-19C7-462F-B734-24547264E2E0', NULL, 'test2', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('850B2B08-40F8-4C04-A8E5-ADF2CE21226E', N'组织结构', 'Organization Management', NULL, '9A36430A-29D6-4A55-8614-68DEC1B5F77C', NULL, 'organization-management', '0', 'team', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('83515BF1-6AA7-423E-A74D-AF36E99FD50F', N'平台管理', 'Platform', NULL, '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', NULL, 'platform', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('73D6EE01-8F88-4184-AFF3-AFE9623C215F', N'流程设置', 'Process Settings', NULL, 'FB7E6186-407E-440B-AE56-7D6D416F9242', NULL, 'process-management', '0', 'setting', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('6B436DE0-B168-4986-930D-BA58597B715B', N'岗位管理', 'Position', NULL, '50126FFF-813F-40BC-B872-C39886999FD5', '/platform/organization-management/position-level/position-level.module.ts', 'position', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('AF9D1ABC-AF92-49E0-8A83-BD5D5BC2CF1D', N'培训管理', 'Training', NULL, 'DD2530DD-DCCD-4962-A722-9912E99466B7', NULL, 'training', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('6191D326-DC0D-4E84-882C-BDC17A93F7FB', N'人员配置', 'Users', NULL, 'D7659DFA-6A1B-41D5-91F3-995D9D181365', '/platform/system-management/role/member/member.module.ts', 'role/member', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('65504D04-42A0-43CC-944D-BF10CA6DAF11', N'业务对象', 'Business Object', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017', '/bpm/business-management/business-object/business-object.module.ts', 'business-object', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('6A2E175F-BDE7-411F-B78A-C0AB60953AB2', N'表单模型', 'Form Model', NULL, '0BAC816E-1F7E-40F4-A52D-45AD1BD95520', '/bpm/form-management/model/index/index.module.ts', 'model', '1', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('3D66597D-D730-4B9A-872A-C0E7C0AD0A18', N'矩阵新增', 'Add', NULL, 'C9D81832-BA20-42E9-B85C-C057A71E07FC', '/platform/user-management/matrix/add/add.module.ts', 'matrix/add', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('3B47E81F-38CA-4486-9618-C68587A2515A', N'消息模板', 'Message Template', NULL, 'DCCA2676-8912-41F8-BC0F-A03FB9480256', '/platform/message-management/templete/index/index.module.ts', 'templete', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('7C5A0008-9DDF-4B1E-9555-C7B1E7905389', N'前台角色权限', 'front-role', NULL, '8D86D709-2A28-4FE3-8EC5-7F868195670E', '/platform/user-management/front-role/index/index.module.ts', 'front-role', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('D85ACB0F-E01A-483F-8FBB-C8AAAA882E89', N'服务器信息（待实现）', 'Server Management', NULL, 'E0BE4248-A0D1-4C8F-A185-E95774198907', '1', 'Server', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('59BC1A5E-4769-41EE-B62C-CBDD227814C1', N'业务对象明细', 'Business Object Query', NULL, '092CDAD4-B6A6-4FD3-9BFE-9F89B798164C', '/bpm/business-management/business-object/query/query.module.ts', 'business-object/query', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('22986C9F-CFD2-43CF-BAE7-CD1911C61BBE', N'系统集成', 'System Integration', NULL, '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', NULL, 'Integration', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('69A4D3FD-DC2D-446D-86C7-CDF3F4CC4474', N'矩阵新增', 'Add', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9', '/platform/user-management/matrix/add/add.module.ts', 'matrix/add', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('03F6CD30-6545-42DA-BA83-D374DB696B4C', N'标签管理', 'Tag Management', NULL, '49E23799-3147-43B7-B5D9-ED179E659275', NULL, 'tag-management', '0', 'tags', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('061002F4-093B-4729-A988-D52470FFFBB8', N'维度管理', 'Dimension', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB', '/platform/user-management/matrix/dimension/dimension.module.ts', 'dimension', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('A97F4A15-F7D2-46F2-9E17-D6AD446AB7F6', N'规则编辑', 'Rule Edit', NULL, '232FE2FC-E919-48FC-A83B-CC7D88D3143B', '/platform/message-management/rule/edit/edit.module.ts', 'rule/edit', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('AD9E7431-4775-4E93-87C8-D884F7EE2DA8', N'三级菜单测试', 'third page test', NULL, 'EA346FA6-A3E8-40DB-AD80-44C307BEEB93', NULL, 'test', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('9B55949F-2FDC-4328-AB91-DA3BC19F796D', N'代理管理（待实现）', 'Agent', NULL, '6DAAA5D9-D3AD-4456-88F4-F3A2631A8833', '1', 'agent', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('50DE04EF-A8EA-4D12-8315-DAE56CE50E19', N'表单模型查看', 'query', NULL, 'BEAA4AD1-31FC-4BEB-8C03-B42D6084A97C', '/bpm/form-management/model/query/query.module.ts', 'model/query', '1', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('30C4AC62-42FB-4560-9032-E16CEA1F23B0', N'人员管理', 'User Management', NULL, '598E1FF9-C622-4FBF-9DE8-C701D1BFA37F', '/platform/user-management/user/index/index.module.ts', 'users', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('193C46D8-CBA5-4BF8-9C1D-E3A83387E57A', N'前台角色菜单', 'front-role/menu', NULL, '67843A29-23A5-41BE-A60E-D0A26D5602B9', '/platform/user-management/front-role/menu/menu.module.ts', 'front-role/menu', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('52CCF575-0F27-46F7-A05F-E596EA8FB959', N'按组织', 'Common Role', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B', '/platform/user-management/common-role/role-user/common-role-setting.module.ts', 'common-role-user', '0', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('4DA3CD9D-D6BF-4915-9E75-E67273DCD9E8', N'模板编辑', 'Templete Edit', NULL, 'F8C98398-FA5F-49A0-A364-C9FE7E2F948C', '/platform/message-management/templete/edit/edit.module.ts', 'templete/edit', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('21DD1A8D-A61C-40C0-89CB-EE7D5FD9810A', N'首页', 'HR', NULL, '8169D475-C314-4608-999A-380CB12DBD52', NULL, 'business-test1', '0', NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('597D62EF-4D81-4588-9D51-F3E7BF6B3230', N'数据字典', 'Dictionary', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8', '/platform/system-management/dictionary/dictionary.module.ts', 'dictionary', '0', 'book', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('4E56C480-00A0-48FF-A508-F42C34349F60', N'表单模型添加', 'add', NULL, '82626B0F-D436-4799-ABF5-AE968383EA85', '/bpm/form-management/model/edit/edit.module.ts', 'model/edit', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('0E51C9FB-6737-4E82-AA1D-F4C8B9BBE512', N'规则添加', 'Rule Add', NULL, 'D05AFCCA-7425-43A2-AF9A-F3384E9206F2', '/platform/message-management/rule/add/add.module.ts', 'rule/add', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('48E4B6BB-F119-4BC9-9865-F64D2CE9CADA', N'模型管理', NULL, NULL, 'FAB88C3A-BBDA-4F63-8A1B-8AA064CD1563', NULL, 'form', NULL, NULL, NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('165E5E02-2453-4C7F-87C9-F9B991999291', N'后台角色菜单', 'Role Menu', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A', '/platform/user-management/role/menu/menu.module.ts', 'role/menu', '1', 'stop', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('A0C16F94-C9E9-4886-8300-FB9C484C54C8', N'业务对象编辑', 'Business Object Detail', NULL, '89269868-9E00-43F8-8493-F18A450C49E9', '/bpm/business-management/business-object/detail/detail.module.ts', 'business-object/detail', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('5333C10C-5845-4E44-A395-FC72FDCC1581', N'任务详情', 'Task Detail', NULL, '36F88F01-31B8-4298-83E7-199132D3AD54', '/automator/process-test-result/detail/detail.module.ts', 'test-result/detail', '1', 'folder', NULL, NULL);
INSERT INTO `MenuDetails`(`MenuDetailId`, `MenuName`, `MenuEnglishName`, `LanguageCode`, `MenuId`, `FileURL`, `FunctionURL`, `Hidden`, `IconURL1`, `IconURL2`, `IconURL3`) VALUES ('4440FD57-929E-4242-B9DF-FD6C0F5B03B7', N'工作交接（待实现）', 'Hand Over', NULL, '7D084C4C-7701-49DC-A260-7ED72FE50038', '1', 'handover', '0', 'book', NULL, NULL);
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('export', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('user', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('user', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, 'D576B728-C6F9-4B31-974B-48845FEB7A08');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, 'D576B728-C6F9-4B31-974B-48845FEB7A08');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('export', NULL, '32F564B4-8E4D-4FA4-BDCA-C813670ABBBE');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '2F02B81C-3B37-4F76-A3F8-03AC5D81799B');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '2F02B81C-3B37-4F76-A3F8-03AC5D81799B');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '82626B0F-D436-4799-ABF5-AE968383EA85');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '99C0DFA2-B13A-4AAB-BA1A-6C780D4100C6');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '2973735E-AE52-4D99-9C73-BA6452E7652D');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '0BAC816E-1F7E-40F4-A52D-45AD1BD95520');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '975DABBB-E2AA-47D9-8B93-E30C17049281');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '975DABBB-E2AA-47D9-8B93-E30C17049281');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('user', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('iam', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('user', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('delete', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('user', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('add', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('save', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9');
INSERT INTO `MenuActionRelations`(`Code`, `Name`, `MenuId`) VALUES ('edit', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9');
INSERT INTO `Companies`(`CompanyId`, `CompanyCode`, `ComapnyName`, `CompanyNameEn`, `City`, `BusinessType`, `Address`, `ContactUser1`, `ContactUser2`, `PhoneNumber1`, `PhoneNumber2`, `Fax`, `Status`, `Remark`, `CompanyManager`, `CompanyType`, `Domain`, `UpperId`, `Level`, `SortCode`, `ShowInMDS`, `ProcessLevel`, `ProcessType`, `FullPathText`, `FullPathCode`, `F1`, `F2`, `F3`, `F4`, `F5`) VALUES (1, '10010001', N'测试公司', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, NULL, 4, NULL, NULL, 0, 1, '0', NULL, NULL, N'测试公司', '51BDBAD5-88E6-4474-BA70-D88EB00B2434', N'51BDBAD5-88E6-4474-BA70-D88EB00B2434', NULL, NULL, NULL, NULL);
INSERT INTO `Organizations`(`OrganizationId`, `Name`, `Name2`, `DeptCode`, `CompanyId`, `UpperId`, `DeptManager`, `DeptTelephone`, `Level`, `Status`, `Remark`, `CityId`, `IndependentRoll`, `SortCode`, `ProcessLevel`, `ProcessType`, `FullPathText`, `FullPathCode`, `F1`, `F2`, `F3`) VALUES (1, N'测试部门', NULL, N'10010002', 1, NULL, NULL, NULL, 0, 1, NULL, NULL, '0', 1, NULL, NULL, N'测试公司_测试部门', '51BDBAD5-88E6-4474-BA70-D88EB00B2434_D9B5A723-A0B3-4473-95BE-971C9A6C3C47', N'D9B5A723-A0B3-4473-95BE-971C9A6C3C47', NULL, NULL);
INSERT INTO `Positions`(`PositionId`, `OrganizationId`, `JobTitleId`, `OfficeId`, `PositionCode`, `Name`, `Description`, `UpperId`, `IsActive`, `CompanyId`, `Type`, `F1`, `F2`, `F3`, `F4`, `F5`) VALUES ('C2B1B3F2-0AF3-4C1E-B3F7-0004A5ED77B4', 1, 'F78F0793-D062-48C2-8732-A70105BE8D93', NULL, N'10010003', '部门经理', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `UserPositionRelations`(`UserPositionRelationId`, `PositionId`, `UserId`, `PrimaryPosition`, `StartDate`, `EndDate`, `IsActive`, `F1`, `F2`, `F3`) VALUES ('13E3B799-9259-4B6D-A42E-753744F91B20', 'C2B1B3F2-0AF3-4C1E-B3F7-0004A5ED77B4', '********-17E8-4D9A-8AB3-E055C366DC62', '1', '2017-12-14 00:10:33.630', '2517-12-14 00:10:33.630', '0', NULL, NULL, NULL);
INSERT INTO `JobTitles`(`JobTitleId`, `JobNo`, `Name`, `JobNameEn`, `JobLevel`, `JobCode`, `JobCategory`, `Description`, `Status`, `F1`, `F2`, `F3`) VALUES ('F78F0793-D062-48C2-8732-A70105BE8D93', '6', N'测试岗位', NULL, 1, NULL, 'D', NULL, NULL, NULL, NULL, N'1');
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('78A295E2-B393-47BA-8546-25AE1DA5433F', N'reject', N'退回', 'checked', 2, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('1B4D5D64-193A-4561-9756-274AEA904338', N'discuss', N'沟通', '', 6, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('7B2DD223-CFA5-454A-B63E-2791C60DC25E', N'notice', N'通知', 'checked', 3, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('1C6BEF5A-B3B6-4354-8054-3E125034A4F4', N'approve', N'同意', 'checked', 1, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL,NULL, NULL);
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('09363866-0C39-4557-869D-6BDC40971617', N'cancel', N'作废', '', 7, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('3CD8836D-D150-4F90-BD7D-8A5095DDB289', N'bpm-instance-actions', N'BPM实例功能', 'bpm-instance-actions', 1, 1, N'按钮支持默认选中：checked', NULL, NULL, NULL, '********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('66B91A7F-7C66-4162-A335-A2B051C8DC35', N'counter-sign', N'加签', '', 4, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO `Dictionaries` (`DictionaryId`, `Code`, `Name`, `Value`, `OrderNum`, `Status`, `Remark`, `UpperId`, `TypeCode`, `TypeName`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('80E7F0F3-17BB-4020-B53E-FE9F858F9E9B', N'handover', N'交办', '', 5, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO `Sequences`(`SequenceId`, `FormType`, `FormPrefix`, `FormNo`, `SequenceType`, `SequenceLength`, `Description`, `LastUpdateDate`) VALUES ('C85DE4DC-0485-48AE-84E7-80F044E9CC2D', 'process', NULL, 3, 1, 6, N'流程编号', NULL);
INSERT INTO `Applications` (`ApplicationId`, `AppCode`, `AppName`, `AppType`, `Url`, `Description`, `OrderNo`, `Status`, `CreateUserId`, `CreateDate`, `ModifyUserId`, `ModifyDate`) VALUES ('EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', N'后台管理', 'S', NULL, N'后台管理的描述', 0, 1, '********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);


ALTER TABLE `Companies` 
MODIFY COLUMN `CompanyId` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST;
ALTER TABLE `Organizations` 
MODIFY COLUMN `OrganizationId` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST;
set FOREIGN_KEY_CHECKS=1;