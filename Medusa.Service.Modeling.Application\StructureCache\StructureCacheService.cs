using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Application.StructureCache.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.Cache;
using MT.Enterprise.Utils.Extensions;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.StructureCache
{
    
    
    
    public class StructureCacheService : ServiceBase, IStructureCacheService
    {
        #region 
        readonly MyDbContext _dbContext;
        private readonly IRedis _redis;

        
        
        
        
        
        public StructureCacheService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _redis = serviceProvider.GetService<IRedis>();
        }

        #endregion

        
        
        
        
        public void SetRedis(StructureCacheSetDto dto)
        {
            if (dto.BusinessObjectType == 1)
            {
                _redis.Client.HashSet("structure:businessObject", dto.BusinessObjectId.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(dto.BusinessObjectId, dto.BusinessObjectType)));

                
                var co1 = _dbContext.Modeling.Queryable<CompositeObject>().Where(a => a.BusinessObjectId == dto.BusinessObjectId).Select(a => a.Id).ToList();
                var co2 = _dbContext.Modeling.Queryable<CompositeObjectRelation>().Where(a => a.BusinessObjectId == dto.BusinessObjectId && a.ObjectType == "businessObject").Select(a => a.CompositeObjectId).ToList();
                var co = co1.Union(co2).ToList();
                co.ForEach(c =>
                {
                    _redis.Client.HashSet("structure:compositeObject", c.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(c, 2)));
                });
            }
            else if (dto.BusinessObjectType == 2)
            {
                _redis.Client.HashSet("structure:compositeObject", dto.BusinessObjectId.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(dto.BusinessObjectId, dto.BusinessObjectType)));
            }
            else if (dto.BusinessObjectType == 3)
            {
                _redis.Client.HashSet("structure:view", dto.BusinessObjectId.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(dto.BusinessObjectId, dto.BusinessObjectType)));

                
                var co = _dbContext.Modeling.Queryable<CompositeObjectRelation>().Where(a => a.BusinessObjectId == dto.BusinessObjectId && a.ObjectType == "view").Select(a => a.CompositeObjectId).ToList();
                co.ForEach(c =>
                {
                    _redis.Client.HashSet("structure:compositeObject", c.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(c, 2)));
                });
            }
        }

        
        
        
        
        public void SetAllRedis(StructureCacheSetDto dto)
        {
            if (dto.BusinessObjectType == 1)
            {
                _dbContext.Modeling.Queryable<BusinessObject>()
                    .Where(a => a.State == 1)
                    .WhereIF(!string.IsNullOrEmpty(dto.ApplicationId), a => SqlFunc.IsNullOrEmpty(a.ApplicationId) || a.ApplicationId.Contains(dto.ApplicationId))
                    .ToList().ForEach(f =>
                {
                    _redis.Client.HashSet("structure:businessObject", f.Id.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(f.Id, dto.BusinessObjectType)));
                });
            }
            else if (dto.BusinessObjectType == 2)
            {
                _dbContext.Modeling.Queryable<CompositeObject>()
                    .WhereIF(!string.IsNullOrEmpty(dto.ApplicationId), a => a.ApplicationId == dto.ApplicationId)
                    .ToList().ForEach(f =>
                {
                    _redis.Client.HashSet("structure:compositeObject", f.Id.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(f.Id, dto.BusinessObjectType)));
                });
            }
            else if (dto.BusinessObjectType == 3)
            {
                _dbContext.Modeling.Queryable<View>()
                    .Where(a => a.State == 2)
                    .WhereIF(!string.IsNullOrEmpty(dto.ApplicationId), a => SqlFunc.IsNullOrEmpty(a.AppId) || a.AppId == SqlFunc.ToGuid(dto.ApplicationId))
                    .ToList().ForEach(f =>
                {
                    _redis.Client.HashSet("structure:view", f.Id.ToString(), Newtonsoft.Json.JsonConvert.SerializeObject(GetStructure(f.Id, dto.BusinessObjectType)));
                });
            }
        }

        
        
        
        
        
        public string GetRedis(StructureCacheSetDto dto)
        {
            var key = string.Empty;
            if (dto.BusinessObjectType == 1)
            {
                key = "structure:businessObject";
            }
            else if (dto.BusinessObjectType == 2)
            {
                key = "structure:compositeObject";
            }
            else if (dto.BusinessObjectType == 3)
            {
                key = "structure:view";
            }

            var result = string.Empty;
            if (!string.IsNullOrEmpty(key) && dto.BusinessObjectId != Guid.Empty && _redis.Client.HashExists(key, dto.BusinessObjectId.ToString()))
            {
                result = _redis.Client.HashGet(key, dto.BusinessObjectId.ToString()).ToString();

                
                if (string.IsNullOrEmpty(result))
                {
                    this.SetRedis(dto);
                    result = _redis.Client.HashGet(key, dto.BusinessObjectId.ToString()).ToString();
                }
            }

            return result;
        }

        
        
        
        
        
        
        private List<ObjectStructureDto> GetStructure(Guid businessObjectId, int businessObjectType)
        {
            var result = new List<ObjectStructureDto>();
            var mainTableId = Guid.Empty;

            if (businessObjectType == 1 || businessObjectType == 3)
            {
                mainTableId = businessObjectId;
            }
            else if (businessObjectType == 2)
            {
                var coj = _dbContext.Modeling.Queryable<CompositeObject>().Where(w => w.Id == businessObjectId).First();
                if (coj != null)
                {
                    mainTableId = coj.BusinessObjectId;
                }
            }

            if (businessObjectType == 3)
            {
                var view = _dbContext.Modeling.Queryable<View, DataBase>((v, db) => v.DatabaseId == db.Id)
                    .Where((v, db) => v.Id == mainTableId)
                    .Select((v, db) => new ObjectStructureDto
                    {
                        Id = v.Id,
                        DataBaseId = db.Id,
                        Name = v.Name,
                        CreateDate = v.CreateDate,
                        Description = v.Description,
                        DataBaseName = db.Name,
                        DataBaseDescription = db.Description,
                        DataBaseType = db.Type,
                        IsCustom = false,
                        IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                        State = v.State,
                        IsMain = true,
                        IsLogicalDelete = false,
                        IsTree = false,
                        ObjectType = "view"
                    })
                    .First();
                if (view != null)
                {
                    view.Columns = _dbContext.Modeling.Queryable<ViewColumns>()
                        .Where(vc => vc.ViewId == view.Id)
                        .OrderBy(vc => vc.IsPrimaryKey, OrderByType.Desc)
                        .OrderBy(vc => vc.OrderNumber)
                        .Select(vc => new ObjectColumnDto
                        {
                            Id = vc.Id,
                            Name = vc.BusinessObjectColumnAlias,
                            Description = vc.BusinessObjectColumnDescription,
                            DisplayType = vc.DisplayType,
                            IsPrimaryKey = vc.IsPrimaryKey == 1,
                            IsSystemColumn = false,
                            Length = 0,
                            Decimal = 0,
                            ColumnType = vc.ColumnType
                        })
                        .ToList();
                    result.Add(view);
                }
            }
            else
            {
                var mainTable = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((dt, db) => new JoinQueryInfos(JoinType.Inner, dt.DataBaseId == db.Id))
                 .Where((dt, db) => dt.Id == mainTableId)
                 .Select((dt, db) => new ObjectStructureDto
                 {
                     Id = dt.Id,
                     DataBaseId = dt.DataBaseId,
                     Name = dt.Name,
                     CreateDate = dt.CreateDate,
                     Description = dt.Description,
                     DataBaseName = db.Name,
                     DataBaseDescription = db.Description,
                     DataBaseType = db.Type,
                     IsCustom = dt.IsCustom,
                     IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                     State = dt.State,
                     IsMain = true,
                     IsLogicalDelete = dt.IsLogicalDelete,
                     IsTree = dt.IsTree,
                     ApplicationId = dt.ApplicationId,
                     ApplicationName = dt.ApplicationName,
                     ObjectType = "businessObject"
                 }).First();
                if (mainTable != null)
                {
                    mainTable.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn>()
                        .Where(a => a.BusinessObjectId == mainTable.Id && a.IsEnable.Value)
                         .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                        .OrderBy(a => a.IsSystemColumn)
                        .OrderBy(a => a.Order).ToList().MapTo<List<ObjectColumnDto>>();
                    result.Add(mainTable);
                    if (businessObjectType == 2)
                    {
                        var cojr = _dbContext.Modeling.Queryable<CompositeObjectRelation, BusinessObject, DataBase>((cr, dt, db) => new JoinQueryInfos(JoinType.Inner, cr.BusinessObjectId == dt.Id && !dt.IsDelete.Value && dt.State == 1, JoinType.Inner, dt.DataBaseId == db.Id))
                            .Where((cr, dt, db) => cr.CompositeObjectId == businessObjectId && cr.ObjectType == "businessObject")
                        .Select((cr, dt, db) => new ObjectStructureDto
                        {
                            Id = dt.Id,
                            DataBaseId = dt.DataBaseId,
                            Name = dt.Name,
                            CreateDate = dt.CreateDate,
                            Description = dt.Description,
                            DataBaseName = db.Name,
                            DataBaseDescription = db.Description,
                            DataBaseType = db.Type,
                            IsCustom = dt.IsCustom,
                            IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                            State = dt.State,
                            IsMain = false,
                            IsLogicalDelete = dt.IsLogicalDelete,
                            IsTree = dt.IsTree,
                            MainDataTableColumnId = cr.ParentBusinessObjectColumnId,
                            DetailDataTableColumnId = cr.BusinessObjectColumnId,
                            JoinType = cr.JoinType,
                            JoinRelation = cr.JoinRelation,
                            CompositeObjectRelationId = cr.Id,
                            ParentId = cr.ParentId,
                            ApplicationId = dt.ApplicationId,
                            ApplicationName = dt.ApplicationName,
                            ObjectType = "businessObject",
                            ExtraCondition = cr.ExtraCondition
                        }).ToList();
                        cojr.ForEach(f =>
                        {
                            f.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn>()
                            .Where(a => a.BusinessObjectId == f.Id && a.IsEnable.Value)
                            .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                            .OrderBy(a => a.IsSystemColumn)
                            .OrderBy(a => a.Order).ToList().MapTo<List<ObjectColumnDto>>();
                        });
                        result.AddRange(cojr);

                        var view = _dbContext.Modeling.Queryable<CompositeObjectRelation, View, DataBase>((cr, v, db) =>
                        new JoinQueryInfos(JoinType.Inner, cr.BusinessObjectId == v.Id, JoinType.Inner, v.DatabaseId == db.Id))
                            .Where((cr, v, db) => cr.CompositeObjectId == businessObjectId && cr.ObjectType == "view")
                        .Select((cr, v, db) => new ObjectStructureDto
                        {
                            Id = v.Id,
                            DataBaseId = v.DatabaseId,
                            Name = v.Name,
                            CreateDate = v.CreateDate,
                            Description = v.Description,
                            DataBaseName = db.Name,
                            DataBaseDescription = db.Description,
                            DataBaseType = db.Type,
                            IsCustom = false,
                            IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                            State = v.State,
                            IsMain = false,
                            IsLogicalDelete = false,
                            IsTree = false,
                            MainDataTableColumnId = cr.ParentBusinessObjectColumnId,
                            DetailDataTableColumnId = cr.BusinessObjectColumnId,
                            JoinType = cr.JoinType,
                            JoinRelation = cr.JoinRelation,
                            CompositeObjectRelationId = cr.Id,
                            ParentId = cr.ParentId,
                            ObjectType = "view",
                            ExtraCondition = cr.ExtraCondition
                        }).ToList();
                        view.ForEach(f =>
                        {
                            f.Columns = _dbContext.Modeling.Queryable<ViewColumns>()
                            .Where(a => a.ViewId == f.Id)
                            .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                            .OrderBy(a => a.OrderNumber)
                            .Select(a => new ObjectColumnDto
                            {
                                Id = a.Id,
                                Name = a.BusinessObjectColumnAlias,
                                Description = a.BusinessObjectColumnDescription,
                                DisplayType = a.DisplayType,
                                IsPrimaryKey = a.IsPrimaryKey == 1,
                                IsSystemColumn = false,
                                Length = 0,
                                Decimal = 0,
                                ColumnType = a.ColumnType
                            }).ToList();
                        });
                        result.AddRange(view);
                    }
                }
            }

            return result;
        }
    }
}
