using Medusa.Service.Modeling.Application.Common.Dtos;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Application.Common
{
    
    
    
    public interface ICommonService : IServiceBase
    {
        
        
        
        
        
        PageResult<dynamic> BusinessObjectData(SearchDto dto);

        
        
        
        
        
        PageResult<dynamic> BusinessObjectDataForIntegrationCenter(SearchForIntegrationCenterDto dto);

        
        
        
        
        void SaveBusinessObjectData(TableDto dto);

        
        
        
        void SpecialMdmRecordInfo();

        
        
        
        void SpecialPersonInCharge();
    }
}
