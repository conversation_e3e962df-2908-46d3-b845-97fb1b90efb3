using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 销售线索（SET）
    /// </summary>
    [EntityTable("crmsaleclueset")]
    public class CRMSaleClueSET
    {
        /// <summary>
        /// 销售线索主表主键
        /// </summary>
        public string SaleClueID { get; set; }

        /// <summary>
        /// CompetitorBrand
        /// </summary>
        public string CompetitorBrand { get; set; }

        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// ProjectStatus
        /// </summary>
        public string ProjectStatus { get; set; }

        /// <summary>
        /// TerminalCustomer
        /// </summary>
        public string TerminalCustomer { get; set; }

        /// <summary>
        /// TerminalModel
        /// </summary>
        public string TerminalModel { get; set; }

        /// <summary>
        /// AnnualDemand
        /// </summary>
        public string AnnualDemand { get; set; }

        /// <summary>
        /// Price
        /// </summary>
        public string Price { get; set; }

        /// <summary>
        /// CompetitorPrice
        /// </summary>
        public string CompetitorPrice { get; set; }

        /// <summary>
        /// MainType
        /// </summary>
        public string MainType { get; set; }

        /// <summary>
        /// MainTypeName
        /// </summary>
        public string MainTypeName { get; set; }

        /// <summary>
        /// ProductModel
        /// </summary>
        public string ProductModel { get; set; }

        /// <summary>
        /// Stock
        /// </summary>
        public string Stock { get; set; }

        /// <summary>
        /// StockName
        /// </summary>
        public string StockName { get; set; }

        /// <summary>
        /// MarketCategory
        /// </summary>
        public string MarketCategory { get; set; }

        /// <summary>
        /// MarketCategoryName
        /// </summary>
        public string MarketCategoryName { get; set; }

        /// <summary>
        /// KeyPoints
        /// </summary>
        public string KeyPoints { get; set; }

        /// <summary>
        /// InterviewUser
        /// </summary>
        public string InterviewUser { get; set; }

        /// <summary>
        /// OtherUser
        /// </summary>
        public string OtherUser { get; set; }

        /// <summary>
        /// InterviewDate
        /// </summary>
        public string InterviewDate { get; set; }

        /// <summary>
        /// InterviewContent
        /// </summary>
        public string InterviewContent { get; set; }

        /// <summary>
        /// SelfView
        /// </summary>
        public string SelfView { get; set; }

        /// <summary>
        /// Purpose
        /// </summary>
        public string Purpose { get; set; }

        /// <summary>
        /// PurposeName
        /// </summary>
        public string PurposeName { get; set; }

        /// <summary>
        /// ProductIntro
        /// </summary>
        public string ProductIntro { get; set; }

        /// <summary>
        /// ProductIntroName
        /// </summary>
        public string ProductIntroName { get; set; }

        /// <summary>
        /// TradeCondition
        /// </summary>
        public string TradeCondition { get; set; }

        /// <summary>
        /// TradeConditionName
        /// </summary>
        public string TradeConditionName { get; set; }

        /// <summary>
        /// UsePurpose
        /// </summary>
        public string UsePurpose { get; set; }

        /// <summary>
        /// UsePurposeName
        /// </summary>
        public string UsePurposeName { get; set; }

        /// <summary>
        /// SupplyForm
        /// </summary>
        public string SupplyForm { get; set; }

        /// <summary>
        /// SupplyFormName
        /// </summary>
        public string SupplyFormName { get; set; }

        /// <summary>
        /// ProcessMode
        /// </summary>
        public string ProcessMode { get; set; }

        /// <summary>
        /// CombinationObj
        /// </summary>
        public string CombinationObj { get; set; }

        /// <summary>
        /// CombinationObjName
        /// </summary>
        public string CombinationObjName { get; set; }

        /// <summary>
        /// ProductType
        /// </summary>
        public string ProductType { get; set; }

        /// <summary>
        /// ProductTypeName
        /// </summary>
        public string ProductTypeName { get; set; }

        /// <summary>
        /// Environment
        /// </summary>
        public string Environment { get; set; }

        /// <summary>
        /// EnvironmentName
        /// </summary>
        public string EnvironmentName { get; set; }

        /// <summary>
        /// ProductRequirement
        /// </summary>
        public string ProductRequirement { get; set; }

        /// <summary>
        /// ProductRequirementName
        /// </summary>
        public string ProductRequirementName { get; set; }

        /// <summary>
        /// AppArea
        /// </summary>
        public string AppArea { get; set; }

        /// <summary>
        /// AppAreaName
        /// </summary>
        public string AppAreaName { get; set; }

        /// <summary>
        /// Ratio
        /// </summary>
        public string Ratio { get; set; }

        /// <summary>
        /// Expectation
        /// </summary>
        public string Expectation { get; set; }

        /// <summary>
        /// ExpectationName
        /// </summary>
        public string ExpectationName { get; set; }

        /// <summary>
        /// DemandDesc
        /// </summary>
        public string DemandDesc { get; set; }

        /// <summary>
        /// Percent
        /// </summary>
        public string Percent { get; set; }

        /// <summary>
        /// Turnover
        /// </summary>
        public string Turnover { get; set; }

        /// <summary>
        /// AccountPeriod
        /// </summary>
        public string AccountPeriod { get; set; }

        /// <summary>
        /// FinancialCondition
        /// </summary>
        public string FinancialCondition { get; set; }

        /// <summary>
        /// Evaluation
        /// </summary>
        public string Evaluation { get; set; }

        /// <summary>
        /// EvaluationName
        /// </summary>
        public string EvaluationName { get; set; }

        /// <summary>
        /// PatentBlockage
        /// </summary>
        public string PatentBlockage { get; set; }

        /// <summary>
        /// CreateDate
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// ModifyDate
        /// </summary>
        public DateTime ModifyDate { get; set; }

        /// <summary>
        /// CreateUserId
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// ModifyUserId
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// ParentId
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// IsDelete
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// CreateUserOrgPathId
        /// </summary>
        public string CreateUserOrgPathId { get; set; }
    }
}
