using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.DictionaryMgr.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.DictionaryMgr
{
    
    
    
    public class DictionaryMgrService : ServiceBase, IDictionaryMgrService
    {
        #region 
        readonly MyDbContext _dbContext;

        
        
        
        
        
        public DictionaryMgrService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
        }

        #endregion

        
        
        
        
        
        public DictionaryGroupDto GetDictionaryGroup(bool isHasStructure)
        {
            var dics = _dbContext.Modeling.Queryable<Dictionary>()
                .Where(a => a.Status == 1 && SqlFunc.IsNullOrEmpty(a.UpperId))
                .OrderBy(a => a.OrderNum)
                .ToList();
            var structure = new List<ObjectStructureDto>();
            if (isHasStructure)
            {
                var mainTable = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((dt, db) => new JoinQueryInfos(JoinType.Inner, dt.DataBaseId == db.Id))
                   .Where((dt, db) => !dt.IsDelete.Value && dt.State == 1 && dt.Name == "Dictionary")
                   .Select((dt, db) => new ObjectStructureDto
                   {
                       Id = dt.Id,
                       DataBaseId = dt.DataBaseId,
                       Name = dt.Name,
                       CreateDate = dt.CreateDate,
                       Description = dt.Description,
                       DataBaseName = db.Name,
                       DataBaseDescription = db.Description,
                       DataBaseType = db.Type,
                       IsCustom = dt.IsCustom,
                       IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                       State = dt.State,
                       IsMain = true,
                       IsLogicalDelete = dt.IsLogicalDelete,
                       IsTree = dt.IsTree,
                       ApplicationId = dt.ApplicationId,
                       ApplicationName = dt.ApplicationName,
                       ObjectType = "businessObject"
                   }).First();
                if (mainTable != null)
                {
                    mainTable.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn>()
                    .Where(a => a.BusinessObjectId == mainTable.Id && a.IsEnable.Value)
                   .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                   .OrderBy(a => a.IsSystemColumn)
                   .OrderBy(a => a.Order)
                   .Select(a => new ObjectColumnDto
                   {
                       Description = a.Description,
                       DisplayType = a.DisplayType,
                       Id = a.Id,
                       IsPrimaryKey = a.IsPrimaryKey,
                       Name = a.Name
                   })
                   .ToList();
                    structure.Add(mainTable);
                }
            }

            return new DictionaryGroupDto()
            {
                Dics = dics,
                Structure = structure
            };
        }

        
        
        
        
        
        
        public PageResult<Dictionary> GetDictionaryByGroupId(Guid groupId, DictionaryQueryDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<Dictionary>()
                .Includes(a => a.OrgPermission.Where(w => w.Status == 1).ToList())
                .Includes(a => a.UserPermission.Where(w => w.Status == 1).ToList())
                .Where(a => a.UpperId == groupId && a.Status == 1)
                .WhereIF(dto.EnableOrgPermission.HasValue && dto.EnableOrgPermission.Value, a => a.OrgPermission.Count(w => SqlFunc.StartsWith(dto.OrgPath, w.OrgPath)) > 0)
                .WhereIF(dto.EnableUserPermission.HasValue && dto.EnableUserPermission.Value, a => a.UserPermission.Count(w => dto.UserLoginId == w.UserLoginId) > 0)
                .OrderBy(a => a.OrderNum);
            string sql = itemsQuery.ToSqlString();
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<Dictionary>
            {
                Items = items,
                Total = dto.IsAll ? items.Count : count
            };
        }

        
        
        
        
        public void Save(Dictionary dto)
        {
            var list = _dbContext.Modeling.Queryable<Dictionary>().Where(w => w.Code == dto.Code && w.Status != 0)
                .WhereIF(dto.Id != Guid.Empty, w => w.Id != dto.Id)
                .WhereIF(!string.IsNullOrEmpty(dto.TypeCode), w => w.TypeCode == dto.TypeCode)
                .WhereIF(string.IsNullOrEmpty(dto.TypeCode), w => SqlFunc.IsNullOrEmpty(w.UpperId))
                .ToList();
            if (list.Count > 0)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dictionary.exist"));
            }

            if (dto.Id == Guid.Empty)
            {
                dto.Status = 1;
                dto.Id = Guid.NewGuid();
                _dbContext.Modeling.Insertable<Dictionary>(dto).ExecuteCommand();
                if (dto.UpperId.HasValue && dto.UpperId.Value != Guid.Empty)
                {
                    if (dto.OrgPermission != null && dto.OrgPermission.Count > 0)
                    {
                        dto.OrgPermission.ForEach(f =>
                        {
                            f.Id = Guid.NewGuid();
                            f.DictionaryId = dto.Id;
                            f.Status = 1;
                        });
                        _dbContext.Modeling.Insertable<DictionaryOrgPermission>(dto.OrgPermission).ExecuteCommand();
                    }

                    if (dto.UserPermission != null && dto.UserPermission.Count > 0)
                    {
                        dto.UserPermission.ForEach(f =>
                        {
                            f.Id = Guid.NewGuid();
                            f.DictionaryId = dto.Id;
                            f.Status = 1;
                        });
                        _dbContext.Modeling.Insertable<DictionaryUserPermission>(dto.UserPermission).ExecuteCommand();
                    }
                }
            }
            else
            {
                var item = _dbContext.Modeling.Queryable<Dictionary>().InSingle(dto.Id);
                if (item == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.dictionary.notfound"));
                }

                item.Name = dto.Name;
                item.Code = dto.Code;
                item.Value = dto.Value;
                item.OrderNum = dto.OrderNum;
                item.Remark = dto.Remark;
                _dbContext.Modeling.Updateable<Dictionary>(item).ExecuteCommand();

                if (item.UpperId.HasValue && item.UpperId.Value != Guid.Empty)
                {
                    _dbContext.Modeling.Updateable<DictionaryOrgPermission>().SetColumns(a => a.Status == 0).Where(a => a.DictionaryId == item.Id).ExecuteCommand();
                    if (dto.OrgPermission != null && dto.OrgPermission.Count > 0)
                    {
                        dto.OrgPermission.ForEach(f =>
                        {
                            f.Id = Guid.NewGuid();
                            f.DictionaryId = item.Id;
                            f.Status = 1;
                        });
                        var x = _dbContext.Modeling.Storageable(dto.OrgPermission).WhereColumns(w => new { w.OrgName, w.OrgPath, w.DictionaryId }).ToStorage();
                        x.AsInsertable.ExecuteCommand();
                        x.AsUpdateable.IgnoreColumns(w => w.Id).ExecuteCommand();
                    }

                    _dbContext.Modeling.Updateable<DictionaryUserPermission>().SetColumns(a => a.Status == 0).Where(a => a.DictionaryId == item.Id).ExecuteCommand();
                    if (dto.UserPermission != null && dto.UserPermission.Count > 0)
                    {
                        dto.UserPermission.ForEach(f =>
                        {
                            f.Id = Guid.NewGuid();
                            f.DictionaryId = item.Id;
                            f.Status = 1;
                        });
                        var x = _dbContext.Modeling.Storageable(dto.UserPermission).WhereColumns(w => new { w.UserName, w.UserLoginId, w.DictionaryId }).ToStorage();
                        x.AsInsertable.ExecuteCommand();
                        x.AsUpdateable.IgnoreColumns(w => w.Id).ExecuteCommand();
                    }
                }
            }
        }

        
        
        
        
        public void Delete(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<Dictionary>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dictionary.notfound"));
            }

            item.Status = 0;
            _dbContext.Modeling.Updateable<Dictionary>(item).ExecuteCommand();
            if (item.UpperId.HasValue && item.UpperId.Value != Guid.Empty)
            {
                _dbContext.Modeling.Updateable<DictionaryOrgPermission>().SetColumns(a => a.Status == 0).Where(a => a.DictionaryId == item.Id).ExecuteCommand();
                _dbContext.Modeling.Updateable<DictionaryUserPermission>().SetColumns(a => a.Status == 0).Where(a => a.DictionaryId == item.Id).ExecuteCommand();
            }
        }
    }
}
