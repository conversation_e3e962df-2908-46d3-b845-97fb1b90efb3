using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Medusa.Service.Modeling.Application.CRMTasks.Dtos;
using Medusa.Service.Modeling.Application.Tasks.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.LowCode;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.CRMTasks
{
    
    
    
    public class TasksService : ServiceBase, ITasksService
    {
        private readonly MyDbContext _dbContext;
        readonly string setPath = string.Empty;

        
        
        
        
        public TasksService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            setPath = appSettings["BusinessOrg"]?["SET"]?.ToString();
        }

        
        
        
        public MyDbContext P_DbContext => _dbContext;

        
        
        
        
        
        public PageResult<ReturnTaskTemplateDto> SearchTaskTemplate(SearchTaskTemplateDto dto)
        {
            var count = 0;

            bool sign = false;
            List<Guid> templateIds = new List<Guid>();

            if (!string.IsNullOrEmpty(dto.AppliedDepartmentIds) || !string.IsNullOrEmpty(dto.AppliedPersonnelIds))
            {
                sign = true;
                var itemsHistoryQuery = _dbContext.Modeling.Queryable<Crmtaskapplicationhistory>()
                .Where(c => !c.IsDelete).ToList();

                if (!string.IsNullOrEmpty(dto.AppliedDepartmentIds))
                {
                    var deptids = dto.AppliedDepartmentIds.Split(',').ToList();
                    itemsHistoryQuery.RemoveAll(c => c.AppliedDepartmentIds.Split(',').Join(deptids, p => p, q => q, (p, q) => p).Count() == 0);
                }

                if (!string.IsNullOrEmpty(dto.AppliedPersonnelIds))
                {
                    var userids = dto.AppliedPersonnelIds.Split(',').ToList();
                    itemsHistoryQuery.RemoveAll(c => c.AppliedPersonnelIds.Split(',').Join(userids, p => p, q => q, (p, q) => p).Count() == 0);
                }

                templateIds = itemsHistoryQuery.Select(c => c.TemplateId).Distinct().ToList();
            }

            var itemsQuery = _dbContext.Modeling.Queryable<Crmtasktemplate>()
            .WhereIF(sign, c => templateIds.Contains(c.Templateid))
            .WhereIF(!string.IsNullOrEmpty(dto.Templatename), c => c.Templatename.Contains(dto.Templatename))
            .WhereIF(dto.CreatedDateStart.HasValue && dto.CreatedDateEnd.HasValue, c => c.CreatedDate.HasValue && c.CreatedDate.Value.Date >= dto.CreatedDateStart.Value.Date
             && c.CreatedDate.Value.Date <= dto.CreatedDateEnd.Value.Date)
            .OrderBy(c => c.CreatedDate, SqlSugar.OrderByType.Desc)
            .Select(c => new ReturnTaskTemplateDto
            {
                Templateid = c.Templateid, 
                Templatename = c.Templatename, 
                CreatedDate = c.CreatedDate.Value.ToString("yyyy-MM-dd"), 
                IsDelete = c.IsDelete, 
                IsActive = c.IsActive, 
                Description = c.Description, 
                TaskConfigJson = c.TaskConfigJson, 
            });

            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<ReturnTaskTemplateDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        public void AddTaskTemplate(CrmtasktemplateDto dto)
        {
            var list = _dbContext.Modeling.Queryable<Crmtasktemplate>().Where(w => w.Templatename == dto.Templatename).ToList();
            if (list.Count > 0)
            {
                throw new StatusNotFoundException("相同名称的任务模板已存在！");
            }

            if (dto.IsActive && string.IsNullOrEmpty(dto.TaskConfigJson))
            {
                throw new StatusConflictException("保存为已发布的任务模板必须配置好任务项配置！");
            }

            if (!string.IsNullOrEmpty(dto.TaskConfigJson))
            {
                List<TaskConfigDto> taskConfigDtos = Newtonsoft.Json.JsonConvert.DeserializeObject<List<TaskConfigDto>>(dto.TaskConfigJson);
                foreach (var item in taskConfigDtos)
                {
                    if (item.Checked)
                    {
                        if (item.ConfigNum <= 0)
                        {
                            item.ConfigNum = 0;
                            item.Checked = false;
                        }
                    }
                    else
                    {
                        item.ConfigNum = 0;
                    }
                }

                
                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                };

                dto.TaskConfigJson = Newtonsoft.Json.JsonConvert.SerializeObject(taskConfigDtos, settings);
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Insertable(new Crmtasktemplate()
            {
                Templateid = Guid.NewGuid(),
                Templatename = dto.Templatename,
                TaskConfigJson = dto.TaskConfigJson,
                IsDelete = dto.IsDelete,
                IsActive = dto.IsActive,
                Description = dto.Description,
                CreatedDate = DateTime.Now,
                CreatedUserId = userId
            }).ExecuteCommand();
        }

        
        
        
        
        public void UpdateTaskTemplate(CrmtasktemplateDto dto)
        {
            var entity = _dbContext.Modeling.Queryable<Crmtasktemplate>().InSingle(dto.Templateid);
            if (entity == null)
            {
                throw new StatusNotFoundException("未找到需要修改的任务模板！");
            }

            var list = _dbContext.Modeling.Queryable<Crmtasktemplate>().Where(w => w.Templatename == dto.Templatename && w.Templateid != dto.Templateid).ToList();
            if (list.Count > 0)
            {
                throw new StatusNotFoundException("相同名称的任务模板已存在！");
            }

            if (dto.IsActive && string.IsNullOrEmpty(dto.TaskConfigJson))
            {
                throw new StatusConflictException("保存为已发布的任务模板必须配置好任务项配置！");
            }

            if (!string.IsNullOrEmpty(dto.TaskConfigJson))
            {
                List<TaskConfigDto> taskConfigDtos = Newtonsoft.Json.JsonConvert.DeserializeObject<List<TaskConfigDto>>(dto.TaskConfigJson);
                foreach (var item in taskConfigDtos)
                {
                    if (item.Checked)
                    {
                        if (item.ConfigNum <= 0)
                        {
                            item.ConfigNum = 0;
                            item.Checked = false;
                        }
                    }
                    else
                    {
                        item.ConfigNum = 0;
                    }
                }

                
                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                };

                dto.TaskConfigJson = Newtonsoft.Json.JsonConvert.SerializeObject(taskConfigDtos, settings);
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Updateable<Crmtasktemplate>()
                 .SetColumns(w => new Crmtasktemplate()
                 {
                     Templatename = dto.Templatename,
                     TaskConfigJson = dto.TaskConfigJson,
                     IsDelete = dto.IsDelete,
                     IsActive = dto.IsActive,
                     Description = dto.Description,
                     ModifiedDate = DateTime.Now,
                     ModifiedUserId = userId,
                 })
                 .Where(w => w.Templateid == dto.Templateid)
                .ExecuteCommand();
        }

        
        
        
        
        public List<TaskConfigDto> GetTaskConfig()
        {
            var enumDataList = new List<TaskConfigDto>();
            foreach (TaskTypeEnum value in Enum.GetValues(typeof(TaskTypeEnum)))
            {
                TaskConfigDto taskConfigDto = new TaskConfigDto();
                string description = GetEnumDescription(value);
                taskConfigDto.TaskConfig = value;
                taskConfigDto.ConfigNanme = description;
                taskConfigDto.ConfigNum = 0;

                enumDataList.Add(taskConfigDto);
            }

            return enumDataList;
        }

        
        
        
        
        
        public PageResult<Crmtaskapplicationhistory> SearchTaskTemplateHistory(SearchTaskTemplateHistoryDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<Crmtaskapplicationhistory>()
                .Where(c => c.TemplateId == dto.TemplateId)
                .OrderBy(c => c.CreatedDate, SqlSugar.OrderByType.Desc);

            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);

            return new PageResult<Crmtaskapplicationhistory>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        public void AddTaskTemplateApplication(CrmtasktemplateApplicationDto dto)
        {
            if (!string.IsNullOrEmpty(dto.TaskConfigJson))
            {
                List<TaskConfigDto> taskConfigDtos = Newtonsoft.Json.JsonConvert.DeserializeObject<List<TaskConfigDto>>(dto.TaskConfigJson);
                foreach (var item in taskConfigDtos)
                {
                    if (item.Checked)
                    {
                        if (item.ConfigNum <= 0)
                        {
                            item.ConfigNum = 0;
                            item.Checked = false;
                        }
                    }
                    else
                    {
                        item.ConfigNum = 0;
                    }
                }

                
                var settings = new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                };

                dto.TaskConfigJson = Newtonsoft.Json.JsonConvert.SerializeObject(taskConfigDtos, settings);
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Insertable(new Crmtaskapplicationhistory()
            {
                TemplateHistoryId = Guid.NewGuid(),
                TemplateId = dto.Templateid,
                TemplateName = dto.Templatename,
                AppliedDepartment = dto.AppliedDepartment,
                AppliedDepartmentIds = dto.AppliedDepartmentIds,
                AppliedPersonnel = dto.AppliedPersonnel,
                AppliedPersonnelIds = dto.AppliedPersonnelIds,
                TemplateDescription = dto.TemplateDescription,
                TaskConfigJson = dto.TaskConfigJson,
                AppliedStatus = dto.AppliedStatus,
                EffectiveEndDate = dto.EffectiveEndDate,
                EffectiveStartDate = dto.EffectiveStartDate,
                Remark = dto.Remark,
                CronExpression = dto.CronExpression,
                IsDelete = false,
                CreatedDate = DateTime.Now,
                CreatedUserId = userId,
                ModifiedDate = DateTime.Now,
                ModifiedUserId = userId,
            }).ExecuteCommand();
        }

        
        
        
        
        public void ChangeTaskTemplateApplication(ChangeTemplateApplicationDto dto)
        {
            var entity = _dbContext.Modeling.Queryable<Crmtaskapplicationhistory>().InSingle(dto.TemplateHistoryId);
            if (entity == null)
            {
                throw new StatusNotFoundException("未找到需要修改的任务模板应用数据！");
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Updateable<Crmtaskapplicationhistory>()
                 .SetColumns(w => new Crmtaskapplicationhistory()
                 {
                     AppliedStatus = dto.AppliedStatus,
                     ModifiedDate = DateTime.Now,
                     ModifiedUserId = userId,
                 })
                 .Where(w => w.TemplateHistoryId == dto.TemplateHistoryId)
                .ExecuteCommand();
        }

        
        
        
        
        public List<CronConfigDto> GetCronConfig()
        {
            var enumDataList = new List<CronConfigDto>();
            foreach (CronExpressionEnum value in Enum.GetValues(typeof(CronExpressionEnum)))
            {
                CronConfigDto taskConfigDto = new CronConfigDto();
                string description = GetEnumDescription(value);
                taskConfigDto.CronConfig = value;
                taskConfigDto.ConfigNanme = description.Split('|')[1];
                taskConfigDto.ConfigCron = description.Split('|')[0];

                enumDataList.Add(taskConfigDto);
            }

            return enumDataList;
        }

        
        
        
        
        
        public PageResult<Crmtask> SearchTask(SearchTaskDto dto)
        {
            var count = 0;

            
            var userId = UserConstants.CurrentUser.Value.Id;
            var users = _dbContext.Boost.Queryable<Core.Entities.Boost.Users>()
                .Where(c => c.UpperUserId == userId && c.Status == 1)
                .Select(c => c.UserId).ToList();
            users.Add(userId);

            var itemsQuery = _dbContext.Modeling.Queryable<Crmtask>()
            .Where(c => c.IsDelete == false && users.Contains(c.ResponsiblePersonnel))
            .WhereIF(dto.CreatedDateStart.HasValue && dto.CreatedDateEnd.HasValue, c => c.CreatedDate.HasValue && c.CreatedDate.Value.Date >= dto.CreatedDateStart.Value.Date
             && c.CreatedDate.Value.Date <= dto.CreatedDateEnd.Value.Date)
            .OrderBy(c => c.CreatedDate, SqlSugar.OrderByType.Desc);

            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<Crmtask>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        public void AddCrmTask(Guid? templateId)
        {
            
            var crmTaskTemplates = _dbContext.Modeling.Queryable<Crmtasktemplate, Crmtaskapplicationhistory>((ct, ch) => new object[]
            {
                JoinType.Inner, ct.Templateid == ch.TemplateId
            })
            .WhereIF(templateId.HasValue, (ct, ch) => ct.Templateid == templateId)
            .Where((ct, ch) => ct.IsDelete == false
                               && ct.IsActive == true
                               && ch.IsDelete == false
                               && ch.AppliedStatus == true
                               && SqlFunc.Between(DateTime.Now, ch.EffectiveStartDate, ch.EffectiveEndDate))
            .Select((ct, ch) => new CrmWaitCreateTaskDto
            {
                TemplateHistoryId = ch.TemplateHistoryId,
                TemplateName = ch.TemplateName,
                AppliedDepartmentIds = ch.AppliedDepartmentIds,
                AppliedPersonnelIds = ch.AppliedPersonnelIds,
                IsDelete = ch.IsDelete,
                CronExpression = ch.CronExpression,
                Description = ct.Description
            })
            .ToList();

            var tasks = new List<Crmtask>();

            foreach (var item in crmTaskTemplates)
            {
                var sDate = default(DateTime);
                var eDate = default(DateTime);
                if (item.CronExpression == "0 0 1 * * ?")
                { 
                    sDate = DateTime.Now.Date.AddDays(1 - DateTime.Now.Day).ToShortDateString().ToOurDateTime();
                    eDate = DateTime.Now.Date.AddDays(1 - DateTime.Now.Day).AddMonths(1).AddDays(-1).ToShortDateString().ToOurDateTime();
                }
                else if (item.CronExpression == "0 0 1 ? * 2")
                { 
                    sDate = DateTime.Now.AddDays(0 - Convert.ToInt16(DateTime.Now.DayOfWeek) + 1).ToShortDateString().ToOurDateTime();
                    eDate = DateTime.Now.AddDays(6 - Convert.ToInt16(DateTime.Now.DayOfWeek) + 1).ToShortDateString().ToOurDateTime();
                }
                else
                {
                    throw new Exception("格式错误!");
                }

                var users = new List<Core.Entities.Boost.Users>();
                if (!string.IsNullOrEmpty(item.AppliedPersonnelIds))
                {
                    var userIds = item.AppliedPersonnelIds.Split(',').Select(x => Guid.Parse(x)).ToList();
                    var userInfo = _dbContext.Boost.Queryable<Core.Entities.Boost.Users>().Where(x => userIds.Contains(x.UserId)).ToList();
                    users.AddRange(userInfo);
                }

                if (!string.IsNullOrEmpty(item.AppliedDepartmentIds))
                {
                    var orgIds = item.AppliedDepartmentIds.Split(',').ToList();

                    orgIds.ForEach(orgId =>
                    {
                        var userInfo = _dbContext.Boost.Queryable<Core.Entities.Boost.Users>().Where(x => SqlFunc.Contains(x.FullPathCode, orgId)).ToList();
                        users.AddRange(userInfo);
                    });
                }

                
                var hasUserIds = _dbContext.Modeling.Queryable<Crmtask>()
                       .Where(x => x.TemplateHistoryId == item.TemplateHistoryId && x.StartTime == sDate && x.EndTime == x.EndTime)
                       .Select(x => x.ResponsiblePersonnel).ToList();

                
                users = users.Where(x => !hasUserIds.Contains(x.UserId)).ToList();

                users.ForEach(user =>
                {
                    var task = new Crmtask
                    {
                        TaskId = Guid.NewGuid(),
                        TemplateHistoryId = item.TemplateHistoryId,
                        TaskTitle = item.TemplateName,
                        ResponsiblePersonnel = user.UserId,
                        ResponsiblePersonnel_Name = user.UserName,
                        StartTime = sDate,
                        EndTime = eDate,
                        TaskStatus = false,
                        TaskDescription = item.Description,
                        IsDelete = false,
                        CreatedUserId = "admin",
                        CreatedDate = DateTime.Now,
                    };
                    tasks.Add(task);
                });
            }

            if (tasks.Any())
            {
                _dbContext.Modeling.Storageable(tasks).ExecuteCommand();
            }
        }

        #region 内部扩展方法

        
        
        
        
        
        private static string GetEnumDescription(Enum value)
        {
            var fieldInfo = value.GetType().GetField(value.ToString());
            var attributes = (DescriptionAttribute[])fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.Length > 0 ? attributes[0].Description : value.ToString();
        }
        #endregion
    }
}
