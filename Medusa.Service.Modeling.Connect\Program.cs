using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AspectCore.Extensions.DependencyInjection;
using Medusa.Service.Cache;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.AppConfig;
using Medusa.Service.Modeling.Core;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MT.Enterprise.Core;
using MT.Enterprise.Core.Cache;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.SDK;
using MT.Enterprise.SDK.ApiRequest;
using MT.Enterprise.SDK.Gateway;
using MT.Enterprise.Utils.Extensions;
using Nacos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Connect
{
    /// <summary>
    /// Program
    /// </summary>
    public static class Program
    {
        /// <summary>
        /// 入口
        /// </summary>
        /// <param name="args">args</param>
        /// <returns>异步结果</returns>
        public static async Task Main(string[] args)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", false, true)
                .AddJsonFile($"appsettings.{environmentName}.json", true, true)
                .Build();
            var nacosConfig = configuration.GetSection("Nacos");
            await Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddMemoryCache();
                    services.AddNacos(configure =>
                    {
                        configure.DefaultTimeOut = nacosConfig["DefaultTimeOut"].ToOurInt();
                        configure.ServerAddresses = nacosConfig.GetSection("ServerAddresses").Get<string[]>().ToList();
                        configure.Namespace = nacosConfig["Namespace"];
                        configure.ListenInterval = nacosConfig["ListenInterval"].ToOurInt();
                    });
                    var AppConfigDto = CreateRegister(services, nacosConfig);
                    // 添加 Core
                    services.AddCore(core =>
                    {
                        core.AddI18n(opt =>
                        {
                            opt.Languages = GetI18Languages(services, nacosConfig);
                            opt.DefaultLang = AppConstants.I18nDefaultLang;
                        });

                        core.AddORM(orm =>
                        {
                            Enum.TryParse(AppConfigDto["Persistence"]?["DbType"]?.ToString(), true, out ConnectionDbType dbType);
                            orm.AddDbContext(opt =>
                            {
                                opt.Name = "modeling";
                                opt.ConnectionString = AppConfigDto["Persistence"]?["ConnectionString"]?.ToString();
                                opt.ConnectionDbType = dbType;
                                opt.IsDefault = true;
                            });
                        });

                        core.AddCache(opt =>
                        {
                            opt.RedisHost = AppConfigDto["Redis"]?["Host"]?.ToString();
                            opt.RedisDb = Convert.ToInt32(AppConfigDto["Redis"]?["DB"]);
                            opt.Password = AppConfigDto["Redis"]?["Password"]?.ToString();
                        });
                    });

                    // 添加 SDK
                    services.AddSDK(sdk =>
                    {
                        sdk.AddGateway(opt =>
                        {
                            opt.Host = AppConfigDto["ApiGateway"]?["Host"]?.ToString();
                            opt.Port = Convert.ToInt32(AppConfigDto["ApiGateway"]?["Port"]);
                            opt.AppKey = AppConfigDto["ApiGateway"]?["AppKey"]?.ToString();
                            opt.AppSecret = AppConfigDto["ApiGateway"]?["AppSecret"]?.ToString();
                            opt.Timeout = Convert.ToInt32(AppConfigDto["ApiGateway"]?["Timeout"]);
                        });
                        sdk.AddApiRequest(opt =>
                        {
                            opt.Host = AppConfigDto["ApiRequest"]?["Host"]?.ToString();
                            opt.Db = Convert.ToInt32(AppConfigDto["ApiRequest"]?["DB"]);
                            opt.Password = AppConfigDto["ApiRequest"]?["Password"]?.ToString();
                            opt.Timeout = Convert.ToInt32(AppConfigDto["ApiRequest"]?["Timeout"]);
                            opt.IsHttps = (AppConfigDto["ApiRequest"]?["IsHttps"].Value<bool>()).Value;
                        });
                    });
                    services.AddSingleton<ProcessReceiveByMQService>();
                    services.AddCacheServices();
                    services.AddApplicationServices();
                    var serviceProvider = services.BuildServiceProvider();
                    var appConfigService = serviceProvider.GetService<IAppConfigService>();
                    appConfigService.AppConfigCache(AppConfigDto);
                    var processReceiveByMQService = serviceProvider.GetService<ProcessReceiveByMQService>();
                    processReceiveByMQService.Execute();

                }).UseServiceProviderFactory(new DynamicProxyServiceProviderFactory())
                .RunConsoleAsync();
        }

        /// <summary>
        /// CreateRegister
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="section">section</param>
        /// <returns>JObject</returns>
        private static JObject CreateRegister(IServiceCollection services, IConfigurationSection section)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (!string.IsNullOrEmpty(environmentName) && (environmentName == "Local" || environmentName == "Development"))
            {
                string config = string.Empty;
                if (environmentName == "Development")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Development.json"));
                }
                else if (environmentName == "Local")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Local.json"));
                }

                return JObject.Parse(config);
            }
            else
            {
                var serviceProvider = services.BuildServiceProvider();
                var configClient = serviceProvider.GetService<INacosConfigClient>();

                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = section["DataId"],
                    Group = section["GroupId"],
                }).Result;

                if (!string.IsNullOrEmpty(res))
                {
                    var appSettings = JObject.Parse(res);
                    return appSettings;
                }

                throw new Exception("Not Found Nacos!");
            }
        }

        /// <summary>
        /// 获取I18多语言文件
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="section">section</param>
        /// <returns>Dictionary</returns>
        private static Dictionary<string, string> GetI18Languages(IServiceCollection services, IConfigurationSection section)
        {
            var result = new Dictionary<string, string>();

            var serviceProvider = services.BuildServiceProvider();
            var configClient = serviceProvider.GetService<INacosConfigClient>();

            var languageIds = section.GetSection("LanguageIds").Get<string[]>().ToList();

            foreach (var languageId in languageIds)
            {
                var matched = Regex.Matches(languageId, @"([A-Za-z0-9-_]+)").ToList();
                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = languageId,
                    Group = section["LanguageGroupId"],
                }).Result;
                result.Add(matched[0].ToString(), res);
            }

            return result;
        }
    }
}
