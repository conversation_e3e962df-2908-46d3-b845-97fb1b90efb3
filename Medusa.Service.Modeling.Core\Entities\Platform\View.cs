using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 视图表
    /// </summary>
    [EntityTable("Views")]
    public class View
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 视图名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 视图描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 应用ID
        /// </summary>
        public Guid? AppId { get; set; }

        /// <summary>
        /// 是否自定义
        /// </summary>
        public bool IsCustom { get; set; }

        /// <summary>
        /// 数据库ID
        /// </summary>
        public Guid DatabaseId { get; set; }

        /// <summary>
        /// 状态：0-已删除 1-未发布 2-已发布
        /// </summary>
        public int State { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 复合条件
        /// </summary>
        public string GroupFilter { get; set; }
    }
}
