using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.Common;
using Medusa.Service.Modeling.Application.Common.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// V1_CommonController
    /// </summary>
    [Route("v1/common")]
    [ApiExplorerSettings(GroupName = "Common.v1")]
    public class V1_CommonController : ProductControllerBase
    {
        #region //  服务注入
        readonly ICommonService _commonService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_CommonController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="commonService">commonService</param>
        public V1_CommonController(ICommonService commonService)
        {
            _commonService = commonService;
        }
        #endregion

        /// <summary>
        /// 获取业务对象数据
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("business-object-data")]
        public PageResult<dynamic> BusinessObjectData([FromBody] SearchDto dto)
        {
            return _commonService.BusinessObjectData(dto);
        }

        /// <summary>
        /// 获取业务对象数据For集成中心
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("business-object-data/integration-center")]
        public PageResult<dynamic> BusinessObjectDataForIntegrationCenter([FromBody] SearchForIntegrationCenterDto dto)
        {
            return _commonService.BusinessObjectDataForIntegrationCenter(dto);
        }

        /// <summary>
        /// 保存业务对象数据
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("business-object-data/save")]
        public void SaveBusinessObjectData([FromBody] TableDto dto)
        {
            _commonService.SaveBusinessObjectData(dto);

            if (dto.TableName.ToUpper() == "mdmrecordinfo".ToUpper())
            {
                _commonService.SpecialMdmRecordInfo();
                _commonService.SpecialPersonInCharge();
            }
        }

        /// <summary>
        /// 特殊处理任职记录
        /// </summary>
        [HttpPost("business-object-data/special-record")]
        public void SpecialMdmRecordInfo()
        {
            _commonService.SpecialMdmRecordInfo();
        }

        /// <summary>
        /// 特殊处理部门负责人
        /// </summary>
        [HttpPost("business-object-data/special-person-in-charge")]
        public void SpecialPersonInCharge()
        {
            _commonService.SpecialPersonInCharge();
        }
    }
}
