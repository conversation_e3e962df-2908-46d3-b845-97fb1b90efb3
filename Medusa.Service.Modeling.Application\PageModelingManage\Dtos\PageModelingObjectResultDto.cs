using System.Collections.Generic;
using Medusa.Service.Modeling.Core.Entity;

namespace Medusa.Service.Modeling.Application.PageModelingManage.Dtos
{
    
    
    
    public class PageModelingObjectResultDto
    {
        
        
        
        public List<BusinessObject> BusinessObject { get; set; }

        
        
        
        public List<CompositeObject> CompositeObject { get; set; }

        
        
        
        public List<View> View { get; set; }

        
        
        
        public List<PageModeling> PageModeling { get; set; }
    }
}
