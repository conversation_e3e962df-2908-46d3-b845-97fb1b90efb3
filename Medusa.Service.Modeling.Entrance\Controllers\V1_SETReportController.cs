using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.CRMReport;
using Medusa.Service.Modeling.Application.CRMReport.Dto;
using Medusa.Service.Modeling.Application.DataBaseManage;
using Medusa.Service.Modeling.Application.ProcessRelated;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;
using Org.BouncyCastle.Crypto.Tls;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// CRM报表
    /// </summary>
    [Route("v1/report/set")]
    [ApiExplorerSettings(GroupName = "SETReport.v1")]
    public class V1_SETReportController : ProductControllerBase
    {
        #region //  服务注入
        readonly ICRMReportService _cRMReportService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_SETReportController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="cRMReportService">cRMReportService</param>
        public V1_SETReportController(ICRMReportService cRMReportService)
        {
            _cRMReportService = cRMReportService;
        }
        #endregion

        /// <summary>
        /// 条线图-项目开发各阶段绝对数量
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>条线图</returns>
        [HttpGet("stage-count-bar")]
        public BarChartDto GetStageCount_Bar(string flag = "SET")
        {
            return _cRMReportService.GetStageCount_Bar(flag);
        }

        /// <summary>
        /// 漏斗图-项目开发各阶段绝对数量
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>条线图</returns>
        [HttpGet("stage-count-funnel")]
        public FunnelDto GetStageCount_Funnel(string flag = "SET")
        {
            return _cRMReportService.GetStageCount_Funnel(flag);
        }

        /// <summary>
        /// 漏斗图-项目开发各阶段绝对数量
        /// </summary>
        /// <returns>条线图</returns>
        [HttpGet("product-count-bar")]
        public BarChartDto GetProductTypeCount_Bar()
        {
            return _cRMReportService.GetProductTypeCount_SET_Bar();
        }

        /// <summary>
        ///  产品大类数据源
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("query-product-type")]
        public List<SelectDto> GetProductType(string flag = "SET")
        {
            return _cRMReportService.GetProductType(flag);
        }

        /// <summary>
        ///  产品大类数据源
        /// </summary>
        /// <param name="search">查询参数</param>
        /// <returns>返回</returns>
        [HttpGet("query-stage-count")]
        public List<ReportBaseDto> GetStageCountBySearch_SET([FromQuery] SearchDto search)
        {
            return _cRMReportService.GetStageCountBySearch_SET(search);
        }

        /// <summary>
        ///  项目经理项目数量占比
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("market-count")]
        public List<ReportBaseDto> GetMarketCount(string flag = "SET")
        {
            return _cRMReportService.GetMarketCount(flag);
        }

        /// <summary>
        ///  销售担当项目数量占比
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("sales-count")]
        public List<ReportBaseDto> GetSalesCount(string flag = "SET")
        {
            return _cRMReportService.GetSalesCount(flag);
        }

        /// <summary>
        ///  项目经理项目数量占比
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("query-user")]
        public object GetUsers(string flag = "SET")
        {
            return _cRMReportService.GetUsers(flag);
        }

        /// <summary>
        ///  项目经理项目数量占比
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("sxtype-project-count")]
        public object GetSXTypeProjectCount(string flag = "SET")
        {
            return _cRMReportService.GetSXTypeProjectCount(flag);
        }

        /// <summary>
        ///  项目经理项目数量占比
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("sxtype-customer-count")]
        public object GetSXTypeCustomerCount(string flag = "SET")
        {
            return _cRMReportService.GetSXTypeCustomerCount(flag);
        }

        /// <summary>
        ///  项目区域分布
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("province-count")]
        public object GetProvinceCount(string flag = "SET")
        {
            return _cRMReportService.GetProvinceCount(flag);
        }

        /// <summary>
        ///  销售-产品
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("sales-product-count")]
        public object GetSalesProductCount(string flag = "SET")
        {
            return _cRMReportService.GetSalesProductCount(flag);
        }

        /// <summary>
        ///  市场-产品
        /// </summary>
        /// <param name="flag">标识</param>
        /// <returns>返回</returns>
        [HttpGet("market-product-count")]
        public object GetMarketProductCount(string flag = "SET")
        {
            return _cRMReportService.GetMarketProductCount(flag);
        }
    }
}
