using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.ApplicationMgr.Dtos;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.ModuleMgr.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.Middlewares.UserState;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.ApplicationMgr
{
    
    
    
    public class ApplicationMgrService : ServiceBase, IApplicationMgrService
    {
        #region 
        readonly MyDbContext _dbContext;

        
        
        
        
        
        public ApplicationMgrService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
        }

        #endregion

        
        
        
        
        
        public List<ApplicationDto> GetApplications(ApplicationQueryDto dto)
        {
            SqlSugarClient db = _dbContext.Modeling;
            if (dto.DataBaseId.HasValue)
            {
                var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(dto.DataBaseId.Value);
                if (dataBase == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
                }

                db = CustomDbClient.CustomDB(dataBase);
            }

            var list = db.Queryable<ApplicationEntity>().Where(w => !w.IsDelete)
                .Select(w => new ApplicationDto
                {
                    Id = w.Id,
                    Name = w.Name,
                    Describe = w.Describe,
                    CreateDate = w.CreateDate.Value
                })
                .OrderBy(w => w.CreateDate)
                .ToList();
            if (dto.IsNeedOtherInfo.HasValue && dto.IsNeedOtherInfo.Value)
            {
                var pages = db.Queryable<PageModeling>().ToList();
                var modules = db.Queryable<ModuleEntity>().Where(w => !w.IsDelete).ToList();
                var businessObjects = db.Queryable<BusinessObject>().Where(w => !w.IsDelete.Value).ToList();
                var compositeObjects = db.Queryable<CompositeObject>().Where(w => !w.IsDelete.Value).ToList();
                list.ForEach(f =>
                {
                    f.Pages = pages.Where(w => w.ApplicationId == f.Id).OrderBy(w => w.CreateDate).Select(w => new PageModelingDto
                    {
                        Id = w.Id,
                        Status = w.Status,
                        Type = w.Type,
                        ModuleId = w.ModuleId
                    })
                    .ToList();
                    f.Modules = modules.Where(w => w.ApplicationId == f.Id).OrderBy(w => w.CreateDate).Select(w => new ModuleDto
                    {
                        Id = w.Id,
                        Name = w.Name,
                        ApplicationId = w.ApplicationId,
                        ApplicationName = w.ApplicationName
                    }).ToList();
                    f.Bos = businessObjects.Where(w => SqlFunc.IsNullOrEmpty(w.ApplicationId) || w.ApplicationId.Contains(f.Id.ToString())).OrderBy(w => w.CreateDate).Select(w => new ObjectDto
                    {
                        Id = w.Id,
                        Name = w.Name
                    })
                    .ToList();
                    f.Combos = compositeObjects.Where(w => w.ApplicationId == f.Id.ToString()).Select(w => new CompositeObjectDto
                    {
                        Id = w.Id,
                        Name = w.Name
                    })
                    .ToList();
                });
            }

            return list;
        }

        
        
        
        
        
        public ApplicationDto GetApplication(Guid id)
        {
            var entity = _dbContext.Modeling.Queryable<ApplicationEntity>().InSingle(id);
            if (entity == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.application.notfound"));
            }

            return _dbContext.Modeling.Queryable<ApplicationEntity>().Where(w => w.Id == id)
                .Select(w => new ApplicationDto
                {
                    Id = w.Id,
                    Name = w.Name,
                    Describe = w.Describe,
                    CreateDate = w.CreateDate.Value
                })
                .First();
        }

        
        
        
        
        public void DeleteApplication(Guid id)
        {
            var entity = _dbContext.Modeling.Queryable<ApplicationEntity>().InSingle(id);
            if (entity == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.application.notfound"));
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Updateable<ApplicationEntity>()
                .SetColumns(w => new ApplicationEntity()
                {
                    IsDelete = true,
                    UpdateDate = DateTime.Now,
                    UpdateUser = userId
                })
                .Where(w => w.Id == id).ExecuteCommand();
        }

        
        
        
        
        public void AddApplication(ApplicationDto dto)
        {
            var list = _dbContext.Modeling.Queryable<ApplicationEntity>().Where(w => !w.IsDelete && w.Name == dto.Name).ToList();
            if (list.Count > 0)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.application.exist"));
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Insertable<ApplicationEntity>(new ApplicationEntity()
            {
                Id = dto.Id,
                Name = dto.Name,
                Describe = dto.Describe,
                CreateDate = DateTime.Now,
                CreateUser = userId
            }).ExecuteCommand();
        }

        
        
        
        
        
        public void UpdateApplication(Guid id, ApplicationDto dto)
        {
            var entity = _dbContext.Modeling.Queryable<ApplicationEntity>().InSingle(id);
            if (entity == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.application.notfound"));
            }

            var list = _dbContext.Modeling.Queryable<ApplicationEntity>().Where(w => !w.IsDelete && w.Name == dto.Name && w.Id != id).ToList();
            if (list.Count > 0)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.application.exist"));
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Updateable<ApplicationEntity>()
                 .SetColumns(w => new ApplicationEntity()
                 {
                     Name = dto.Name,
                     Describe = dto.Describe,
                     UpdateDate = DateTime.Now,
                     UpdateUser = userId
                 })
                 .Where(w => w.Id == id)
                .ExecuteCommand();

            _dbContext.Modeling.Updateable<ModuleEntity>()
                .SetColumns(w => new ModuleEntity()
                {
                    ApplicationName = dto.Name
                })
            .Where(w => w.ApplicationId == id).ExecuteCommand();

            _dbContext.Modeling.Updateable<PageModeling>()
                .SetColumns(w => new PageModeling()
                {
                    ApplicationName = dto.Name
                })
            .Where(w => w.ApplicationId == id).ExecuteCommand();
        }
    }
}
