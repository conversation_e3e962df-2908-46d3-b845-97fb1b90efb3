using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 视图字段表
    /// </summary>
    [EntityTable("ViewColumns")]
    public class ViewColumns
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 视图ID
        /// </summary>
        public Guid ViewId { get; set; }

        /// <summary>
        /// 业务对象ID
        /// </summary>
        public Guid? BusinessObjectId { get; set; }

        /// <summary>
        /// 业务对象对象
        /// </summary>
        public string BusinessObjectName { get; set; }

        /// <summary>
        /// 业务对象字段ID
        /// </summary>
        public Guid? BusinessObjectColumnId { get; set; }

        /// <summary>
        /// 列类型：对象列，函数列
        /// </summary>
        public string ColumnType { get; set; }

        /// <summary>
        /// 函数对象类型：table,view
        /// </summary>
        public string FunctionBusinessObjectType { get; set; }

        /// <summary>
        /// 函数对象ID
        /// </summary>
        public Guid? FunctionBusinessObjectId { get; set; }

        /// <summary>
        /// 函数对象名称
        /// </summary>
        public string FunctionBusinessObjectName { get; set; }

        /// <summary>
        /// 函数对象字段ID
        /// </summary>
        public Guid? FunctionBusinessObjectColumnId { get; set; }

        /// <summary>
        /// 函数对象字段名称
        /// </summary>
        public string FunctionBusinessObjectColumnName { get; set; }

        /// <summary>
        /// 函数外键字段ID
        /// </summary>
        public Guid? FunctionForeignKeyBusinessObjectColumnId { get; set; }

        /// <summary>
        /// 函数外键字段名称
        /// </summary>
        public string FunctionForeignKeyBusinessObjectColumnName { get; set; }

        /// <summary>
        /// 函数ID
        /// </summary>
        public string FunctionId { get; set; }

        /// <summary>
        /// 外键对象ID
        /// </summary>
        public Guid? ForeignKeyObjectId { get; set; }

        /// <summary>
        /// 外键对象名称
        /// </summary>
        public string ForeignKeyObjectName { get; set; }

        /// <summary>
        /// 外键对象字段
        /// </summary>
        public Guid? ForeignKeyObjectColumnId { get; set; }

        /// <summary>
        /// 外键对象字段名称
        /// </summary>
        public string ForeignKeyObjectColumnName { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int OrderNumber { get; set; }

        /// <summary>
        /// 字段名称
        /// </summary>
        public string BusinessObjectColumnName { get; set; }

        /// <summary>
        /// 字段别名
        /// </summary>
        public string BusinessObjectColumnAlias { get; set; }

        /// <summary>
        /// 字段描述
        /// </summary>
        public string BusinessObjectColumnDescription { get; set; }

        /// <summary>
        /// 字段类型
        /// </summary>
        public string DisplayType { get; set; }

        /// <summary>
        /// 是否主键
        /// </summary>
        public int IsPrimaryKey { get; set; } = 0;
    }
}
