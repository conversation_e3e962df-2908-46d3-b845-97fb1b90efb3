using System;
using System.Collections.Generic;
using System.Text;
using Medusa.Service.Cache.Entities.Platform;
using Microsoft.VisualBasic;
using MT.Enterprise.Core.ORM;
using NPOI.SS.Formula.Functions;
using static System.Runtime.CompilerServices.RuntimeHelpers;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 日报
    /// </summary>
    [EntityTable("crmdailyreport")]
    public class CRMDailyReport
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 拜访客户
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 拜访客户Id
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 拜访行程-开始
        /// </summary>
        public string StartTime { get; set; }

        /// <summary>
        /// 拜访行程-结束
        /// </summary>
        public string EndTime { get; set; }

        /// <summary>
        /// 拜访人员姓名
        /// </summary>
        public string VisitUserName { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        public string VisitUserPhone { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 拜访目的
        /// </summary>
        public string Purpose { get; set; }

        /// <summary>
        /// 定位打卡信息
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 拜访人员职务
        /// </summary>
        public string VisitUserPosition { get; set; }

        /// <summary>
        /// 拜访内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 创建用户姓名
        /// </summary>
        public string CreateUserName { get; set; }

        /// <summary>
        /// 汇报日期
        /// </summary>
        public string ReportDate { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否推送
        /// </summary>
        public int? IsSend { get; set; }

        /// <summary>
        /// 定位
        /// </summary>
        public string Position { get; set; }

        /// <summary>
        /// 日报ID
        /// </summary>
        public string RBID { get; set; }

        /// <summary>
        /// 日报内容
        /// </summary>
        public string DailyContent { get; set; }

        /// <summary>
        /// 是否出差编码
        /// </summary>
        public string IsOutID { get; set; }

        /// <summary>
        /// 是否出差
        /// </summary>
        public string IsOut { get; set; }

        /// <summary>
        /// 是否推送描述
        /// </summary>
        public string IsSendName { get; set; }

        /// <summary>
        /// 是否推送编码
        /// </summary>
        public string IsSendCode { get; set; }

        /// <summary>
        /// 填报人
        /// </summary>
        public string TBR { get; set; }

        /// <summary>
        /// 填报人对象
        /// </summary>
        public string TBRObject { get; set; }

        /// <summary>
        /// 填报人编码
        /// </summary>
        public string TBRCode { get; set; }

        /// <summary>
        /// 定位打卡信息Code
        /// </summary>
        public string LocationCode { get; set; }

        /// <summary>
        /// 定位打卡信息对象
        /// </summary>
        public string LocationObject { get; set; }

        /// <summary>
        /// 是否已阅
        /// </summary>
        public string IsReadID { get; set; }
    }
}
