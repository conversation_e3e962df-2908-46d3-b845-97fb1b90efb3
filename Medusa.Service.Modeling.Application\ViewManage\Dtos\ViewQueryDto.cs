using System;

namespace Medusa.Service.Modeling.Application.ViewManage.Dtos
{
    
    
    
    public class ViewQueryDto : PageQueryDtoBase
    {
        
        
        
        public Guid? Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public Guid? ApplicationId { get; set; }
    }
}
