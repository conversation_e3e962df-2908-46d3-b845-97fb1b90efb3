using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Core.Enums
{
    /// <summary>
    /// 流程状态枚举
    /// </summary>
    public enum ProcInstStatusEnums
    {
        /// <summary>
        /// 通过
        /// </summary>
        Approved,

        /// <summary>
        /// 审批中
        /// </summary>
        Processing,

        /// <summary>
        /// 待发起
        /// </summary>
        Ready,

        /// <summary>
        /// 退回处理
        /// </summary>
        Rejected,

        /// <summary>
        /// 拒绝
        /// </summary>
        Refused,

        /// <summary>
        /// 作废
        /// </summary>
        Canceled
    }
}
