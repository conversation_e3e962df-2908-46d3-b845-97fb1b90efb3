using System;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.DataBaseManage;
using Medusa.Service.Modeling.Application.DataBaseManage.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// DataBases controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "DataBaseManage.v1")]
    public class V1_DataBaseManageController : ProductControllerBase
    {
        #region //  服务注入
        readonly IDataBaseManageService _dataBaseManageService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_DataBaseManageController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="dataBaseManageService">dataBaseManageService</param>
        public V1_DataBaseManageController(IDataBaseManageService dataBaseManageService)
        {
            _dataBaseManageService = dataBaseManageService;
        }
        #endregion

        /// <summary>
        /// 获取数据库信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("data-bases")]
        public PageResult<DataBaseDto> GetDataBases([FromQuery] DataBaseQueryDto dto)
        {
            return _dataBaseManageService.GetDataBases(dto);
        }

        /// <summary>
        /// 新增/修改获取数据库信息
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("data-bases/save")]
        [ModelFieldCheck]
        public void SaveDataBase([FromBody] DataBaseDto dto)
        {
            _dataBaseManageService.SaveDataBase(dto);
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("data-bases/test")]
        public void Test([FromBody] DataBaseDto dto)
        {
            _dataBaseManageService.Test(dto);
        }

        /// <summary>
        /// 获取数据库下的对象信息
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("data-bases/{id}/object")]
        public PageResult<ObjectResultDto> GetObject([FromRoute]Guid id, [FromQuery] ObjectQueryDto dto)
        {
            return _dataBaseManageService.GetObject(id, dto);
        }

        /// <summary>
        /// 对象加载到数据库
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        [HttpPost("data-bases/{id}/object/load")]
        public void ObjectLoad([FromRoute] Guid id, [FromBody] ObjectLoadDto dto)
        {
            _dataBaseManageService.ObjectLoad(id, dto);
        }
    }
}
