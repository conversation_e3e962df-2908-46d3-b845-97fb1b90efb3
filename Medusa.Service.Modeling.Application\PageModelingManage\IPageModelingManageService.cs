using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.Authority.Dtos;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using NPOI.SS.UserModel;

namespace Medusa.Service.Modeling.Application.PageModelingManage
{
    
    
    
    public interface IPageModelingManageService : IServiceBase
    {
        #region 建模

        
        
        
        
        
        PageModelingDto GetPageModeling(Guid id);

        
        
        
        
        
        
        PageModelingDto GetPageModelingPreview(Guid id, Guid versionId);

        
        
        
        
        
        PageResult<PageModelingDto> GetPageModelings(PageModelingQueryDto dto);

        
        
        
        
        
        Guid SavePageModeling(PageModelingDto dto);

        
        
        
        
        
        Guid UpdatePageModelingDesgin(PageModelingDto dto);

        
        
        
        
        void Import(ExportImportTemplateDto dto);

        
        
        
        
        void DeletePageModelings(Guid id);

        
        
        
        
        
        List<ObjectGroupDto> GetBusinessObjects(ObjectQueryDto dto);

        
        
        
        
        
        List<ObjectStructureDto> GetBusinessObjectFields(ObjectColumnQueryDto dto);

        
        
        
        
        void CopyPageModeling(PageModelingDto dto);

        
        
        
        
        
        IWorkbook ExportImportTemplate(ExportImportTemplateDto dto);

        
        
        
        
        void Undercarriage(Guid id);

        
        
        
        
        void Publish(Guid id);
        #endregion

        
        
        
        
        
        
        
        (string, string, string, string) GenClass([FromQuery] string tableName, [FromQuery] string namespaceKey, [FromBody] DataBase db);

        
        
        
        
        
        
        PageResult<PageModelingVersionDto> GetPageModelingVersions(Guid id, PageQueryDtoBase dto);

        
        
        
        
        
        List<PageModelingDto> GetPageModelingVersions(List<string> id);

        
        
        
        
        void CreateVersion(PageModelingVersionDto dto);

        
        
        
        
        void PublicVersion(PageModelingVersionDto dto);

        
        
        
        
        void UseVersion(PageModelingVersionDto dto);

        
        
        
        
        
        Guid UpdatePageModeling(PageModelingDto dto);

        
        
        
        
        
        PageModelingObjectResultDto GetPageModelingObjects(List<PageModelingDto> dto);

        
        
        
        
        
        JObject Sync(PageModelingSyncDto dto);
    }
}
