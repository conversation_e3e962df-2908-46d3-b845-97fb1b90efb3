using System;
using Medusa.Service.Modeling.Application.ModuleMgr.Dtos;

namespace Medusa.Service.Modeling.Application.ModuleMgr
{
    
    
    
    public interface IModuleMgrService : IServiceBase
    {
        
        
        
        
        void DeleteModule(Guid id);

        
        
        
        
        void AddModule(ModuleDto dto);

        
        
        
        
        
        void UpdateModule(Guid id, ModuleDto dto);
    }
}
