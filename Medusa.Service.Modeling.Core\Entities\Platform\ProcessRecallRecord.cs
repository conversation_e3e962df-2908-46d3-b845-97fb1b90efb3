using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 流程撤回记录
    /// </summary>
    [EntityTable("ProcessRecallRecord")]
    public class ProcessRecallRecord
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// DataBaseId
        /// </summary>
        public Guid DataBaseId { get; set; }

        /// <summary>
        /// BusinessObjectId
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// BusinessObjectName
        /// </summary>
        public string BusinessObjectName { get; set; }

        /// <summary>
        /// DataId
        /// </summary>
        public Guid DataId { get; set; }

        /// <summary>
        /// Data
        /// </summary>
        public string Data { get; set; }

        /// <summary>
        /// CreateDate
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Btid
        /// </summary>
        public string Btid { get; set; }

        /// <summary>
        /// Boid
        /// </summary>
        public string Boid { get; set; }

        /// <summary>
        /// Bsid
        /// </summary>
        public string Bsid { get; set; }
    }
}
