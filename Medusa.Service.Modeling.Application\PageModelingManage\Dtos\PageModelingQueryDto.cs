using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.PageModelingManage.Dtos
{
    
    
    
    public class PageModelingQueryDto : PageQueryDtoBase
    {
        
        
        
        public Guid? Key { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public Guid? ApplicationId { get; set; }

        
        
        
        public Guid? ModuleId { get; set; }

        
        
        
        public int? Status { get; set; }

        
        
        
        public string ApplicationEquipment { get; set; }

        
        
        
        public string VersionType { get; set; }

        
        
        
        public string ObjectId { get; set; }

        
        
        
        public string ObjectType { get; set; }
    }
}
