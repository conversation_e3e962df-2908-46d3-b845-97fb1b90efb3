using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using Medusa.Service.Cache;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.AppConfig;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Entrance.Development;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using MT.Enterprise.Core;
using MT.Enterprise.Core.Cache;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Core.OperationLog;
using MT.Enterprise.Core.ORM;
using MT.Enterprise.SDK;
using MT.Enterprise.SDK.ApiRequest;
using MT.Enterprise.SDK.Gateway;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Nacos;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Entrance
{
    /// <summary>
    /// Startup.
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="configuration">Configuration</param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// 获取配置
        /// </summary>
        /// <value>The configuration.</value>
        public IConfiguration Configuration { get; }

        private JObject AppConfigDto
        {
            get;
            set;
        }

        /// <summary>
        /// 配置服务
        /// </summary>
        /// <param name="services">Services.</param>
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMemoryCache();

            services.AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    // 使用 iso 格式化日期
                    options.SerializerSettings.DateFormatString = Clock.GetFormat(Clock.ClockType.DateTime);
                    options.SerializerSettings.DateParseHandling = Newtonsoft.Json.DateParseHandling.None;
                });

            var nacosConfig = Configuration.GetSection("Nacos");
            services.AddNacos(configure =>
            {
                configure.DefaultTimeOut = nacosConfig["DefaultTimeOut"].ToOurInt();
                configure.ServerAddresses = nacosConfig.GetSection("ServerAddresses").Get<string[]>().ToList();
                configure.Namespace = nacosConfig["Namespace"];
                configure.ListenInterval = nacosConfig["ListenInterval"].ToOurInt();
            });

            AppConfigDto = CreateRegister(services, nacosConfig);

            // 添加 Core
            services.AddCore(core =>
            {
                core.AddI18n(opt =>
                {
                    opt.Languages = GetI18Languages(services, nacosConfig);
                    opt.DefaultLang = AppConstants.I18nDefaultLang;
                });

                core.AddORM(orm =>
                {
                    Enum.TryParse(AppConfigDto["Persistence"]?["DbType"]?.ToString(), true, out ConnectionDbType dbType);
                    orm.AddDbContext(opt =>
                    {
                        opt.Name = "modeling";
                        opt.ConnectionString = AppConfigDto["Persistence"]?["ConnectionString"]?.ToString();
                        opt.ConnectionDbType = dbType;
                        opt.IsDefault = true;
                    });

                    Enum.TryParse(AppConfigDto["Boost"]?["DbType"]?.ToString(), true, out ConnectionDbType dbType1);
                    orm.AddDbContext(opt =>
                    {
                        opt.Name = "Boost";
                        opt.ConnectionString = AppConfigDto["Boost"]?["ConnectionString"]?.ToString();
                        opt.ConnectionDbType = dbType1;
                        opt.IsDefault = true;
                    });
                });

                core.AddCache(opt =>
                {
                    opt.RedisHost = AppConfigDto["Redis"]?["Host"]?.ToString();
                    opt.RedisDb = Convert.ToInt32(AppConfigDto["Redis"]?["DB"]);
                    opt.Password = AppConfigDto["Redis"]?["Password"]?.ToString();
                });
            });

            // 添加 SDK
            services.AddSDK(sdk =>
            {
                sdk.AddGateway(opt =>
                {
                    opt.Host = AppConfigDto["ApiGateway"]?["Host"]?.ToString();
                    opt.Port = Convert.ToInt32(AppConfigDto["ApiGateway"]?["Port"]);
                    opt.AppKey = AppConfigDto["ApiGateway"]?["AppKey"]?.ToString();
                    opt.AppSecret = AppConfigDto["ApiGateway"]?["AppSecret"]?.ToString();
                    opt.Timeout = Convert.ToInt32(AppConfigDto["ApiGateway"]?["Timeout"]);
                });
                sdk.AddApiRequest(opt =>
                {
                    opt.Host = AppConfigDto["ApiRequest"]?["Host"]?.ToString();
                    opt.Db = Convert.ToInt32(AppConfigDto["ApiRequest"]?["DB"]);
                    opt.Password = AppConfigDto["ApiRequest"]?["Password"]?.ToString();
                    opt.Timeout = Convert.ToInt32(AppConfigDto["ApiRequest"]?["Timeout"]);
                    opt.IsHttps = (AppConfigDto["ApiRequest"]?["IsHttps"].Value<bool>()).Value;
                });
            });

            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "服务列表", Version = "v1" });

                // 如果没标注版本，或标注的版本包含当前版本，则显示该API
                options.DocInclusionPredicate((docname, description) => description.GroupName?.Split(".").Last() == docname);

                // 添加文档
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Medusa.Service.Modeling.Entrance.xml"));
                options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, "Medusa.Service.Modeling.Application.xml"));

                // 添加默认头信息
                options.OperationFilter<CustomHeaderFilter>();

                // Api 分组
                options.TagActionsBy(api => { return new List<string> { api.GroupName }; });
                options.CustomOperationIds(api => $"{api.ActionDescriptor.RouteValues["controller"]}.{api.ActionDescriptor.RouteValues["action"]}");
                options.IgnoreObsoleteActions();
                options.IgnoreObsoleteProperties();
            });

            // application service 注入
            services.AddCacheServices();
            services.AddApplicationServices();

            // AutoMapper 注册
            services.AddMappers(options =>
            {
                options.AddApplicationMappers();
            });
        }

        /// <summary>
        /// Configure the specified app and env.
        /// </summary>
        /// <param name="app">App.</param>
        /// <param name="env">Env.</param>
        /// <param name="appConfigService">appConfigService</param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IAppConfigService appConfigService)
        {
            var cultureInfo = new CultureInfo("zh-CN")
            {
                DateTimeFormat =
                {
                    ShortDatePattern = "yyyy-MM-dd",
                    LongTimePattern = "HH:mm:ss"
                }
            };
            var supportedCultures = new[]
            {
                cultureInfo,
                new CultureInfo("en-US"),
            };
            app.UseRequestLocalization(new RequestLocalizationOptions
            {
                DefaultRequestCulture = new RequestCulture(AppConstants.I18nDefaultLang),
                SupportedCultures = supportedCultures,
                SupportedUICultures = supportedCultures
            });

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseErrorHandling(true);
            }

            appConfigService.AppConfigCache(AppConfigDto);

            app.UseUserState();
            app.UseGatewayConnectorState();
            app.UseRequestConnectorState();
            app.UseRouting();

            app.UseSwagger(options => { options.RouteTemplate = "swagger/{documentName}/swagger.json"; });
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "Api List - v1");
                options.DisplayOperationId();
                options.DisplayRequestDuration();
                options.EnableDeepLinking();
                options.EnableFilter();
            });

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }

        /// <summary>
        /// CreateRegister
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="section">section</param>
        /// <returns>JObject</returns>
        private JObject CreateRegister(IServiceCollection services, IConfigurationSection section)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (!string.IsNullOrEmpty(environmentName) && (environmentName == "Local" || environmentName == "Development"))
            {
                string config = string.Empty;
                if (environmentName == "Development")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Development.json"));
                }
                else if (environmentName == "Local")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Local.json"));
                }

                return JObject.Parse(config);
            }
            else
            {
                var serviceProvider = services.BuildServiceProvider();
                var configClient = serviceProvider.GetService<INacosConfigClient>();

                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = section["DataId"],
                    Group = section["GroupId"],
                }).Result;

                if (!string.IsNullOrEmpty(res))
                {
                    var appSettings = JObject.Parse(res);
                    return appSettings;
                }

                throw new Exception("Not Found Nacos!");
            }
        }

        /// <summary>
        /// 获取I18多语言文件
        /// </summary>
        /// <param name="services">services</param>
        /// <param name="section">section</param>
        /// <returns>Dictionary</returns>
        private Dictionary<string, string> GetI18Languages(IServiceCollection services, IConfigurationSection section)
        {
            var result = new Dictionary<string, string>();

            var serviceProvider = services.BuildServiceProvider();
            var configClient = serviceProvider.GetService<INacosConfigClient>();

            var languageIds = section.GetSection("LanguageIds").Get<string[]>().ToList();

            foreach (var languageId in languageIds)
            {
                var matched = Regex.Matches(languageId, @"([A-Za-z0-9-_]+)").ToList();
                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = languageId,
                    Group = section["LanguageGroupId"],
                }).Result;
                result.Add(matched[0].ToString(), res);
            }

            return result;
        }
    }
}
