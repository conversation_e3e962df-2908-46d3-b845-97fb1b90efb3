using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 客户
    /// </summary>
    [EntityTable("crmcustomer")]
    public class CRMCustomer
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 创建用户组
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        public string No { get; set; }

        /// <summary>
        /// 客户类型编码
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 客户类型名称
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// 客户重要性
        /// </summary>
        public string Importance { get; set; }

        /// <summary>
        /// 客户重要性
        /// </summary>
        public string ImportanceName { get; set; }

        /// <summary>
        /// 销售担当对象
        /// </summary>
        public string SalesObject { get; set; }

        /// <summary>
        /// 销售担当ID
        /// </summary>
        public string SalesUserId { get; set; }

        /// <summary>
        /// 销售担当姓名
        /// </summary>
        public string SalesName { get; set; }

        /// <summary>
        /// 市场担当对象
        /// </summary>
        public string MarketObject { get; set; }

        /// <summary>
        /// 市场担当用户
        /// </summary>
        public string MarketUserId { get; set; }

        /// <summary>
        /// 市场担当姓名
        /// </summary>
        public string MarketName { get; set; }

        /// <summary>
        /// 是否上市编码
        /// </summary>
        public string IsList { get; set; }

        /// <summary>
        /// 是否上市
        /// </summary>
        public string IsListName { get; set; }

        /// <summary>
        /// 省份
        /// </summary>
        public string Province { get; set; }

        /// <summary>
        /// 省份名称
        /// </summary>
        public string ProvinceName { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 城市名称
        /// </summary>
        public string CityName { get; set; }

        /// <summary>
        /// 基地
        /// </summary>
        public string BaseArea { get; set; }

        /// <summary>
        /// 所有权
        /// </summary>
        public string Ownership { get; set; }

        /// <summary>
        /// 所有权名称
        /// </summary>
        public string OwnershipName { get; set; }

        /// <summary>
        /// 产能GW
        /// </summary>
        public string CapacityGW { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否推送
        /// </summary>
        public int? IsSend { get; set; }
    }
}
