using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.OperationLog.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Core.OperationLog;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.OperationLog
{
    
    
    
    public class OperationLogService : ServiceBase, IOperationLogService
    {
        #region 
        readonly MyDbContext _dbContext;

        
        
        
        
        
        public OperationLogService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
        }

        #endregion

        
        
        
        
        
        public async Task WriteLog(WriteLogDto dto)
        {
            var log = new OperationLogEntity();
            log.LogId = Guid.NewGuid();
            log.Menu = dto.Menu;
            log.Action = dto.Action;
            log.MainTableName = dto.MainTableName;
            log.MainTableDataId = dto.MainTableDataId;
            log.TableName = dto.TableName;
            log.TableDataId = dto.TableDataId;
            StringBuilder sb = new StringBuilder();
            sb.Append("{");
            sb.Append($"\"operationType\":\"{dto.OperationType}\"");
            if (!string.IsNullOrEmpty(dto.TableName))
            {
                sb.Append($",\"operationTable\":\"{dto.TableName}\"");
            }

            if (dto.Data != null && dto.Data.Count > 0)
            {
                sb.Append($",\"operationData\":\"{dto.Data.ToOurJsonString()}\"");
            }

            if (!string.IsNullOrEmpty(dto.OperationCondition))
            {
                sb.Append($",\"operationCondition\":\"{dto.OperationCondition}\"");
            }

            sb.Append("}");
            List<string> co = new List<string>();
            if (dto.Data != null && dto.Data.Count > 0)
            {
                var oc = _dbContext.Modeling.Queryable<BusinessObject, BusinessObjectColumn, DataBase>((a, b, c) => a.Id == b.BusinessObjectId && a.DataBaseId == c.Id)
                    .Where((a, b, c) => a.Name == dto.TableName && !a.IsDelete.Value && c.OnlyCode == "Default")
                    .Select((a, b, c) => b)
                    .ToList();
                dto.Data.Keys.ToList().ForEach(f =>
                {
                    var c = oc.Find(o => o.Name == f);
                    if (c != null && (!c.IsSystemColumn.HasValue || !c.IsSystemColumn.Value))
                    {
                        co.Add($"{(!string.IsNullOrEmpty(c.Description) ? c.Description : c.Name)}:{dto.Data[f]}");
                    }
                });
            }

            log.DataContent = sb.ToString();
            log.DataDescription = string.Join(",", co);
            log.CreateUserId = UserConstants.CurrentUser.Value.Id;
            log.CreateUserName = UserConstants.CurrentUser.Value.Name;
            log.CreateUserLoginId = UserConstants.CurrentUser.Value.Account;
            log.CreateTime = DateTime.Now;
            await _dbContext.Modeling.Insertable(log).ExecuteCommandAsync();
        }

        
        
        
        
        
        public async Task<PageResult<LogResultDto>> GetLog(LogQueryDto dto)
        {
            var ids = dto.MainTableDataId.Split(',');
            RefAsync<int> total = 0;
            var query1 = _dbContext.Modeling.Queryable<OperationLogEntity, BusinessObject>((a, b) => a.TableName == b.Name)
                        .Where((a, b) => a.MainTableName == dto.MainTableName && ids.Contains(a.MainTableDataId) && !SqlFunc.IsNullOrEmpty(a.DataDescription))
                        .Select((a, b) => new LogResultDto
                        {
                            Action = a.Action,
                            CreateTime = a.CreateTime,
                            CreateUserName = a.CreateUserName,
                            DataDescription = a.DataDescription,
                            Menu = a.Menu,
                            TableName = SqlFunc.IF(SqlFunc.IsNullOrEmpty(b.Description)).Return(b.Name).End(b.Description)
                        });
            var query2 = _dbContext.Modeling.Queryable<OperationLogEntity, OperationLogEntity, BusinessObject>((a, b, c) => a.TableName == b.TableName && a.TableDataId == b.TableDataId && b.TableName == c.Name)
                        .Where((a, b, c) => a.MainTableName == dto.MainTableName && ids.Contains(a.MainTableDataId) && !SqlFunc.IsNullOrEmpty(b.DataDescription))
                          .Select((a, b, c) => new LogResultDto
                          {
                              Action = b.Action,
                              CreateTime = b.CreateTime,
                              CreateUserName = b.CreateUserName,
                              DataDescription = b.DataDescription,
                              Menu = b.Menu,
                              TableName = SqlFunc.IF(SqlFunc.IsNullOrEmpty(c.Description)).Return(c.Name).End(c.Description)
                          });
            var unionQuery = _dbContext.Modeling.Union(query1, query2)
                .OrderBy(a => a.CreateTime);

            var items = dto.IsAll ? await unionQuery.ToListAsync() : await unionQuery.ToPageListAsync(dto.PageIndex, dto.PageSize, total);

            return new PageResult<LogResultDto>
            {
                Items = items,
                Total = total
            };
        }
    }
}
