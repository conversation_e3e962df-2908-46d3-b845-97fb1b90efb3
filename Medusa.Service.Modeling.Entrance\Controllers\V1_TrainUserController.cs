using System;
using Medusa.Service.Modeling.Application.EOPMessage.Dto;
using Medusa.Service.Modeling.Application.TrainUser.Dto;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;
using MMedusa.Service.Modeling.Application.EOPMessage;
using MMedusa.Service.Modeling.Application.TrainUser;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// CRM消息
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "TrainUser.v1")]
    [IgnoreAntiforgeryToken]
    public class V1_TrainUserController : ProductControllerBase
    {
        private readonly ITrainUserService _trainUserService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="trainUserService">eOPMessageService</param>
        public V1_TrainUserController(ITrainUserService trainUserService)
        {
            _trainUserService = trainUserService;
        }

        /// <summary>
        /// _cRMReportService
        /// </summary>
        public ITrainUserService P_trainUserService => _trainUserService;

        /// <summary>
        /// 客户信息更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("ppt")]
        public TrainUserDto CustmerMsg()
        {
            return _trainUserService.GetTrainUserFiles();
        }
    }
}
