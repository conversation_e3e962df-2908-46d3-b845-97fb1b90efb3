using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// MdmCustomer
    /// </summary>
    public class MdmCustomer
    {
        /// <summary>
        /// 客户编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 销售人员
        /// </summary>
        public string SalesUserId { get; set; }

        /// <summary>
        /// SalesUserName
        /// </summary>
        public string SalesUserName { get; set; }

        /// <summary>
        /// MarketUserId
        /// </summary>
        public string MarketUserId { get; set; }

        /// <summary>
        /// MarketUserName
        /// </summary>
        public string MarketUserName { get; set; }
    }
}
