using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.PageModelingManage.Dtos
{
    
    
    
    public class ObjectQueryDto
    {
        
        
        
        public Guid? Id { get; set; }

        
        
        
        public Guid DataBaseId { get; set; }

        
        
        
        public Guid ApplicationId { get; set; }

        
        
        
        public string ObjectType { get; set; }

        
        
        
        public bool? IsHasColumns { get; set; }

        
        
        
        public string HasColumnObjectId { get; set; }

        
        
        
        public bool? IsTreeObject { get; set; }
    }
}
