using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// CRM任务表
    /// </summary>
    [SugarTable("crmtask")]
    public class Crmtask
    {
        /// <summary>
        /// 任务主键ID
        /// </summary>
        [SugarColumn(ColumnName = "taskId", IsPrimaryKey = true)]
        public Guid TaskId { get; set; }

        /// <summary>
        /// 模板应用历史ID
        /// </summary>
        [SugarColumn(ColumnName = "templateHistoryId")]
        public Guid TemplateHistoryId { get; set; }

        /// <summary>
        /// 任务标题
        /// </summary>
        [SugarColumn(ColumnName = "taskTitle")]
        public string TaskTitle { get; set; }

        /// <summary>
        /// 负责人员id
        /// </summary>
        [SugarColumn(ColumnName = "responsiblePersonnel")]
        public Guid ResponsiblePersonnel { get; set; }

        /// <summary>
        /// 负责人员名称
        /// </summary>
        [SugarColumn(ColumnName = "responsiblePersonnel_Name")]
        public string ResponsiblePersonnel_Name { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [SugarColumn(ColumnName = "startTime")]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [SugarColumn(ColumnName = "endTime")]
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 任务状态 1已完成,0未完成
        /// </summary>
        [SugarColumn(ColumnName = "taskStatus")]
        public bool TaskStatus { get; set; }

        /// <summary>
        /// 任务描述
        /// </summary>
        [SugarColumn(ColumnName = "taskDescription")]
        public string TaskDescription { get; set; }

        /// <summary>
        /// 是否删除 1已删除,0未删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [SugarColumn(ColumnName = "createdUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "createdDate")]
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// 修改用户ID
        /// </summary>
        [SugarColumn(ColumnName = "modifiedUserId")]
        public string ModifiedUserId { get; set; }
    }
}
