using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 视图过滤表
    /// </summary>
    [EntityTable("ViewFilters")]
    public class ViewFilters
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 视图ID
        /// </summary>
        public Guid ViewId { get; set; }

        /// <summary>
        /// 条件名称
        /// </summary>
        public string FilterName { get; set; }

        /// <summary>
        /// 业务对象ID
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// 业务对象字段ID
        /// </summary>
        public Guid BusinessObjectColumnId { get; set; }

        /// <summary>
        /// 操作符
        /// </summary>
        public string Judge { get; set; }

        /// <summary>
        /// 过滤源：固定值-FixedValue，字典表-Dictionary
        /// </summary>
        public string FilterSource { get; set; }

        /// <summary>
        /// 过滤值
        /// </summary>
        public string FilterValue { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int OrderNumber { get; set; }
    }
}
