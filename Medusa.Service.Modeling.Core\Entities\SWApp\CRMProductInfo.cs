using System;
using System.Collections.Generic;
using System.Text;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 送样产品导入跟进记录表
    /// </summary>
    public class CRMProductInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 内测完成时间
        /// </summary>
        public string TestEndDate { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 内测送样时间
        /// </summary>
        public string TestStartDate { get; set; }

        /// <summary>
        /// 是否可批量供货
        /// </summary>
        public string IsBatch { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 中大样时间
        /// </summary>
        public string LargeSampleDate { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 认证搭配
        /// </summary>
        public string AuthCollocation { get; set; }

        /// <summary>
        /// 认证完成时间
        /// </summary>
        public string AuthEndDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 认证送样时间
        /// </summary>
        public string AuthStartDate { get; set; }

        /// <summary>
        /// 小批量时间
        /// </summary>
        public string SmallLotDate { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 进度跟踪
        /// </summary>
        public string ProcessTrack { get; set; }

        /// <summary>
        /// 样品送样进度跟进表ID
        /// </summary>
        public string SampleId { get; set; }

        /// <summary>
        /// 售前产品导入Id
        /// </summary>
        public string ProductImportId { get; set; }

        /// <summary>
        /// 是否批量
        /// </summary>
        public string IsBatchNmae { get; set; }

        /// <summary>
        /// 事业部标识
        /// </summary>
        public string Sybbs { get; set; }
    }
}
