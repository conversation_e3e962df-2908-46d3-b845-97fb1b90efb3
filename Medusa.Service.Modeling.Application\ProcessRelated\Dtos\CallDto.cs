using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class CallDto
    {
        
        
        
        public string Btid { get; set; }

        
        
        
        public string Boid { get; set; }

        
        
        
        public string Bsid { get; set; }

        
        
        
        public string InstanceNumber { get; set; }

        
        
        
        public string InstanceTopic { get; set; }

        
        
        
        public string ProcessId { get; set; }

        
        
        
        public string ProcessName { get; set; }

        
        
        
        public string StartUserName { get; set; }

        
        
        
        public string StartUserAccount { get; set; }

        
        
        
        public string OwnerUserAccount { get; set; }

        
        
        
        public string OwnerUserName { get; set; }

        
        
        
        public string StartTime { get; set; }

        
        
        
        public string Status { get; set; }

        
        
        
        public string StatusName { get; set; }

        
        
        
        public string DetailStatus { get; set; }

        
        
        
        public string DetailStatusName { get; set; }

        
        
        
        public string InstanceStatus { get; set; }

        
        
        
        public string InstanceStatusName { get; set; }

        
        
        
        public object Users { get; set; }

        
        
        
        public object Ccusers { get; set; }

        
        
        
        public object BusinessData { get; set; }

        
        
        
        public object ActivityExtension { get; set; }

        
        
        
        public object InstanceExtension { get; set; }

        
        
        
        public bool? VirtualObjectDataWriteback { get; set; }

        
        
        
        public string ProcessVersion { get; set; }
    }
}
