using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.DataBaseManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewEngines;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Utils.Extensions;
using MySqlX.XDevAPI.Common;
using MySqlX.XDevAPI.Relational;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Utilities;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.DataBaseManage
{
    
    
    
    public class DataBaseManageService : ServiceBase, IDataBaseManageService
    {
        #region 
        readonly MyDbContext _dbContext;
        private readonly IStructureCacheService _structureCacheService;

        
        
        
        
        
        public DataBaseManageService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _structureCacheService = serviceProvider.GetService<IStructureCacheService>();
        }

        #endregion

        
        
        
        
        
        public DataBaseDto GetDataBase(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<DataBase>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            return item.MapTo<DataBaseDto>();
        }

        
        
        
        
        
        public PageResult<DataBaseDto> GetDataBases(DataBaseQueryDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<DataBase>()
                   .WhereIF(!string.IsNullOrEmpty(dto.Name), x => x.Name.Contains(dto.Name))
                   .WhereIF(!string.IsNullOrEmpty(dto.Localhost), x => x.Localhost.Contains(dto.Localhost))
                   .WhereIF(!string.IsNullOrEmpty(dto.Type), x => x.Type == dto.Type)
                   .WhereIF(dto.IsOutSideBusinessBase.HasValue, x => x.IsOutSideBusinessBase == dto.IsOutSideBusinessBase)
                   .OrderBy(x => x.CreateDate, SqlSugar.OrderByType.Desc)
                   .Select(x => new DataBaseDto
                   {
                       Id = x.Id,
                       OnlyCode = x.OnlyCode,
                       Name = x.Name,
                       Account = x.Account,
                       Description = x.Description,
                       Localhost = x.Localhost,
                       Type = x.Type,
                       Password = x.Password,
                       IsOutSideBusinessBase = x.IsOutSideBusinessBase,
                       Port = x.Port
                   });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<DataBaseDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        public void SaveDataBase(DataBaseDto dto)
        {
            if (dto.Id == Guid.Empty)
            {
                var hasData = _dbContext.Modeling.Queryable<DataBase>().Where(w => w.OnlyCode == dto.OnlyCode).Any();
                if (hasData)
                {
                    throw new StatusNotFoundException("数据库唯一编码已存在");
                }

                var item = dto.MapTo<DataBase>();
                item.Id = Guid.NewGuid();
                DateTime now = DateTime.Now;
                item.CreateDate = now;
                _dbContext.Modeling.Insertable(item).ExecuteCommand();
            }
            else
            {
                var item = _dbContext.Modeling.Queryable<DataBase>().InSingle(dto.Id);
                if (item == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
                }

                item.Account = dto.Account;
                item.Description = dto.Description;
                item.Localhost = dto.Localhost;
                item.Name = dto.Name;
                item.Password = dto.Password;
                item.IsOutSideBusinessBase = dto.IsOutSideBusinessBase;
                item.Port = dto.Port;
                _dbContext.Modeling.Updateable(item).ExecuteCommand();
            }
        }

        
        
        
        
        public void Test(DataBaseDto dto)
        {
            try
            {
                var item = dto.MapTo<DataBase>();
                using (SqlSugarClient client = CustomDbClient.CustomDB(item))
                {
                    client.Open();
                }
            }
            catch
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.checkDB"));
            }
        }

        
        
        
        
        
        
        public PageResult<ObjectResultDto> GetObject(Guid id, ObjectQueryDto dto)
        {
            var item = _dbContext.Modeling.Queryable<DataBase>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            List<ObjectResultDto> result = new List<ObjectResultDto>();
            SqlSugarClient client = CustomDbClient.CustomDB(item);
            if (client != null)
            {
                if (dto.Type == "view")
                {
                    var views = client.DbMaintenance.GetViewInfoList(false);
                    var yyViews = _dbContext.Modeling.Queryable<View>().Where(a => a.DatabaseId == id).ToList();
                    result = views.FindAll(t => yyViews.FindIndex(f => f.Name.ToOurLowerString() == t.Name.ToOurLowerString()) == -1)
                        .WhereIF(!string.IsNullOrEmpty(dto.Name), s => s.Name.Contains(dto.Name))
                        .WhereIF(!string.IsNullOrEmpty(dto.Desc), s => s.Description.Contains(dto.Desc))
                        .Select(s => new ObjectResultDto { Name = s.Name, Description = s.Name }).ToList();
                }
                else if (dto.Type == "table")
                {
                    var tables = client.DbMaintenance.GetTableInfoList(false);
                    var yyTables = _dbContext.Modeling.Queryable<BusinessObject>().Where(a => a.DataBaseId == id).ToList();
                    result = tables.FindAll(t => yyTables.FindIndex(f => f.Name.ToOurLowerString() == t.Name.ToOurLowerString()) == -1)
                        .WhereIF(!string.IsNullOrEmpty(dto.Name), s => s.Name.Contains(dto.Name))
                        .WhereIF(!string.IsNullOrEmpty(dto.Desc), s => s.Description != null && s.Description.Contains(dto.Desc))
                        .Select(s => new ObjectResultDto { Name = s.Name, Description = s.Description }).ToList();
                }
            }

            return new PageResult<ObjectResultDto>
            {
                Items = result,
                Total = result.Count
            };
        }

        
        
        
        
        
        public void ObjectLoad(Guid id, ObjectLoadDto dto)
        {
            var item = _dbContext.Modeling.Queryable<DataBase>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var objectNames = new List<string>();
            dto.ObjecNames.ToList().ForEach(f => objectNames.Add(f.ToOurLowerString()));

            SqlSugarClient client = CustomDbClient.CustomDB(item);
            if (client != null)
            {
                if (dto.Type == "view")
                {
                    var redisKey = new List<Guid>();
                    var insert = new List<View>();
                    var update = new List<View>();
                    var insertColumns = new List<ViewColumns>();
                    var updateColumns = new List<ViewColumns>();
                    DateTime now = DateTime.Now;
                    var views = client.DbMaintenance.GetViewInfoList(false);
                    var yyViews = _dbContext.Modeling.Queryable<View>().Where(a => a.DatabaseId == id).ToList();
                    views = views.FindAll(t => objectNames.Contains(t.Name.ToOurLowerString()));
                    views.ForEach(t =>
                    {
                        var yyView = yyViews.Find(f => f.Name.ToOurLowerString() == t.Name.ToOurLowerString());
                        var gid = yyView != null ? yyView.Id : Guid.NewGuid();
                        if (yyView == null)
                        {
                            var view = new View();
                            view.Id = gid;
                            view.Name = t.Name;
                            view.Description = t.Name;
                            view.DatabaseId = id;
                            view.State = 2;
                            view.IsCustom = false;
                            view.CreateDate = now;
                            insert.Add(view);
                            if (!redisKey.Contains(gid))
                            {
                                redisKey.Add(gid);
                            }
                        }
                        else
                        {
                            yyView.Description = t.Name;
                            update.Add(yyView);
                            if (!redisKey.Contains(gid))
                            {
                                redisKey.Add(gid);
                            }
                        }

                        var yyViewColumns = _dbContext.Modeling.Queryable<ViewColumns>().Where(a => a.ViewId == gid).ToList();
                        var viewColumns = client.DbMaintenance.GetColumnInfosByTableName(t.Name, false);
                        int index = 0;
                        viewColumns.ForEach(c =>
                        {
                            var yyViewColumn = yyViewColumns.Find(f => f.BusinessObjectColumnName.ToOurLowerString() == c.DbColumnName.ToOurLowerString());
                            if (yyViewColumn == null)
                            {
                                var viewColumn = new ViewColumns();
                                viewColumn.Id = Guid.NewGuid();
                                viewColumn.ViewId = gid;
                                viewColumn.BusinessObjectColumnName = c.DbColumnName;
                                viewColumn.BusinessObjectColumnDescription = c.ColumnDescription ?? c.DbColumnName;
                                viewColumn.BusinessObjectColumnAlias = c.DbColumnName;
                                viewColumn.ColumnType = "ObjectColumn";
                                viewColumn.DisplayType = DbTypeHelper.DbTypeFormat(item.Type, c.DataType);
                                viewColumn.OrderNumber = index;
                                viewColumn.IsPrimaryKey = index == 0 ? 1 : 0;
                                insertColumns.Add(viewColumn);
                                if (!redisKey.Contains(gid))
                                {
                                    redisKey.Add(gid);
                                }
                            }
                            else
                            {
                                yyViewColumn.BusinessObjectColumnDescription = c.ColumnDescription ?? c.DbColumnName;
                                yyViewColumn.OrderNumber = index;
                                yyViewColumn.IsPrimaryKey = index == 0 ? 1 : 0;
                                updateColumns.Add(yyViewColumn);
                                if (!redisKey.Contains(gid))
                                {
                                    redisKey.Add(gid);
                                }
                            }

                            index++;
                        });
                    });

                    if (insertColumns.Count > 0)
                    {
                        _dbContext.Modeling.Insertable(insertColumns).ExecuteCommand();
                    }

                    if (insert.Count > 0)
                    {
                        _dbContext.Modeling.Insertable(insert).ExecuteCommand();
                    }

                    if (updateColumns.Count > 0)
                    {
                        _dbContext.Modeling.Updateable(updateColumns).ExecuteCommand();
                    }

                    if (update.Count > 0)
                    {
                        _dbContext.Modeling.Updateable(update).ExecuteCommand();
                    }

                    redisKey.ForEach(f =>
                    {
                        _structureCacheService.SetRedis(new StructureCache.Dtos.StructureCacheSetDto()
                        {
                            BusinessObjectId = f,
                            BusinessObjectType = 1
                        });
                    });
                }
                else if (dto.Type == "table")
                {
                    var redisKey = new List<Guid>();
                    var insert = new List<BusinessObject>();
                    var update = new List<BusinessObject>();
                    var insertColumns = new List<BusinessObjectColumn>();
                    var updateColumns = new List<BusinessObjectColumn>();
                    DateTime now = DateTime.Now;
                    var tables = client.DbMaintenance.GetTableInfoList(false);
                    var yyTables = _dbContext.Modeling.Queryable<BusinessObject>().Where(a => a.DataBaseId == id).ToList();
                    tables = tables.FindAll(t => objectNames.Contains(t.Name.ToOurLowerString()));
                    tables.ForEach(t =>
                    {
                        var yyTable = yyTables.Find(f => f.Name.ToOurLowerString() == t.Name.ToOurLowerString());
                        var gid = yyTable != null ? yyTable.Id : Guid.NewGuid();
                        if (yyTable == null)
                        {
                            var businessObject = new BusinessObject();
                            businessObject.Id = gid;
                            businessObject.Name = t.Name;
                            businessObject.Description = t.Description ?? t.Name;
                            businessObject.DataBaseId = id;
                            businessObject.CreateDate = now;
                            businessObject.IsCustom = false;
                            businessObject.State = 1;
                            businessObject.IsDelete = false;
                            insert.Add(businessObject);
                            if (!redisKey.Contains(gid))
                            {
                                redisKey.Add(gid);
                            }
                        }
                        else
                        {
                            yyTable.Description = t.Description ?? t.Name;
                            update.Add(yyTable);
                            if (!redisKey.Contains(gid))
                            {
                                redisKey.Add(gid);
                            }
                        }

                        var yyTableColumns = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == gid).ToList();
                        var tableColumns = client.DbMaintenance.GetColumnInfosByTableName(t.Name, false);
                        int index = 0;
                        tableColumns.ForEach(c =>
                        {
                            var yyTableColumn = yyTableColumns.Find(f => f.Name.ToOurLowerString() == c.DbColumnName.ToOurLowerString());
                            if (yyTableColumn == null)
                            {
                                var businessObjectColumn = new BusinessObjectColumn();
                                businessObjectColumn.Id = Guid.NewGuid();
                                businessObjectColumn.Name = c.DbColumnName;
                                businessObjectColumn.Description = c.ColumnDescription ?? c.DbColumnName;
                                businessObjectColumn.Length = c.Length;
                                businessObjectColumn.IsPrimaryKey = c.IsPrimarykey;
                                businessObjectColumn.IsNullable = c.IsNullable;
                                businessObjectColumn.Decimal = c.DecimalDigits;
                                businessObjectColumn.Type = c.DataType;
                                businessObjectColumn.BusinessObjectId = gid;
                                businessObjectColumn.CreateDate = now;
                                businessObjectColumn.DisplayType = DbTypeHelper.DbTypeFormat(item.Type, c.DataType);
                                businessObjectColumn.IsIdentity = c.IsIdentity;
                                businessObjectColumn.IsEnable = true;
                                businessObjectColumn.IsSystemColumn = false;
                                businessObjectColumn.Order = index;
                                insertColumns.Add(businessObjectColumn);
                                if (!redisKey.Contains(gid))
                                {
                                    redisKey.Add(gid);
                                }
                            }
                            else
                            {
                                yyTableColumn.Length = c.Length;
                                yyTableColumn.Description = c.ColumnDescription ?? c.DbColumnName;
                                yyTableColumn.Order = index;
                                updateColumns.Add(yyTableColumn);
                                if (!redisKey.Contains(gid))
                                {
                                    redisKey.Add(gid);
                                }
                            }

                            index++;
                        });
                    });

                    if (insertColumns.Count > 0)
                    {
                        _dbContext.Modeling.Insertable(insertColumns).ExecuteCommand();
                    }

                    if (insert.Count > 0)
                    {
                        _dbContext.Modeling.Insertable(insert).ExecuteCommand();
                    }

                    if (updateColumns.Count > 0)
                    {
                        _dbContext.Modeling.Updateable(updateColumns).ExecuteCommand();
                    }

                    if (update.Count > 0)
                    {
                        _dbContext.Modeling.Updateable(update).ExecuteCommand();
                    }

                    redisKey.ForEach(f =>
                    {
                        _structureCacheService.SetRedis(new StructureCache.Dtos.StructureCacheSetDto()
                        {
                            BusinessObjectId = f,
                            BusinessObjectType = 1
                        });
                    });
                }
            }
        }
    }
}
