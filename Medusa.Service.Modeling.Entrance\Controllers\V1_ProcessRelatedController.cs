using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;
using Medusa.Service.Modeling.Application.Authority.Dtos;
using Medusa.Service.Modeling.Application.ProcessRelated;
using Medusa.Service.Modeling.Application.ProcessRelated.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// DataBases controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "ProcessRelated.v1")]
    public class V1_ProcessRelatedController : ProductControllerBase
    {
        #region //  服务注入
        readonly IProcessRelatedService _processRelatedService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_ProcessRelatedController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="processRelatedService">processRelatedService</param>
        public V1_ProcessRelatedController(IProcessRelatedService processRelatedService)
        {
            _processRelatedService = processRelatedService;
        }
        #endregion

        /// <summary>
        /// 获取外部业务对象
        /// </summary>
        /// <param name="sysCode">sysCode</param>
        /// <param name="dataBaseId">dataBaseId</param>
        /// <returns>returns</returns>
        [HttpGet("process-related/outside-business-object/{sysCode}")]
        public List<BusinessObjectItemDto> GetOutsideBusinessObjects([FromRoute] string sysCode, [FromQuery(Name = "data-base-id")]Guid dataBaseId)
        {
            return _processRelatedService.GetOutsideBusinessObjects(sysCode, dataBaseId);
        }

        /// <summary>
        /// 获取外部业务对象结构
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="type">type</param>
        /// <returns>returns</returns>
        [HttpGet("process-related/outside-business-object-structure/{id}")]
        public OutsideBusinessObjectStructureDto GetOutsideBusinessObjectStructure([FromRoute] Guid id, [FromQuery]int type)
        {
            return _processRelatedService.GetOutsideBusinessObjectStructure(id, type);
        }

        /// <summary>
        /// 保存外部业务对象数据
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("process-related/outside-business-object-data/save")]
        public void SaveOutsideBusinessObjectData([FromBody]OutsideBusinessObjectDataDto dto)
        {
            _processRelatedService.SaveOutsideBusinessObjectData(dto);
        }

        /// <summary>
        /// 获取外部业务对象具体数据
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("process-related/outside-business-object/data")]
        public Dictionary<string, object> GetBusinessObjectData(OutsideBusinessObjectDataSearchDto dto)
        {
            return _processRelatedService.GetBusinessObjectData(dto);
        }

        /// <summary>
        /// 发起流程
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("process-related/start")]
        public async Task<string> Start([FromBody] PorcessDto dto)
        {
            return await _processRelatedService.Start(dto);
        }

        /// <summary>
        /// 撤回流程
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("process-related/recall")]
        public async Task Recall([FromBody] PorcessDto dto)
        {
            await _processRelatedService.Recall(dto);
        }

        /// <summary>
        /// 作废流程
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("process-related/cancel")]
        public async Task Cancel([FromBody] PorcessDto dto)
        {
            await _processRelatedService.Cancel(dto);
        }

        ///// <summary>
        ///// 流程发起回调
        ///// </summary>
        ///// <param name="dto">dto</param>
        // [HttpPost("process-related/call-receive-start")]
        // public void CallReceiveStart([FromBody] CallDto dto)
        // {
        //    _processRelatedService.CallReceiveRecord(dto, "start");
        // }

        ///// <summary>
        ///// 流程审批回调
        ///// </summary>
        ///// <param name="dto">dto</param>
        // [HttpPost("process-related/call-receive-approve")]
        // public void CallReceiveApprove([FromBody] CallDto dto)
        // {
        //    _processRelatedService.CallReceiveRecord(dto, "approve");
        // }

        ///// <summary>
        ///// 流程结束回调
        ///// </summary>
        ///// <param name="dto">dto</param>
        // [HttpPost("process-related/call-receive-end")]
        // public void CallReceiveEnd([FromBody] CallDto dto)
        // {
        //    _processRelatedService.CallReceiveRecord(dto, "end");
        // }

        /// <summary>
        /// 外部业务对象校验相关
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("process-related/outside-verification")]
        public async Task<List<ExpandoObject>> OutsideVerification(OutsideVerificationDto dto)
        {
            return await _processRelatedService.OutsideVerification(dto);
        }
    }
}
