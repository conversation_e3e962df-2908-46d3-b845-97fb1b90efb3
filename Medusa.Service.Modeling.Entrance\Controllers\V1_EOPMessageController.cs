using System;
using Medusa.Service.Modeling.Application.EOPMessage.Dto;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;
using MMedusa.Service.Modeling.Application.EOPMessage;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// CRM消息
    /// </summary>
    [Route("v1/msg")]
    [ApiExplorerSettings(GroupName = "EOPMessage.v1")]
    [IgnoreAntiforgeryToken]
    public class V1_EOPMessageController : ProductControllerBase
    {
        private readonly IEOPMessageService _eOPMessageService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="eOPMessageService">eOPMessageService</param>
        public V1_EOPMessageController(IEOPMessageService eOPMessageService)
        {
            _eOPMessageService = eOPMessageService;
        }

        /// <summary>
        /// _cRMReportService
        /// </summary>
        public IEOPMessageService P_EOPMessageService => _eOPMessageService;

        /// <summary>
        /// 客户信息更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("customer")]
        public string CustmerMsg()
        {
            return _eOPMessageService.CustmerMsg();
        }

        /// <summary>
        /// 销售线索更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("salesclue")]
        public string SalesClueMsg()
        {
            return _eOPMessageService.SalesClueMsg();
        }

        /// <summary>
        /// 送样更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("sample")]
        public string SampleMsg()
        {
            return _eOPMessageService.SampleMsg();
        }

        /// <summary>
        /// 定时消息-【到达时间节点后，如果送样结果未填写，每天提醒一次】
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("sample-job")]
        public string SampleJobMsg()
        {
            return _eOPMessageService.SampleJobMsg();
        }

        /// <summary>
        /// 产品导入跟进更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("productimport")]
        public string ProductImportMsg()
        {
            return _eOPMessageService.ProductImportMsg();
        }

        /// <summary>
        /// 月度台账更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("monthaccount")]
        public string MonthAccountMsg()
        {
            return _eOPMessageService.MonthAccountMsg();
        }

        /// <summary>
        /// 日常台账更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("daliyaccount")]
        public string DaliyAccountMsg()
        {
            return _eOPMessageService.DaliyAccountMsg();
        }

        /// <summary>
        /// 日报更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("daliyreport")]
        public string DailyReportMsg()
        {
            return _eOPMessageService.DailyReportMsg();
        }

        /// <summary>
        /// 月报更改消息通知
        /// </summary>
        /// <returns>返回</returns>
        [HttpPost("monthreport")]
        public string MonthReportMsg()
        {
            return _eOPMessageService.MonthReportMsg();
        }
    }
}
