using System;
using System.Collections.Generic;
using System.Text;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 月度台账
    /// </summary>
    [EntityTable("crmmonthaccount")]
    public class CRMMonthAccount
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 客户等级编码
        /// </summary>
        public string CustomerLevel { get; set; }

        /// <summary>
        /// 背板库存
        /// </summary>
        public decimal? BBStock { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 月总产能MW
        /// </summary>
        public string MonthMW { get; set; }

        /// <summary>
        /// 计划产量 MW
        /// </summary>
        public string PlanMW { get; set; }

        /// <summary>
        /// 本月已排订单金额
        /// </summary>
        public decimal? CurrentMonthOrderAmount { get; set; }

        /// <summary>
        /// 胶膜采购量
        /// </summary>
        public decimal? JMOrderQuantity { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public string Month { get; set; }

        /// <summary>
        /// 销售担当用户名
        /// </summary>
        public string SalesUserId { get; set; }

        /// <summary>
        /// 开工率
        /// </summary>
        public decimal? StartsRate { get; set; }

        /// <summary>
        /// 使用量
        /// </summary>
        public decimal? SWConsumption { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 销售担当对象
        /// </summary>
        public string SalesUserObject { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 月出货计划1
        /// </summary>
        public decimal? MonthPlan1 { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 单玻MW
        /// </summary>
        public string SingleMW { get; set; }

        /// <summary>
        /// 产品类别编码
        /// </summary>
        public string SWCategory { get; set; }

        /// <summary>
        /// 占有率
        /// </summary>
        public decimal? Prop { get; set; }

        /// <summary>
        /// 三新类型
        /// </summary>
        public string SXTypeName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 三新类型编码
        /// </summary>
        public string SXType { get; set; }

        /// <summary>
        /// 产品类别
        /// </summary>
        public string JPProductCategoryName { get; set; }

        /// <summary>
        /// 竞品数量
        /// </summary>
        public decimal? JPNum { get; set; }

        /// <summary>
        /// 产品类型编码
        /// </summary>
        public string JPProductCategory { get; set; }

        /// <summary>
        /// 客户等级
        /// </summary>
        public string CustomerLevelName { get; set; }

        /// <summary>
        /// 赛伍占比
        /// </summary>
        public decimal? SWProp { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 发货数量
        /// </summary>
        public decimal? ShipmentNum { get; set; }

        /// <summary>
        /// 胶膜库存
        /// </summary>
        public decimal? JMStock { get; set; }

        /// <summary>
        /// 销售担当
        /// </summary>
        public string SalesUserName { get; set; }

        /// <summary>
        /// 双玻MW
        /// </summary>
        public string DoubleMW { get; set; }

        /// <summary>
        /// 竞品占比
        /// </summary>
        public decimal? JPProp { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 组件库存MW
        /// </summary>
        public string AssemblyMW { get; set; }

        /// <summary>
        /// 月出货计划2
        /// </summary>
        public decimal? MonthPlan2 { get; set; }

        /// <summary>
        /// 本月已发货金额
        /// </summary>
        public decimal? CurrentMonthShipmentAmount { get; set; }

        /// <summary>
        /// 产品类别
        /// </summary>
        public string SWCategoryName { get; set; }

        /// <summary>
        /// 本月已排计划数量
        /// </summary>
        public decimal? CurrentMonthPlanNum { get; set; }

        /// <summary>
        /// 月需求
        /// </summary>
        public decimal? MonthlyDemand { get; set; }

        /// <summary>
        /// 背板采购量
        /// </summary>
        public decimal? BBOrderQuantity { get; set; }

        /// <summary>
        /// 事业部标识
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否推送
        /// </summary>
        public int? IsSend { get; set; }
    }
}
