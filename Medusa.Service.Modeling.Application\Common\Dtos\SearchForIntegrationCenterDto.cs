using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Application.Common.Dtos
{
    
    
    
    public class SearchForIntegrationCenterDto
    {
        
        
        
        public string ObjectType { get; set; } = "table";

        
        
        
        public string ObjectDataBaseCode { get; set; } = "Default";

        
        
        
        public string Name { get; set; }

        
        
        
        public bool IsAll { get; set; } = false;

        
        
        
        public int Page { get; set; } = 1;

        
        
        
        public int PageSize { get; set; } = 10;

        
        
        
        public JObject OrderBy { get; set; }

        
        
        
        public JObject Where { get; set; }
    }
}
