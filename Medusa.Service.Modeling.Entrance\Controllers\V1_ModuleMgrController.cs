using System;
using Medusa.Service.Modeling.Application.ModuleMgr;
using Medusa.Service.Modeling.Application.ModuleMgr.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// ModuleMgr controller
    /// </summary>
    [Route("v1/module")]
    [ApiExplorerSettings(GroupName = "ModuleMgr.v1")]
    public class V1_ModuleMgrController : ProductControllerBase
    {
        #region //  服务注入
        readonly IModuleMgrService _moduleMgrService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_ModuleMgrController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="moduleMgrService">moduleMgrService</param>
        public V1_ModuleMgrController(IModuleMgrService moduleMgrService)
        {
            _moduleMgrService = moduleMgrService;
        }
        #endregion

        /// <summary>
        /// 删除模块
        /// </summary>
        /// <param name="id">id</param>
        [HttpDelete("{id}")]
        public void DeleteModule([FromRoute] Guid id)
        {
            _moduleMgrService.DeleteModule(id);
        }

        /// <summary>
        /// 新增模块
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost]
        public void AddModule([FromBody] ModuleDto dto)
        {
            _moduleMgrService.AddModule(dto);
        }

        /// <summary>
        /// 修改模块
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        [HttpPut("{id}")]
        public void UpdateModule([FromRoute] Guid id, [FromBody] ModuleDto dto)
        {
            _moduleMgrService.UpdateModule(id, dto);
        }
    }
}
