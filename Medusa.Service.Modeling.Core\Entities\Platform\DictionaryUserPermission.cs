using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 字典人员权限
    /// </summary>
    [EntityTable("DictionaryUserPermissions")]
    public class DictionaryUserPermission
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        public string UserLoginId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 字典Id
        /// </summary>
        public Guid DictionaryId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
    }
}
