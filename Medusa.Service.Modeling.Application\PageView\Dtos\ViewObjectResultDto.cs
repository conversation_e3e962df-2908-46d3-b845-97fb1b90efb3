using System;
using System.Collections.Generic;

namespace Medusa.Service.Modeling.Application.PageView.Dtos
{
    
    
    
    public class ViewObjectResultDto
    {
        
        
        
        public string ObjectType { get; set; }

        
        
        
        public Guid ObjectId { get; set; }

        
        
        
        public string ObjectName { get; set; }

        
        
        
        public string ObjectDescription { get; set; }

        
        
        
        public Guid? CompositeObjectId { get; set; }

        
        
        
        public Guid? ParentCompositeObjectId { get; set; }

        
        
        
        public bool IsMain { get; set; } = false;

        
        
        
        public Guid? ParentObjectId { get; set; }

        
        
        
        public Guid? RelationFieldId { get; set; }

        
        
        
        public Guid? ParentRelationFieldId { get; set; }

        
        
        
        public int? JoinType { get; set; }

        
        
        
        public int? JoinRelation { get; set; }

        
        
        
        public List<ViewObjectFieldDto> Fields { get; set; }

        
        
        
        public object ExtraCondition { get; set; }
    }
}
