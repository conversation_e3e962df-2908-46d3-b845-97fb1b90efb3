using Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos;
using Medusa.Service.Modeling.Core.Entity;

namespace Medusa.Service.Modeling.Application.CompositeObjectManage.Profile
{
    
    
    
    public class CompositeObjectManageProfile : AutoMapper.Profile
    {
        
        
        
        public CompositeObjectManageProfile()
        {
            CreateMap<CompositeObjectRelation, CompositeObjectRelationDto>();
            CreateMap<CompositeObjectRelationDto, CompositeObjectRelation>();
            CreateMap<CompositeObject, CompositeObjectDto>();
            CreateMap<CompositeObjectDto, CompositeObject>();
        }
    }
}
