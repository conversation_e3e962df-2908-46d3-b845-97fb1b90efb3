using System;
using System.Collections.Generic;
using System.Text;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.CRMTasks.Dtos
{
    
    
    
    public class SearchTaskTemplateDto : PageQueryDtoBase
    {
        
        
        
        public string Templatename { get; set; }

        
        
        
        public string AppliedDepartmentIds { get; set; }

        
        
        
        public string AppliedPersonnelIds { get; set; }

        
        
        
        public DateTime? CreatedDateStart { get; set; }

        
        
        
        public DateTime? CreatedDateEnd { get; set; }
    }
}
