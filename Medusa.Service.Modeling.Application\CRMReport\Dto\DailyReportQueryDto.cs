using System;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Medusa.Service.Modeling.Application.CRMReport.Dto
{
    
    
    
    public class DailyReportQueryDto : PageQueryDtoBase
    {
        
        
        
        [FromQuery(Name = "depname")]
        [JsonProperty(PropertyName = "depname")]
        public string DepName { get; set; }

        
        
        
        [FromQuery(Name = "salesname")]
        [JsonProperty(PropertyName = "salesname")]
        public string SalesName { get; set; }

        
        
        
        [FromQuery(Name = "startdate")]
        [JsonProperty(PropertyName = "startdate")]
        public DateTime? StartDate { get; set; }

        
        
        
        [FromQuery(Name = "enddate")]
        [JsonProperty(PropertyName = "enddate")]
        public DateTime? EndDate { get; set; }
    }
}
