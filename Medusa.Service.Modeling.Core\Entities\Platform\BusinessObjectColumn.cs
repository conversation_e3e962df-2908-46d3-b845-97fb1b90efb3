using System;
using MT.Enterprise.Core.ORM;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 业务对象列信息
    /// </summary>
    [EntityTable("BusinessObjectColumns")]
    public partial class BusinessObjectColumn
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 字段名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 字段描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 字段长度
        /// </summary>
        public int? Length { get; set; }

        /// <summary>
        /// 字段类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 字段是否主键
        /// </summary>
        public bool? IsPrimaryKey { get; set; }

        /// <summary>
        /// 字段是否允许空值
        /// </summary>
        public bool? IsNullable { get; set; }

        /// <summary>
        /// BusinessObjectsId
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// 小数点
        /// </summary>
        public int? Decimal { get; set; }

        /// <summary>
        /// 显示类型
        /// </summary>
        public string DisplayType { get; set; }

        /// <summary>
        /// 字段是否自增
        /// </summary>
        public bool? IsIdentity { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsEnable { get; set; }

        /// <summary>
        /// 是否系统内置列
        /// </summary>
        public bool? IsSystemColumn { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 旧名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string OldName { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public string DefaultValue { get; set; }
    }
}
