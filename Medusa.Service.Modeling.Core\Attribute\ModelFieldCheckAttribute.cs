using System.Linq;
using Microsoft.AspNetCore.Mvc.Filters;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;

namespace Medusa.Service.Modeling.Core
{
    /// <summary>
    /// 字段检查
    /// </summary>
    public class ModelFieldCheckAttribute : ActionFilterAttribute
    {
        /// <summary>
        /// 重写
        /// </summary>
        /// <param name="context">上下文</param>
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            if (!context.ModelState.IsValid)
            {
                var modelState = context.ModelState.FirstOrDefault(f => f.Value.Errors.Any());
                throw new StatusBadRequestException(I18nManager.GetString("ModelCheck", I18nManager.GetString($"modeling.fields.{modelState.Key}")));
            }
        }
    }
}
