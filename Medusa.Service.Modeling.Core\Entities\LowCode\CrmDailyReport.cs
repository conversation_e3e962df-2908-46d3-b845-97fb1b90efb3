using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// CRM任务表
    /// </summary>
    [SugarTable("crmdailyreport")]
    public class CrmDailyReport
    {
        /// <summary>
        /// 任务主键ID
        /// </summary>
        [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
        public Guid TaskId { get; set; }

        /// <summary>
        /// 客户名称/客户公司名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 联系人/拜访人
        /// </summary>
        public string VisitUserName { get; set; }

        /// <summary>
        /// 联系人/拜访人电话
        /// </summary>
        public string VisitUserPhone { get; set; }

        /// <summary>
        /// 联系人/拜访人职位
        /// </summary>
        public string VisitUserPosition { get; set; }

        /// <summary>
        /// 跟进时间？
        /// </summary>
        public string ReportDate { get; set; }

        /// <summary>
        /// 是否提交
        /// </summary>
        public int IsSend { get; set; }

        /// <summary>
        /// 跟进类型,中文显示，现场拜访，电话拜访
        /// </summary>
        public string GJLX { get; set; }

        /// <summary>
        /// 1=拜访客户
        /// </summary>
        public int IsOutID { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUserId { get; set; }
    }
}
