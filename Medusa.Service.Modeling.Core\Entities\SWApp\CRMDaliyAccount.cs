using System;
using System.Collections.Generic;
using System.Text;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>>
    /// 日常台账
    /// </summary>
    [EntityTable("crmdaliyaccount")]
    public class CRMDaliyAccount
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 计划拜访频率
        /// </summary>
        public string VisitFrequency { get; set; }

        /// <summary>
        /// 销售额达成率
        /// </summary>
        public decimal? SalesRate { get; set; }

        /// <summary>
        /// 未税单价RMB
        /// </summary>
        public decimal? SWWSRMB { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 实际订单数量
        /// </summary>
        public decimal? ActualOrderNum { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? MonthlyQuantity { get; set; }

        /// <summary>
        /// 支援人用户名
        /// </summary>
        public string Supporter { get; set; }

        /// <summary>
        /// 实际占比%
        /// </summary>
        public decimal? ActualProp { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 支援部门对象
        /// </summary>
        public string SupportDeptObject { get; set; }

        /// <summary>
        /// 客户端当月总需求
        /// </summary>
        public decimal? TotalDemand { get; set; }

        /// <summary>
        /// 支援部门编码
        /// </summary>
        public string SupportDept { get; set; }

        /// <summary>
        /// 组件出货市场
        /// </summary>
        public string Shipment { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 产品别
        /// </summary>
        public string CompetitiveName { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 当月月度预测
        /// </summary>
        public string MonthlyForecast { get; set; }

        /// <summary>
        /// 竞品新动向
        /// </summary>
        public string CompetitiveNewTrend { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 客户端痛点
        /// </summary>
        public string PainSpot { get; set; }

        /// <summary>
        /// 支援部门
        /// </summary>
        public string SupportDeptName { get; set; }

        /// <summary>
        /// 客户现用SW产品型号
        /// </summary>
        public string SWModel { get; set; }

        /// <summary>
        /// 占比
        /// </summary>
        public decimal? CompetitiveProp { get; set; }

        /// <summary>
        /// 实际回款
        /// </summary>
        public decimal? ActualCollection { get; set; }

        /// <summary>
        /// 支援人
        /// </summary>
        public string SupporterName { get; set; }

        /// <summary>
        /// 支援人对象
        /// </summary>
        public string SupporterObject { get; set; }

        /// <summary>
        /// 月份
        /// </summary>
        public string Month { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public string SWProduct { get; set; }

        /// <summary>
        /// 回款目标
        /// </summary>
        public decimal? PCTarget { get; set; }

        /// <summary>
        /// 销售线索Id
        /// </summary>
        public string SalesClueId { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public string CompletionTime { get; set; }

        /// <summary>
        /// 未税单价RMB
        /// </summary>
        public decimal? CompetitiveWSRMB { get; set; }

        /// <summary>
        /// 目标占比%
        /// </summary>
        public decimal? TargetProp { get; set; }

        /// <summary>
        /// 实际拜访频率
        /// </summary>
        public string ActualVisitFrequency { get; set; }

        /// <summary>
        /// 建议解决方案
        /// </summary>
        public string Solution { get; set; }

        /// <summary>
        /// 客户端当月采购量
        /// </summary>
        public decimal? TotalProcurement { get; set; }

        /// <summary>
        /// 销售线索
        /// </summary>
        public string SalesClueName { get; set; }

        /// <summary>
        /// 赛伍问题点
        /// </summary>
        public string Problem { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 实际订单总金额
        /// </summary>
        public decimal? ActualOrderAmount { get; set; }

        /// <summary>
        /// 回款达成率
        /// </summary>
        public decimal? CollectionRate { get; set; }

        /// <summary>
        /// 客户新动向
        /// </summary>
        public string NewTrend { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 三新类型编码
        /// </summary>
        public string SXType { get; set; }

        /// <summary>
        /// 三新类型
        /// </summary>
        public string SXTypeName { get; set; }

        /// <summary>
        /// 销售担当对象
        /// </summary>
        public string SalesUserObject { get; set; }

        /// <summary>
        /// 销售担当ID
        /// </summary>
        public string SalesUserId { get; set; }

        /// <summary>
        /// 销售担当
        /// </summary>
        public string SalesUserName { get; set; }

        /// <summary>
        /// 地区
        /// </summary>
        public string BaseArea { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否推送
        /// </summary>
        public int? IsSend { get; set; }
    }
}
