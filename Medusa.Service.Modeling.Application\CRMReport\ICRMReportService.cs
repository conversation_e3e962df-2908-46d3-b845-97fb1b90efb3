using System;
using System.Collections.Generic;
using System.Data;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos;
using Medusa.Service.Modeling.Application.CRMReport.Dto;
using NPOI.SS.UserModel;

namespace Medusa.Service.Modeling.Application.CRMReport
{
    
    
    
    public interface ICRMReportService : IServiceBase
    {
        
        
        
        
        
        List<ReportBaseDto> GetStageCount(string flag);

        
        
        
        
        
        BarChartDto GetStageCount_Bar(string flag);

        
        
        
        
        
        FunnelDto GetStageCount_Funnel(string flag);

        
        
        
        
        BarChartDto GetProductTypeCount_SET_Bar();

        
        
        
        
        
        List<SelectDto> GetProductType(string flag);

        
        
        
        
        
        List<ReportBaseDto> GetStageCountBySearch_SET(SearchDto search);

        
        
        
        
        
        BarChartDto GetStageCountBySearch_SET_Bar(SearchDto search);

        
        
        
        
        
        List<ReportBaseDto> GetMarketCount(string flag);

        
        
        
        
        
        List<ReportBaseDto> GetSalesCount(string flag);

        
        
        
        
        
        object GetUsers(string flag);

        
        
        
        
        
        List<SXTypeCountDto> GetSXTypeProjectCount(string flag);

        
        
        
        
        
        List<SXTypeCountDto> GetSXTypeCustomerCount(string flag);

        
        
        
        
        
        List<ReportBaseDto> GetProvinceCount(string flag);

        
        
        
        
        
        List<User_ProductDto> GetMarketProductCount(string flag);

        
        
        
        
        
        List<User_ProductDto> GetSalesProductCount(string flag);

        
        
        
        
        
        Tuple<List<string>, DataTable, int> GetDailyReport(DailyReportQueryDto dto);

        
        
        
        
        
        IWorkbook ExportDaily(DailyReportQueryDto dto);
    }
}
