using System;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class PorcessDto
    {
        
        
        
        public Guid ModalKey { get; set; }

        
        
        
        public Guid DataBaseId { get; set; }

        
        
        
        public Guid MainTableId { get; set; }

        
        
        
        public string MainTableName { get; set; }

        
        
        
        public string[] StartUserId { get; set; }

        
        
        
        public string[] OrganizationId { get; set; }

        
        
        
        public string Bsid { get; set; }

        
        
        
        public string Btid { get; set; }

        
        
        
        public string Boid { get; set; }

        
        
        
        public string CurrentUserId { get; set; }

        
        
        
        public string CurrentUserName { get; set; }

        
        
        
        public string CurrentUserOrganizationId { get; set; }

        
        
        
        public string BusinessObjectId { get; set; }

        
        
        
        public string CallBackSetting { get; set; }

        
        
        
        public string Menu { get; set; }

        
        
        
        public string Action { get; set; }
    }
}
