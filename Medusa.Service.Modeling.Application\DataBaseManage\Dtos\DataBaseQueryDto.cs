using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Medusa.Service.Modeling.Application.DataBaseManage.Dtos
{
    
    
    
    public class DataBaseQueryDto : PageQueryDtoBase
    {
        
        
        
        public string Name { get; set; }

        
        
        
        public string Localhost { get; set; }

        
        
        
        public string Type { get; set; }

        
        
        
        [FromQuery(Name = "is-outside-business-base")]
        [JsonProperty(PropertyName = "is-outside-business-base")]
        public bool? IsOutSideBusinessBase { get; set; }
    }
}
