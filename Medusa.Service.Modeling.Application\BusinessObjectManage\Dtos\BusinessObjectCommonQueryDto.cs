using System;
using System.Collections.Generic;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos
{
    
    
    
    public class BusinessObjectCommonQueryDto : PageQueryDtoBase
    {
        
        
        
        public bool? IsOr { get; set; }

        
        
        
        public string Wheres { get; set; }

        
        
        
        public bool? IsDelete { get; set; }

        
        
        
        public DateTime? ModifyDateBegin { get; set; }

        
        
        
        public DateTime? ModifyDateEnd { get; set; }
    }
}
