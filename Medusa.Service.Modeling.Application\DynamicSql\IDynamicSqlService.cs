using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.DynamicSql.Dtos;
using Medusa.Service.Modeling.Application.ViewManage.Dtos;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using NPOI.SS.UserModel;

namespace Medusa.Service.Modeling.Application.DynamicSql
{
    
    
    
    public interface IDynamicSqlService : IServiceBase
    {
        
        
        
        
        
        
        JObject Query(JObject jObject, Guid databaseId);

        
        
        
        
        
        void Update(JObject jObject, Guid databaseId);

        
        
        
        
        
        
        IWorkbook Export(JObject jObject, Guid databaseId);

        
        
        
        
        
        
        JObject InitView(string dbType, ViewDto dto);

        
        
        
        
        
        ViewFunctionDto GetFunctionColumnResult(ViewFunctionDto paras);

        
        
        
        
        
        
        JArray SetFunctionSelect(string functionSelect, JArray data);

        
        
        
        
        
        JObject Query(DynamicQueryDto dto);
    }
}
