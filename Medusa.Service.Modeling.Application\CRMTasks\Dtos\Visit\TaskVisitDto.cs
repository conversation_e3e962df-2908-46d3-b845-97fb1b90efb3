using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.CRMTasks.Dtos
{
    
    
    
    public class TaskVisitDto
    {
        
        
        
        public int Num { get; set; }

        
        
        
        public Guid? Id { get; set; }

        
        
        
        public Guid? TaskId { get; set; }

        
        
        
        public string CustomerName { get; set; }

        
        
        
        public string ContactPerson { get; set; }

        
        
        
        public string ContactPosition { get; set; }

        
        
        
        public string ContactNumber { get; set; }

        
        
        
        public string ContactDepartment { get; set; }

        
        
        
        public DateTime? ContactTime { get; set; }

        
        
        
        public string Remark { get; set; }

        
        
        
        public int Status { get; set; }

        
        
        
        public virtual TaskTypeEnum TaskType { get; set; }
    }
}
