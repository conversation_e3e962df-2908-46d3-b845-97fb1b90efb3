using System;

namespace Medusa.Service.Modeling.Application.ProductRegistration.Dto
{
    
    
    
    public class LicenseDto
    {
        
        
        
        public Guid? ProductId { get; set; }

        
        
        
        public string ProductName { get; set; }

        
        
        
        public string ProductVersion { get; set; }

        
        
        
        public string CustomerName { get; set; }

        
        
        
        public string MacAddress { get; set; }

        
        
        
        public int? LicenseType { get; set; }

        
        
        
        public DateTime? ExpirationDate { get; set; }

        
        
        
        public string Manufacturer { get; set; }

        
        
        
        
        public bool IsActive => ExpirationDate >= DateTime.Today;

        
        
        
        public DateTime RequestTime { get; set; }

        
        
        
        public Guid RequestId { get; set; }
    }
}