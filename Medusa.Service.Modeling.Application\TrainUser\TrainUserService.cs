using System;
using System.Collections.Generic;
using System.Linq;
using DotNetCore.CAP.Dashboard;
using Medusa.Service.Modeling.Application.EOPMessage.Dto;
using Medusa.Service.Modeling.Application.TrainUser.Dto;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.Boost;
using Medusa.Service.Modeling.Core.Entities.SWApp;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json.Linq;
using SqlSugar;
using static NPOI.HSSF.Util.HSSFColor;

namespace MMedusa.Service.Modeling.Application.TrainUser
{
    
    
    
    public class TrainUserService : ITrainUserService
    {
        private readonly MyDbContext _dbContext;
        private readonly ILogger _logger;
        private readonly IMemoryCache _cache;
        private readonly string _messageUrl = string.Empty;
        private readonly string systemCode = string.Empty;

        
        
        
        
        public TrainUserService(IServiceProvider serviceProvider)
        {
            _cache = serviceProvider.GetService<IMemoryCache>();
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _logger = serviceProvider.GetService<ILogger<TrainUserService>>();
        }

        
        
        
        
        public TrainUserDto GetTrainUserFiles()
        {
            return _dbContext.Modeling.Queryable<TrainUserEntities>().Select(x => new TrainUserDto()
            {
                Files = x.Files
            }).First();
        }
    }
}
