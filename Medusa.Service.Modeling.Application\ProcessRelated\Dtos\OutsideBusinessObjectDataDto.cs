using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class OutsideBusinessObjectDataDto
    {
        
        
        
        public Guid ProcessId { get; set; }

        
        
        
        public string ProcessVersion { get; set; }

        
        
        
        public Dictionary<string, object> FormData { get; set; }

        
        
        
        public string Boid { get; set; }

        
        
        
        public string Btid { get; set; }

        
        
        
        public string Bsid { get; set; }

        
        
        
        public string Number { get; set; }

        
        
        
        public string OwnerUserId { get; set; }

        
        
        
        public string OrganizationPath { get; set; }

        
        
        
        public string StartUserId { get; set; }

        
        
        
        public string CallBackSetting { get; set; }
    }
}
