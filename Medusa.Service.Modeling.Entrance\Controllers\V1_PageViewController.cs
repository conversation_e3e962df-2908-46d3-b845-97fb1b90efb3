using System;
using System.Collections.Generic;
using System.Dynamic;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.PageView;
using Medusa.Service.Modeling.Application.PageView.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// V1_PageViewController
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "PageView.v1")]
    public class V1_PageViewController : ProductControllerBase
    {
        #region //  服务注入
        readonly IPageViewService _pageViewService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_PageViewController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="pageViewService">pageViewService</param>
        public V1_PageViewController(IPageViewService pageViewService)
        {
            _pageViewService = pageViewService;
        }
        #endregion

        /// <summary>
        /// 获取对象
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("page-view/object")]
        public List<ViewObjectResultDto> GetObject([FromQuery] ViewObjectQueryDto dto)
        {
            return _pageViewService.GetObject(dto);
        }

        /// <summary>
        /// 获取对象字段
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="objectType">objectType</param>
        /// <returns>returns</returns>
        [HttpGet("page-view/object/{id}/field")]
        public List<ViewObjectResultDto> GetObjectField([FromRoute]Guid id, [FromQuery]string objectType)
        {
            return _pageViewService.GetObjectField(id, objectType);
        }
    }
}
