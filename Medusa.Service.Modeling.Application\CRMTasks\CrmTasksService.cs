using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.CRMTasks.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.LowCode;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.Middlewares.UserState;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.CRMTasks
{
    
    
    
    public class CrmTasksService : ICrmTasksService
    {
        private readonly MyDbContext _dbContext;
        readonly string setPath = string.Empty;

        
        
        
        
        public CrmTasksService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
        }

        
        
        
        
        public void SaveVisit(TaskVisitDto task)
        {
            Check(task);
            switch (task.TaskType)
            {
                case TaskTypeEnum.XianChangVisit:
                    SaveOnSiteVisit(task);
                    break;
                default:
                    throw new Exception("未实现的任务保存，请联系管理员！");
            }
        }

        
        
        
        
        
        public CrmPageResult<XianChangVisit> GetXianChangVisists(TaskVisitQueryDto taskVisitQuery)
        {
            var currentUser = UserConstants.CurrentUser.Value.Account;
            var crmTask = _dbContext.Modeling.Queryable<Crmtask>().Where(q => q.TaskId == taskVisitQuery.TaskId).First();

            var totalCount = 0;
            var result = new CrmPageResult<XianChangVisit>();
            if (crmTask != null)
            {
                result.Items = _dbContext.Modeling.Queryable<CrmDailyReport>()
                    .Where(q => q.CreateUserId == currentUser && SqlFunc.ToDate(q.ReportDate) >= crmTask.StartTime && SqlFunc.ToDate(q.ReportDate) <= crmTask.EndTime && q.GJLX == "现场拜访")
                    .Select(q => new XianChangVisit
                    {
                        Id = q.TaskId,
                        TaskId = taskVisitQuery.TaskId,
                        CustomerName = q.CustomerName,
                        ContactPerson = q.VisitUserName,
                        ContactPosition = q.VisitUserPosition,
                        ContactNumber = q.VisitUserPhone,
                        ContactTime = SqlFunc.ToDate(q.ReportDate),
                    })
                    .ToPageList(taskVisitQuery.PageIndex, taskVisitQuery.PageSize, ref totalCount);

                for (int i = 0; i < result.Items.Count; i++)
                {
                    result.Items[i].Num = ((taskVisitQuery.PageIndex - 1) * taskVisitQuery.PageSize) + (i + 1);
                }

                result.Total = totalCount;
                var taskConfigs = GetTemplateConfigs(crmTask.TemplateHistoryId);
                if (taskConfigs != null)
                {
                    var config = taskConfigs.Where(q => q.Checked == true && q.ConfigNanme == "现场拜访").FirstOrDefault();
                    if (config != null)
                    {
                        result.Number1 = config.ConfigNum;
                    }
                }
            }

            return result;
        }

        
        
        
        
        
        public CrmPageResult<DianHuaVisit> GetDianHuaVisists(TaskVisitQueryDto taskVisitQuery)
        {
            var currentUser = UserConstants.CurrentUser.Value.Account;
            var crmTask = _dbContext.Modeling.Queryable<Crmtask>().Where(q => q.TaskId == taskVisitQuery.TaskId).First();

            var totalCount = 0;
            var result = new CrmPageResult<DianHuaVisit>();
            if (crmTask != null)
            {
                result.Items = _dbContext.Modeling.Queryable<CrmDailyReport>()
                    .Where(q => q.CreateUserId == currentUser && SqlFunc.ToDate(q.ReportDate) >= crmTask.StartTime && SqlFunc.ToDate(q.ReportDate) <= crmTask.EndTime && q.GJLX == "电话拜访")
                    .Select(q => new DianHuaVisit
                    {
                        Id = q.TaskId,
                        TaskId = taskVisitQuery.TaskId,
                        CustomerName = q.CustomerName,
                        ContactPerson = q.VisitUserName,
                        ContactPosition = q.VisitUserPosition,
                        ContactNumber = q.VisitUserPhone,
                        ContactTime = SqlFunc.ToDate(q.ReportDate),
                    })
                    .ToPageList(taskVisitQuery.PageIndex, taskVisitQuery.PageSize, ref totalCount);

                for (int i = 0; i < result.Items.Count; i++)
                {
                    result.Items[i].Num = ((taskVisitQuery.PageIndex - 1) * taskVisitQuery.PageSize) + (i + 1);
                }

                result.Total = totalCount;
                var taskConfigs = GetTemplateConfigs(crmTask.TemplateHistoryId);
                result.Total = totalCount;
                if (taskConfigs != null)
                {
                    var config = taskConfigs.Where(q => q.Checked == true && q.ConfigNanme == "电话拜访").FirstOrDefault();
                    if (config != null)
                    {
                        result.Number1 = config.ConfigNum;
                    }
                }
            }

            return result;
        }

        
        
        
        
        
        public CrmPageResult<SatisfactionVisitDto> GetSatisfactionVisits(TaskVisitQueryDto taskVisitQuery)
        {
            var currentUser = UserConstants.CurrentUser.Value.Account;
            var user = _dbContext.Boost.Queryable<Medusa.Service.Modeling.Core.Entities.Boost.Users>().Where(q => q.UserLoginId == currentUser).First();
            var crmTask = _dbContext.Modeling.Queryable<Crmtask>().Where(q => q.TaskId == taskVisitQuery.TaskId).First();
            var totalCount = 0;
            var result = new CrmPageResult<SatisfactionVisitDto>();

            if (crmTask != null)
            {
                result.Items = _dbContext.Modeling.Queryable<MdmCustomer, CrmSatisfaction>((a, b) => new object[]
                 {
                     JoinType.Inner, a.Name == b.CompanyName
                 })
                 .Where((a, b) => SqlFunc.ToDate(b.CreateDate) >= crmTask.StartTime && SqlFunc.ToDate(b.CreateDate) <= crmTask.EndTime
                        && (a.SalesUserId == user.UserLoginId || a.SalesUserId == user.F1 || a.MarketUserId == user.UserLoginId || a.MarketUserId == user.F1))
                 .Select((a, b) => new SatisfactionVisitDto
                 {
                     Id = b.Id,
                     TaskId = taskVisitQuery.TaskId,
                     CustomerName = b.CompanyName,
                     ContactPerson = b.UserName,
                     ContactDepartment = b.DeptName,
                     ContactPosition = b.PositionName,
                     ContactTime = SqlFunc.ToDate(b.CreateDate),
                 })
                 .ToPageList(taskVisitQuery.PageIndex, taskVisitQuery.PageSize, ref totalCount);

                for (int i = 0; i < result.Items.Count; i++)
                {
                    result.Items[i].Num = ((taskVisitQuery.PageIndex - 1) * taskVisitQuery.PageSize) + (i + 1);
                }

                result.Total = totalCount;
                var taskConfigs = GetTemplateConfigs(crmTask.TemplateHistoryId);
                result.Total = totalCount;
                if (taskConfigs != null)
                {
                    var config = taskConfigs.Where(q => q.Checked == true && q.ConfigNanme == "客户满意度调查").FirstOrDefault();
                    if (config != null)
                    {
                        result.Number1 = config.ConfigNum;
                    }
                }
            }

            return result;
        }

        
        
        
        
        
        public CrmPageResult<SaleClueVisitDto> GetSaleClueVisits(TaskVisitQueryDto taskVisitQuery)
        {
            var currentUser = UserConstants.CurrentUser.Value.Account;
            var crmTask = _dbContext.Modeling.Queryable<Crmtask>().Where(q => q.TaskId == taskVisitQuery.TaskId).First();

            var totalCount = 0;
            var result = new CrmPageResult<SaleClueVisitDto>();
            if (crmTask != null)
            {
                result.Items = _dbContext.Modeling.Queryable<CrmSaleClue>()
                    .Where(q => (q.SalesUserId == currentUser || q.MarketUserId == currentUser)
                            && SqlFunc.ToDate(q.CreateDate) >= crmTask.StartTime && SqlFunc.ToDate(q.CreateDate) <= crmTask.EndTime)
                    .Select(q => new SaleClueVisitDto
                    {
                        Id = q.Id,
                        TaskId = taskVisitQuery.TaskId,
                        CustomerName = q.CustomerName,
                        Name = q.Name,
                        ContactTime = SqlFunc.ToDate(q.CreateDate),
                    })
                    .ToPageList(taskVisitQuery.PageIndex, taskVisitQuery.PageSize, ref totalCount);

                for (int i = 0; i < result.Items.Count; i++)
                {
                    result.Items[i].Num = ((taskVisitQuery.PageIndex - 1) * taskVisitQuery.PageSize) + (i + 1);
                }

                result.Total = totalCount;
                var taskConfigs = GetTemplateConfigs(crmTask.TemplateHistoryId);
                result.Total = totalCount;
                if (taskConfigs != null)
                {
                    var config = taskConfigs.Where(q => q.Checked == true && q.ConfigNanme == "商机线索").FirstOrDefault();
                    if (config != null)
                    {
                        result.Number1 = config.ConfigNum;
                    }
                }
            }

            return result;
        }

        
        
        
        
        
        public List<string> GetTaskConfigs(Guid taskId)
        {
            var crmTask = _dbContext.Modeling.Queryable<Crmtask>().Where(q => q.TaskId == taskId).First();
            var taskTemplates = GetTemplateConfigs(crmTask.TemplateHistoryId);
            return taskTemplates.Where(q => q.Checked == true).Select(q => q.ConfigNanme).ToList();
        }
        #region

        
        
        
        
        
        private List<TaskTemplateDetailDto> GetTemplateConfigs(Guid templateId)
        {
            var crmApplication = _dbContext.Modeling.Queryable<Crmtaskapplicationhistory>().Where(q => q.TemplateHistoryId == templateId).First();
            var taskTemplate = JsonConvert.DeserializeObject<List<TaskTemplateDetailDto>>(crmApplication.TaskConfigJson);
            return taskTemplate;
        }

        private void Check(TaskVisitDto data)
        {
            if (!data.TaskId.HasValue)
            {
                throw new Exception("传递的是空任务，请重新进页面或者联系管理员！");
            }
        }

        private void SaveOnSiteVisit(TaskVisitDto data)
        {
            CrmTaskOnSiteVisit task = new CrmTaskOnSiteVisit();

            if (data.Id.HasValue)
            {
                task = _dbContext.Boost.Queryable<CrmTaskOnSiteVisit>().Where(q => q.Id == data.Id).First();
                task.ModifyUserId = UserConstants.CurrentUser.Value.Id;
                task.ModifyUserName = UserConstants.CurrentUser.Value.Name;
                task.ModifyDate = DateTime.Now;
            }
            else
            {
                task.TaskId = data.TaskId;
                task.CreateUserId = UserConstants.CurrentUser.Value.Id;
                task.CreateUserName = UserConstants.CurrentUser.Value.Name;
                task.CreateDate = DateTime.Now;
            }

            task.CustomerName = data.CustomerName;
            task.ContactPerson = data.ContactPerson;
            task.ContactPosition = data.ContactPosition;
            task.ContactTime = data.ContactTime;
            task.Status = data.Status;
            task.Remark = data.Remark;

            _dbContext.Modeling.Storageable(task).ExecuteCommand();
        }
        #endregion
    }
}
