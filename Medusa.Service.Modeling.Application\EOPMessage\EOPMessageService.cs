using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.EOPMessage.Dto;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.Boost;
using Medusa.Service.Modeling.Core.Entities.SWApp;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace MMedusa.Service.Modeling.Application.EOPMessage
{
    
    
    
    public class EOPMessageService : IEOPMessageService
    {
        private readonly MyDbContext _dbContext;
        private readonly ILogger _logger;
        private readonly IMemoryCache _cache;
        private readonly string _messageUrl = string.Empty;
        private readonly string systemCode = string.Empty;

        
        
        
        
        public EOPMessageService(IServiceProvider serviceProvider)
        {
            _cache = serviceProvider.GetService<IMemoryCache>();
            _dbContext = serviceProvider.GetService<MyDbContext>();
            var appSettings = _cache.Get<JObject>("AppSettings");
            _messageUrl = appSettings["Message"]?["Url"]?.ToString();
            systemCode = appSettings["Message"]?["SystemCode"]?.ToString();
            _logger = serviceProvider.GetService<ILogger<EOPMessageService>>();
        }

        
        
        
        
        public string CustmerMsg()
        {
            var result = "0";
            var content = GetTemplateContent("CustomerMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Code == "CustomerViewUrl" && x.TypeCode == "PCMsgUrl").First();
                var list = _dbContext.Modeling.Queryable<CRMCustomer>()
                    .Where(x => x.IsDelete == 0 && x.IsSend == 0)
                    .ToList();
                if (list != null && list.Count > 0)
                {
                    var managerUserId = string.Empty;

                    foreach (var customer in list)
                    {
                        
                        if (!string.IsNullOrWhiteSpace(customer.SalesUserId))
                        {
                            var user = GetManagerUserInfo(customer.SalesUserId);
                            managerUserId = user.Item1;
                            var msgContent1 = content.Replace("{Manager}", user.Item2).Replace("{UserName}", customer.SalesName).Replace("{CustomerName}", customer.Name);
                            var request = new
                            {
                                Id = Guid.NewGuid(),
                                SystemCode = systemCode,
                                MobileUrl = string.Empty,
                                PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + customer.ID : string.Empty,
                                SendToLoginId = managerUserId,
                                Title = msgContent1,
                                UserLoginId = customer.SalesUserId
                            };
                            SendMessage(request);
                        }

                        
                        if (!string.IsNullOrWhiteSpace(customer.MarketUserId))
                        {
                            var user = GetManagerUserInfo(customer.MarketUserId);
                            managerUserId = user.Item1;
                            var msgContent2 = content.Replace("{Manager}", user.Item2).Replace("{UserName}", customer.MarketName).Replace("{CustomerName}", customer.Name);
                            var request = new
                            {
                                Id = Guid.NewGuid(),
                                SystemCode = systemCode,
                                MobileUrl = string.Empty,
                                PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + customer.ID : string.Empty,
                                SendToLoginId = managerUserId,
                                Title = msgContent2,
                                UserLoginId = customer.MarketUserId
                            };
                            SendMessage(request);
                        }

                        customer.IsSend = 1;
                        _dbContext.Modeling.Updateable(customer).ExecuteCommand();
                        result = "1";
                    }
                }
            }

            return result;
        }

        
        
        
        
        public string SalesClueMsg()
        {
            var result = "0";
            var content = GetTemplateContent("SalesClueMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => (x.Code == "SalesClueViewUrl_PV" || x.Code == "SalesClueViewUrl_SET") && x.TypeCode == "PCMsgUrl").ToList();
                var list = _dbContext.Modeling.Queryable<CRMSaleClue>()
                    .Where(x => x.IsDelete == 0 && x.IsSend == 0)
                    .ToList();
                if (list != null && list.Count > 0)
                {
                    var pvdic = pcdic.FirstOrDefault(x => x.Code == "SalesClueViewUrl_PV");
                    var setdic = pcdic.FirstOrDefault(x => x.Code == "SalesClueViewUrl_SET");
                    foreach (var clue in list)
                    {
                        var user = GetManagerUserInfo(clue.SalesUserId);
                        var msgContent = content.Replace("{Manager}", user.Item2).Replace("{UserName}", clue.SalesName).Replace("{ClueName}", clue.Name);
                        var pcUrl = clue.Flag == "PV" ? pvdic.Value + clue.ID : clue.Flag == "SET" ? setdic.Value + clue.ID : string.Empty;
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = pcUrl,
                            SendToLoginId = user.Item1,
                            Title = msgContent,
                            UserLoginId = clue.SalesUserId
                        };
                        SendMessage(request);
                        clue.IsSend = 1;
                        _dbContext.Modeling.Updateable(clue).ExecuteCommand();
                        result = "1";
                    }
                }
            }

            return result;
        }

        
        
        
        
        public string SampleMsg()
        {
            var result = "0";
            var content = GetTemplateContent("SampleMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Code == "SampleViewUrl" && x.TypeCode == "PCMsgUrl").First();
                var list = _dbContext.Modeling.Queryable<CRMSampleRecords>()
                    .InnerJoin<CRMSaleClue>((a, b) => a.SalesClueId == b.ID)
                    .Where((a, b) => a.IsDelete == 0 && a.IsSend == 0)
                    .Select((a, b) => new
                    {
                        SalesName = b.SalesName,
                        SalesUserId = b.SalesUserId,
                        Name = b.Name,
                        CreateUserId = a.CreateUserId,
                        ID = a.ID
                    }).ToList();
                if (list != null && list.Count > 0)
                {
                    foreach (var clue in list)
                    {
                        var user = _dbContext.Boost.Queryable<Users>().First(x => x.UserLoginId == clue.CreateUserId);
                        var msgContent = content.Replace("{SaleUser}", clue.SalesName).Replace("{UserName}", user.UserName).Replace("{ClueName}", clue.Name);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + clue.ID : string.Empty,
                            SendToLoginId = clue.SalesUserId,
                            Title = msgContent,
                            UserLoginId = clue.CreateUserId
                        };
                        SendMessage(request);
                        var data = _dbContext.Modeling.Queryable<CRMSampleRecords>().First(x => x.ID == clue.ID);
                        data.IsSend = 1;
                        _dbContext.Modeling.Updateable(data).ExecuteCommand();
                        result = "1";
                    }
                }
            }

            return result;
        }

        
        
        
        
        public string SampleJobMsg()
        {
            var result = "0";
            var content = GetTemplateContent("SampleJobMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Code == "SampleViewUrl" && x.TypeCode == "PCMsgUrl").First();
                var list = _dbContext.Modeling.Queryable<CRMSampleRecords>()
                    .InnerJoin<CRMSaleClue>((a, b) => a.SalesClueId == b.ID)
                    .Where((a, b) => a.IsDelete == 0 && !string.IsNullOrWhiteSpace(a.TestEndTime) && Convert.ToDateTime(a.TestEndTime) <= DateTime.Now && string.IsNullOrWhiteSpace(a.Result))
                    .Select((a, b) => new
                    {
                        ID = a.ID,
                        SalesName = b.SalesName,
                        SalesUserId = b.SalesUserId,
                        Name = b.Name,
                        No = a.SampleNo
                    }).ToList();
                if (list != null && list.Count > 0)
                {
                    foreach (var item in list)
                    {
                        var msgContent = content.Replace("{SalesName}", item.SalesName).Replace("{ClueName}", item.Name).Replace("{SampleNo}", item.No);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + item.ID : string.Empty,
                            SendToLoginId = item.SalesUserId,
                            Title = msgContent,
                            UserLoginId = item.SalesUserId
                        };
                        SendMessage(request);
                    }

                    result = "1";
                }
            }

            return result;
        }

        
        
        
        
        public string ProductImportMsg()
        {
            var result = "0";
            var content_sales = GetTemplateContent("ProductImportMsg_Sales");
            var content_sampleUser = GetTemplateContent("ProductImportMsg_SampleUser");
            var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Code == "ProductImportViewUrl" && x.TypeCode == "PCMsgUrl").First();
            var updatelist = new List<CRMProductImport>();
            if (!string.IsNullOrWhiteSpace(content_sales))
            {
                var list = _dbContext.Modeling.Queryable<CRMProductImport>()
                    .InnerJoin<CRMSaleClue>((a, b) => a.SalesClueId == b.ID)
                    .Where((a, b) => a.IsDelete == 0 && a.IsSend == 0)
                    .Select((a, b) => new
                    {
                        SalesName = b.SalesName,
                        SalesUserId = b.SalesUserId,
                        Name = b.Name,
                        CreateUserId = a.CreateUserId,
                        ID = a.ID
                    }).ToList();
                if (list != null && list.Count > 0)
                {
                    foreach (var clue in list)
                    {
                        var msgContent = content_sales.Replace("{SalesName}", clue.SalesName).Replace("{ClueName}", clue.Name);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + clue.ID : string.Empty,
                            SendToLoginId = clue.SalesUserId,
                            Title = msgContent,
                            UserLoginId = clue.CreateUserId
                        };
                        SendMessage(request);
                        var data = _dbContext.Modeling.Queryable<CRMProductImport>().First(x => x.ID == clue.ID);
                        data.IsSend = 1;
                        updatelist.Add(data);
                        result = "1";
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(content_sampleUser))
            {
                var productlist = _dbContext.Modeling.Queryable<CRMProductImport>()
                    .InnerJoin<CRMSaleClue>((a, b) => a.SalesClueId == b.ID)
                    .InnerJoin<CRMProductInfo>((a, b, c) => a.ID == c.ProductImportId)
                    .InnerJoin<CRMSampleRecords>((a, b, c, d) => d.ID == c.SampleId)
                    .Where((a, b, c, d) => a.IsDelete == 0 && a.IsSend == 0)
                    .Select((a, b, c, d) => new
                    {
                        CreateSampleUserId = d.CreateUserId,
                        ClueName = b.Name,
                        CreateUserId = a.CreateUserId,
                        ID = a.ID
                    }).ToList();
                if (productlist != null && productlist.Count > 0)
                {
                    var userlist = productlist.Select(x => x.CreateSampleUserId).ToList();
                    var user = _dbContext.Boost.Queryable<Users>().Where(x => userlist.Contains(x.UserLoginId)).ToList();
                    foreach (var p in productlist)
                    {
                        var username = user.FirstOrDefault(x => x.UserLoginId == p.CreateSampleUserId)?.UserName;
                        var msg = content_sampleUser.Replace("{CreateSampleUser}", username).Replace("{ClueName}", p.ClueName);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + p.ID : string.Empty,
                            SendToLoginId = p.CreateSampleUserId,
                            Title = msg,
                            UserLoginId = p.CreateUserId
                        };
                        SendMessage(request);
                        result = "1";
                    }
                }
            }

            if (updatelist.Count > 0)
            {
                _dbContext.Modeling.Updateable(updatelist).ExecuteCommand();
            }

            return result;
        }

        
        
        
        
        public string DaliyAccountMsg()
        {
            var result = "0";
            var content = GetTemplateContent("DaliyAccountMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Code == "DaliyAccountViewUrl" && x.TypeCode == "PCMsgUrl").First();
                var list = _dbContext.Modeling.Queryable<CRMDaliyAccount>()
                    .InnerJoin<CRMSaleClue>((a, b) => a.SalesClueId == b.ID)
                    .Where((a, b) => a.IsDelete == 0 && a.IsSend == 0)
                    .Select((a, b) => new
                    {
                        SalesName = b.SalesName,
                        SalesUserId = b.SalesUserId,
                        Month = a.Month,
                        Name = b.Name,
                        CreateUserId = a.CreateUserId,
                        ID = a.ID
                    }).ToList();
                if (list != null && list.Count > 0)
                {
                    foreach (var clue in list)
                    {
                        var user = GetManagerUserInfo(clue.SalesUserId);
                        var msgContent = content.Replace("{SalesManager}", user.Item2).Replace("{UserName}", clue.SalesName).Replace("{Month}", clue.Month);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + clue.ID : string.Empty,
                            SendToLoginId = user.Item1,
                            Title = msgContent,
                            UserLoginId = clue.CreateUserId
                        };
                        SendMessage(request);
                        var data = _dbContext.Modeling.Queryable<CRMDaliyAccount>().First(x => x.ID == clue.ID);
                        data.IsSend = 1;
                        _dbContext.Modeling.Updateable(data).ExecuteCommand();
                        result = "1";
                    }
                }
            }

            return result;
        }

        
        
        
        
        public string MonthAccountMsg()
        {
            var result = "0";
            var content = GetTemplateContent("MonthAccountMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => (x.Code == "MonthAccountViewUrl_PV" || x.Code == "MonthAccountViewUrl_SET") && x.TypeCode == "PCMsgUrl").ToList();
                var list = _dbContext.Modeling.Queryable<CRMMonthAccount>()
                    .Where((a) => a.IsDelete == 0 && a.IsSend == 0)
                    .ToList();
                if (list != null && list.Count > 0)
                {
                    var pvdic = pcdic.FirstOrDefault(x => x.Code == "MonthAccountViewUrl_PV");
                    var setdic = pcdic.FirstOrDefault(x => x.Code == "MonthAccountViewUrl_SET");
                    foreach (var clue in list)
                    {
                        var pcUrl = clue.Type == "PV" ? pvdic.Value + clue.ID : clue.Type == "SET" ? setdic.Value + clue.ID : string.Empty;
                        var user = GetManagerUserInfo(clue.SalesUserId);
                        var msgContent = content.Replace("{SalesManager}", user.Item2).Replace("{UserName}", clue.SalesUserName).Replace("{Month}", clue.Month);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = pcUrl,
                            SendToLoginId = user.Item1,
                            Title = msgContent,
                            UserLoginId = clue.CreateUserId
                        };
                        SendMessage(request);
                        clue.IsSend = 1;
                        _dbContext.Modeling.Updateable(clue).ExecuteCommand();
                        result = "1";
                    }
                }
            }

            return result;
        }

        
        
        
        
        public string DailyReportMsg()
        {
            var result = "0";
            var content = GetTemplateContent("DailyReportMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Code == "DailyReportViewUrl" && x.TypeCode == "PCMsgUrl").First();
                var list = _dbContext.Modeling.Queryable<CRMDailyReport>()
                    .Where((a) => a.IsDelete == 0 && a.IsSend == 0).ToList();
                if (list != null && list.Count > 0)
                {
                    foreach (var clue in list)
                    {
                        var user = GetManagerUserInfo(clue.CreateUserId);
                        var msgContent = content.Replace("{SalesManager}", user.Item2).Replace("{UserName}", clue.CreateUserName).Replace("{Date}", clue.ReportDate);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + clue.ID : string.Empty,
                            SendToLoginId = user.Item1,
                            Title = msgContent,
                            UserLoginId = clue.CreateUserId
                        };
                        SendMessage(request);
                        clue.IsSend = 1;
                        _dbContext.Modeling.Updateable(clue).ExecuteCommand();
                        result = "1";
                    }
                }
            }

            return result;
        }

        
        
        
        
        public string MonthReportMsg()
        {
            var result = "0";
            var content = GetTemplateContent("MonthReportMsg");
            if (!string.IsNullOrWhiteSpace(content))
            {
                var pcdic = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Code == "MonthReportViewUrl" && x.TypeCode == "PCMsgUrl").First();
                var list = _dbContext.Modeling.Queryable<CRMMonthReport>()
                    .Where((a) => a.IsDelete == 0 && a.IsSend == 0)
                    .ToList();
                if (list != null && list.Count > 0)
                {
                    foreach (var clue in list)
                    {
                        var user = GetManagerUserInfo(clue.CreateUserId);
                        var msgContent = content.Replace("{SalesManager}", user.Item2).Replace("{UserName}", clue.CreateName).Replace("{WeekDate}", clue.StartTime);
                        var request = new
                        {
                            Id = Guid.NewGuid(),
                            SystemCode = systemCode,
                            MobileUrl = string.Empty,
                            PcUrl = !string.IsNullOrWhiteSpace(pcdic.Value) ? pcdic.Value + clue.ID : string.Empty,
                            SendToLoginId = user.Item1,
                            Title = msgContent,
                            UserLoginId = clue.CreateUserId
                        };
                        SendMessage(request);
                        clue.IsSend = 1;
                        _dbContext.Modeling.Updateable(clue).ExecuteCommand();
                        result = "1";
                    }
                }
            }

            return result;
        }

        
        
        
        
        
        public Tuple<string, string> GetManagerUserInfo(string userid)
        {
            var managerUserId = string.Empty;
            var managerUserName = string.Empty;
            var user = _dbContext.Boost.Queryable<Users>()
                                .InnerJoin<Users>((a, b) => a.UpperUserId == b.UserId)
                                .Where((a, b) => a.UserLoginId == userid && a.Status == 1)
                                .Select((a, b) => new
                                {
                                    managerUserId = b.UserLoginId,
                                    managerUserName = b.UserName
                                }).ToList();
            var manager = user.FirstOrDefault();
            if (manager != null)
            {
                managerUserName = manager.managerUserName;
                managerUserId = manager.managerUserId;
            }

            return new Tuple<string, string>(managerUserId, managerUserName);
        }

        
        
        
        
        
        public string GetTemplateContent(string code)
        {
            var sql = @"select TemplateName,TemplateContent from messagetemplate WHERE TemplateCode=@code and `Status`=1 ";
            var template = _dbContext.Boost.Ado.GetDataTable(sql, new List<SugarParameter>()
                {
                    new SugarParameter("@code", code),
                });
            var content = string.Empty;
            if (template != null && template.Rows.Count > 0)
            {
                var row = template.Rows[0];
                content = row["TemplateContent"].ToString();
            }

            return content;
        }

        
        
        
        
        public void SendMessage(object request)
        {
            _logger.LogInformation($"消息推送为{_messageUrl}，传参request：{request.ToOurJsonString()}");
            HttpHelper.Post<string>(_messageUrl, request);
        }
    }
}
