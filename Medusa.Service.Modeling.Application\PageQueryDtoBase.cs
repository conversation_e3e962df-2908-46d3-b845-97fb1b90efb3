using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Medusa.Service.Modeling.Application
{
    
    
    
    public class PageQueryDtoBase
    {
        
        
        
        [FromQuery(Name = "page-index")]
        [JsonProperty(PropertyName = "page-index")]
        public int PageIndex { get; set; } = 1;

        
        
        
        [FromQuery(Name = "page-size")]
        [JsonProperty(PropertyName = "page-size")]
        public int PageSize { get; set; } = 10;

        
        
        
        [FromQuery(Name = "sorter-field")]
        [JsonProperty(PropertyName = "sorter-field")]
        public string SorterField { get; set; }

        
        
        
        [FromQuery(Name = "sorter-order")]
        [JsonProperty(PropertyName = "sorter-order")]
        public PageSort? SorterOrder { get; set; }

        
        
        
        [FromQuery(Name = "is-all")]
        [JsonProperty(PropertyName = "is-all")]
        public bool IsAll { get; set; } = false;
    }
}
