# deploy_copy 和 deploy_run 是 runner 支持的脚本
# 务必采用架构组提供的 runner 才能使用这两个命令
# 这两个命令分别是将 runner 的文件复制到部署服务器、在部署服务器上运行指定命令

stages:
  - lint_and_build
  - test
  - deploy
  - release_connect
  - release_api

before_script:
  - PROJECT_NAME=medusa.modeling
  - PUBLISH_NAME=shuiwu-project
  - SOURCE_PUBLIC=https://api.nuget.org/v3/index.json
  - SOURCE_PRIVATE=http://*************:15840/nuget
  - HARBOR=*************:8858/shuiwu

lint_and_build_task:
  stage: lint_and_build
  except:
    - develop
    - master
    - tags
    - 水务
  script:
    #- sed -i -e 's/Warning/Error/g' csharp.ruleset
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    - dotnet build ./Medusa.Service.Modeling.Entrance/Medusa.Service.Modeling.Entrance.csproj --no-restore

#test_task:
#  stage: test
#  except:
#    - develop
#    - master
#    - tags
#  script:
#    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
#    - dotnet test ./Medusa.Service.Modeling.UnitTests/Medusa.Service.Modeling.UnitTests.csproj --no-restore

dev_deploy_api_task:
  stage: deploy
  only:
    - 水务
  script:
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    - dotnet publish ./Medusa.Service.Modeling.Entrance/Medusa.Service.Modeling.Entrance.csproj --no-restore -o /tmp/publish/$PROJECT_NAME.api/tmp
    #- dotnet ./CodeObfuscate/Z00bfuscator.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Core.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Application.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.SSO.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.FetchUser.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Entrance.dll
    - tar -cvzf /tmp/$PROJECT_NAME.api.tgz -C /tmp/publish/$PROJECT_NAME.api tmp
    - deploy_copy /tmp/$PROJECT_NAME.api.tgz /var/www/$PUBLISH_NAME/$PROJECT_NAME.api
    - deploy_run "cd /var/www/$PUBLISH_NAME/$PROJECT_NAME.api && tar -xvzf $PROJECT_NAME.api.tgz"
    - deploy_run "cd /var/www/$PUBLISH_NAME/$PROJECT_NAME.api && rm $PROJECT_NAME.api.tgz"
    - deploy_run "rm -rf /var/www/$PUBLISH_NAME/$PROJECT_NAME.api/src"
    - deploy_run "mv /var/www/$PUBLISH_NAME/$PROJECT_NAME.api/tmp /var/www/$PUBLISH_NAME/$PROJECT_NAME.api/src"
    # - deploy_run "pm2 restart $PROJECT_NAME.api"

dev_deploy_connect_task:
  stage: deploy
  only:
    - 水务
  script:
    - dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
    - dotnet publish ./Medusa.Service.Modeling.Connect/Medusa.Service.Modeling.Connect.csproj --no-restore -o /tmp/publish/$PROJECT_NAME.connect/tmp
    #- dotnet ./CodeObfuscate/Z00bfuscator.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Core.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Application.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.SSO.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.FetchUser.dll /tmp/publish/$PROJECT_NAME/tmp/Medusa.Service.Platform.Entrance.dll
    - tar -cvzf /tmp/$PROJECT_NAME.connect.tgz -C /tmp/publish/$PROJECT_NAME.connect tmp
    - deploy_copy /tmp/$PROJECT_NAME.connect.tgz /var/www/$PUBLISH_NAME/$PROJECT_NAME.connect
    - deploy_run "cd /var/www/$PUBLISH_NAME/$PROJECT_NAME.connect && tar -xvzf $PROJECT_NAME.connect.tgz"
    - deploy_run "cd /var/www/$PUBLISH_NAME/$PROJECT_NAME.connect && rm $PROJECT_NAME.connect.tgz"
    - deploy_run "rm -rf /var/www/$PUBLISH_NAME/$PROJECT_NAME.connect/src"
    - deploy_run "mv /var/www/$PUBLISH_NAME/$PROJECT_NAME.connect/tmp /var/www/$PUBLISH_NAME/$PROJECT_NAME.connect/src"
    # - deploy_run "pm2 restart $PROJECT_NAME.connect"

release_api_task:
  stage: release_api
  only:
    - tags
  script:
    - deploy_run "cd /var/www/$PUBLISH_NAME/$PROJECT_NAME.api/src && docker build -t $PROJECT_NAME.api:$CI_COMMIT_TAG  ."
    - deploy_run "docker tag $PROJECT_NAME.api:$CI_COMMIT_TAG  $HARBOR/$PROJECT_NAME.api:$CI_COMMIT_TAG"
    - deploy_run "docker push $HARBOR/$PROJECT_NAME.api:$CI_COMMIT_TAG"
    - deploy_run "docker rmi $HARBOR/$PROJECT_NAME.api:$CI_COMMIT_TAG && docker rmi $PROJECT_NAME.api:$CI_COMMIT_TAG"

release_connect_task:
  stage: release_connect
  only:
    - tags
  script:
    - deploy_run "cd /var/www/$PUBLISH_NAME/$PROJECT_NAME.connect/src && docker build -t $PROJECT_NAME.connect:$CI_COMMIT_TAG  ."
    - deploy_run "docker tag $PROJECT_NAME.connect:$CI_COMMIT_TAG  $HARBOR/$PROJECT_NAME.connect:$CI_COMMIT_TAG"
    - deploy_run "docker push $HARBOR/$PROJECT_NAME.connect:$CI_COMMIT_TAG"
    - deploy_run "docker rmi $HARBOR/$PROJECT_NAME.connect:$CI_COMMIT_TAG && docker rmi $PROJECT_NAME.connect:$CI_COMMIT_TAG"
