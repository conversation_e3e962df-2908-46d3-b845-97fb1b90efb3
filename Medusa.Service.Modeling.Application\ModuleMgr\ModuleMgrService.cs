using System;
using Medusa.Service.Modeling.Application.ModuleMgr.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.Middlewares.UserState;

namespace Medusa.Service.Modeling.Application.ModuleMgr
{
    
    
    
    public class ModuleMgrService : ServiceBase, IModuleMgrService
    {
        #region 
        readonly MyDbContext _dbContext;

        
        
        
        
        
        public ModuleMgrService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
        }

        #endregion

        
        
        
        
        public void DeleteModule(Guid id)
        {
            var entity = _dbContext.Modeling.Queryable<ModuleEntity>().InSingle(id);
            if (entity == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.module.notfound"));
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Updateable<ModuleEntity>()
                .SetColumns(w => new ModuleEntity()
                {
                    IsDelete = true,
                    UpdateDate = DateTime.Now,
                    UpdateUser = userId
                })
                .Where(w => w.Id == id).ExecuteCommand();
        }

        
        
        
        
        public void AddModule(ModuleDto dto)
        {
            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Insertable<ModuleEntity>(new ModuleEntity()
            {
                Id = dto.Id,
                Name = dto.Name,
                Describe = dto.Describe,
                CreateDate = DateTime.Now,
                CreateUser = userId,
                ApplicationId = dto.ApplicationId,
                ApplicationName = dto.ApplicationName,
                IsDelete = false
            }).ExecuteCommand();
        }

        
        
        
        
        
        public void UpdateModule(Guid id, ModuleDto dto)
        {
            var entity = _dbContext.Modeling.Queryable<ModuleEntity>().InSingle(id);
            if (entity == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.module.notfound"));
            }

            var userId = UserConstants.CurrentUser.Value.Account;
            _dbContext.Modeling.Updateable<ModuleEntity>()
                 .SetColumns(w => new ModuleEntity()
                 {
                     Name = dto.Name,
                     Describe = dto.Describe,
                     UpdateDate = DateTime.Now,
                     UpdateUser = userId
                 })
                 .Where(w => w.Id == id)
                .ExecuteCommand();

            _dbContext.Modeling.Updateable<PageModeling>()
                .SetColumns(w => new PageModeling()
                {
                    ModuleName = dto.Name
                })
            .Where(w => w.ApplicationId == id).ExecuteCommand();
        }
    }
}
