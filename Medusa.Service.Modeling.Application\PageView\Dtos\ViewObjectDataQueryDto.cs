namespace Medusa.Service.Modeling.Application.PageView.Dtos
{
    
    
    
    public class ViewObjectDataQueryDto
    {
        
        
        
        public string ObjectType { get; set; }

        
        
        
        public bool? IsOr { get; set; }

        
        
        
        public string Wheres { get; set; }

        
        
        
        public int PageIndex { get; set; } = 1;

        
        
        
        public int PageSize { get; set; } = 10;

        
        
        
        public string SorterField { get; set; }

        
        
        
        public bool IsAll { get; set; } = false;
    }
}
