using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 业务系统
    /// </summary>
    [EntityTable("BusinessSystems")]
    public class BusinessSystem
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 业务系统名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 业务系统描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 业务系统编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }
    }
}
