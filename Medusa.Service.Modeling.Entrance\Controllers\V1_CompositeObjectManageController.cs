using System;
using System.Data;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.CompositeObjectManage;
using Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// CompositeObjects controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "CompositeObjectManageService.v1")]
    public class V1_CompositeObjectManageController : ProductControllerBase
    {
        #region //  服务注入
        readonly ICompositeObjectManageService _compositeObjectManageService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_CompositeObjectManageController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="compositeObjectManageService">compositeObjectManageService</param>
        public V1_CompositeObjectManageController(ICompositeObjectManageService compositeObjectManageService)
        {
            _compositeObjectManageService = compositeObjectManageService;
        }
        #endregion

        /// <summary>
        /// 获取组合对象
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("composite-objects")]
        public PageResult<CompositeObjectDto> GetCompositeObjects([FromQuery] CompositeObjectQueryDto dto)
        {
            return _compositeObjectManageService.GetCompositeObjects(dto);
        }

        /// <summary>
        /// 获取组合对象
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpGet("composite-objects/{id}")]
        public CompositeObjectDto GetCompositeObject([FromRoute] Guid id)
        {
            return _compositeObjectManageService.GetCompositeObject(id);
        }

        /// <summary>
        /// 新增/修改组合对象
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("composite-objects/save")]
        public void SaveCompositeObject([FromBody] CompositeObjectDto dto)
        {
            _compositeObjectManageService.SaveCompositeObject(dto);
        }

        /// <summary>
        /// 组合对象删除
        /// </summary>
        /// <param name="id">id</param>
        [HttpPost("composite-objects/{id}/delete")]
        public void DeleteCompositeObject([FromRoute] Guid id)
        {
            _compositeObjectManageService.DeleteCompositeObject(id);
        }

        /// <summary>
        /// 获取业务对象表字段树
        /// </summary>
        /// <param name="id">组合对象ID</param>
        /// <returns>业务对象表字段树</returns>
        [HttpGet("composite-objects/{id}/query")]
        public BOColumnDataDto GetCompositeObjectDataTree([FromRoute] Guid id)
        {
            return _compositeObjectManageService.GetCompositeObjectTreeList(id);
        }

        /// <summary>
        /// 业务对象数据查询
        /// </summary>
        /// <param name="dto">业务对象数据查询dto</param>
        /// <returns>数据列表</returns>
        [HttpPost("composite-objects/GetCompositeObjectDataList")]
        public DataTable GetCompositeObjectDataList([FromBody] BOColumnDataDto dto)
        {
            DataTable dt = _compositeObjectManageService.GetCompositeObjectDataList(dto);
            return dt;
        }
    }
}
