using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 业务系统权限
    /// </summary>
    [EntityTable("BusinessSystemsAuthoritys")]
    public class BusinessSystemsAuthority
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 业务对象Id
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 业务系统Id
        /// </summary>
        public Guid BusinessSystemId { get; set; }

        /// <summary>
        /// 对象类型
        /// </summary>
        public int? BusinessObjectType { get; set; }
    }
}
