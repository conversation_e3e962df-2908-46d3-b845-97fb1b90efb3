using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 页面版本
    /// </summary>
    [EntityTable("PageVersions")]
    public partial class PageVersion
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 页面ID
        /// </summary>
        public Guid PageModelingId { get; set; }

        /// <summary>
        /// 版本状态 0：草稿 1：已发布
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 页面设计json
        /// </summary>
        public string PageDesginJson { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; }

        /// <summary>
        /// 来源版本号
        /// </summary>
        public int? SourceVersion { get; set; }

        /// <summary>
        /// 业务对象
        /// </summary>
        public string DataSource { get; set; }
    }
}
