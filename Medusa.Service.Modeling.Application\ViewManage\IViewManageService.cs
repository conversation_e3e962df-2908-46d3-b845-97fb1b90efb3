using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.ViewManage.Dtos;
using Medusa.Service.Modeling.Core.Entity;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Application.ViewManage
{
    
    
    
    public interface IViewManageService : IServiceBase
    {
        
        
        
        
        
        PageResult<ViewDto> GetList(ViewQueryDto dto);

        
        
        
        
        
        ViewDto GetView(Guid id);

        
        
        
        
        
        List<ViewColumns> GetViewColumns(Guid id);

        
        
        
        
        void SaveView(ViewDto dto);

        
        
        
        
        void DeleteView(Guid id);

        
        
        
        
        void PublishView(Guid id);

        
        
        
        
        
        PageResult<ViewForCompositeDto> GetListForComposite(ViewQueryForCompositeDto dto);

        
        
        
        
        JArray GetDictionarCascaderyTree();
    }
}
