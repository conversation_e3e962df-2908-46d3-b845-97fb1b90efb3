using System;
using System.Collections.Generic;

namespace Medusa.Service.Modeling.Application.OperationLog.Dtos
{
    
    
    
    public class WriteLogDto
    {
        
        
        
        public string Menu { get; set; }

        
        
        
        public string Action { get; set; }

        
        
        
        public string MainTableName { get; set; }

        
        
        
        public string MainTableDataId { get; set; }

        
        
        
        public string TableName { get; set; }

        
        
        
        public string TableDataId { get; set; }

        
        
        
        public string OperationType { get; set; }

        
        
        
        public string OperationCondition { get; set; }

        
        
        
        public Dictionary<string, object> Data { get; set; }
    }
}
