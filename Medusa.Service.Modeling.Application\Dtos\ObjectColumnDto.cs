using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Medusa.Service.Modeling.Application.Dtos
{
    
    
    
    public class ObjectColumnDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        [Required]
        public string Name { get; set; }

        
        
        
        [Required]
        public string Description { get; set; }

        
        
        
        [Required]
        public int? Length { get; set; }

        
        
        
        public bool? IsPrimaryKey { get; set; }

        
        
        
        public bool? IsNullable { get; set; }

        
        
        
        [Required]
        public int? Decimal { get; set; }

        
        
        
        [Required]
        public string DisplayType { get; set; }

        
        
        
        public bool IsNew { get; set; }

        
        
        
        public bool IsEnable { get; set; }

        
        
        
        public bool IsSystemColumn { get; set; }

        
        
        
        public int? Order { get; set; }

        
        
        
        public string ColumnType { get; set; }

        
        
        
        public string DefaultValue { get; set; }
    }
}
