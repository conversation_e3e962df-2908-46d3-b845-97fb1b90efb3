using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Medusa.Service.Modeling.Application.Authority.Dtos
{
    
    
    
    public class BusinessSysDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        [Required]
        public string Name { get; set; }

        
        
        
        [Required]
        public string Code { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public DateTime? CreateDate { get; set; }
    }
}
