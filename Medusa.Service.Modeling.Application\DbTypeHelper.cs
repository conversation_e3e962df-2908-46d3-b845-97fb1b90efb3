using Medusa.Service.Modeling.Core.Entity;

namespace Medusa.Service.Modeling.Application
{
    
    
    
    public static class DbTypeHelper
    {
        
        
        
        
        
        
        public static string DbTypeFormat(string dataBaseType, string dbType)
        {
            string resultType = string.Empty;
            switch (dataBaseType.ToLower())
            {
                case "sqlserver":
                    resultType = SqlServerFormat(dbType);
                    break;
                case "mysql":
                    resultType = MySqlFormat(dbType);
                    break;
            }

            return resultType;
        }

        
        
        
        
        
        
        
        public static int GetLength(string dataBaseType, string type, int? len)
        {
            int resultLen = 0;
            switch (dataBaseType.ToLower())
            {
                case "sqlserver":
                    switch (type)
                    {
                        case "object":
                            resultLen = !len.HasValue || len.Value == 0 ? 4000 : len.Value;
                            break;
                        case "keyvalue":
                            resultLen = !len.HasValue || len.Value == 0 ? 500 : len.Value;
                            break;
                        case "ntext":
                            resultLen = 0;
                            break;
                        default:
                            resultLen = len.Value;
                            break;
                    }

                    break;
                case "mysql":
                    switch (type)
                    {
                        case "bool":
                            resultLen = 1;
                            break;
                        case "object":
                            resultLen = !len.HasValue || len.Value == 0 ? 4000 : len.Value;
                            break;
                        case "keyvalue":
                            resultLen = !len.HasValue || len.Value == 0 ? 500 : len.Value;
                            break;
                        case "ntext":
                            resultLen = 0;
                            break;
                        default:
                            resultLen = len.Value;
                            break;
                    }

                    break;
            }

            return resultLen;
        }

        
        
        
        
        
        
        public static string NetTypeFormat(string dataBaseType, BusinessObjectColumn column)
        {
            string type;
            switch (column.DisplayType.ToLower())
            {
                case "text":
                    type = "varchar";
                    break;
                case "number":
                    type = column.Decimal.HasValue && column.Decimal.Value > 0 ? "decimal" : "int";
                    break;
                case "date":
                    type = "datetime";
                    break;
                case "bool":
                    if (dataBaseType.ToLower() == "sqlserver")
                    {
                        type = "bit";
                    }
                    else if (dataBaseType.ToLower() == "mysql")
                    {
                        type = "tinyint";
                    }
                    else
                    {
                        type = "int";
                    }

                    break;
                case "object":
                    type = "varchar";
                    break;
                case "keyvalue":
                    type = "varchar";
                    break;
                case "file":
                case "ntext":
                    type = "text";
                    break;
                default:
                    type = "varchar";
                    break;
            }

            return type;
        }

        private static string SqlServerFormat(string dbType)
        {
            string type;
            switch (dbType.ToLower())
            {
                case "char":
                case "varchar":
                case "nchar":
                case "nvarchar":
                case "binary":
                case "varbinary":
                case "image":
                case "uniqueidentifier":
                case "hierarchyid":
                case "sql_variant":
                case "xml":
                case "geometry":
                case "geography":
                    type = "text";
                    break;
                case "text":
                case "ntext":
                    type = "ntext";
                    break;
                case "bit":
                    type = "bool";
                    break;
                case "bigint":
                case "numeric":
                case "smallint":
                case "decimal":
                case "smallmoney":
                case "int":
                case "money":
                case "float":
                case "real":
                    type = "number";
                    break;
                case "date":
                case "datetimeoffset":
                case "datetime2":
                case "datetime":
                case "smalldatetime":
                case "time":
                    type = "date";
                    break;
                default:
                    type = "text";
                    break;
            }

            return type;
        }

        private static string MySqlFormat(string dbType)
        {
            string type;
            switch (dbType.ToLower())
            {
                case "char":
                case "varchar":
                case "tinyblob":
                case "tinytext":
                case "mediumblob":
                case "mediumtext":
                    type = "text";
                    break;
                case "text":
                case "blob":
                case "longblob":
                case "longtext":
                    type = "ntext";
                    break;
                case "tinyint":
                    type = "bool";
                    break;
                case "smallint":
                case "mediumint":
                case "int":
                case "integer":
                case "bigint":
                case "float":
                case "double":
                case "decimal":
                    type = "number";
                    break;
                case "date":
                case "year":
                case "datetime":
                case "timestamp":
                case "time":
                    type = "date";
                    break;
                default:
                    type = "text";
                    break;
            }

            return type;
        }
    }
}
