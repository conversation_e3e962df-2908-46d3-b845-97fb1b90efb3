using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 建模
    /// </summary>
    [EntityTable("PageModelings")]
    public partial class PageModeling
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 页面名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 建模类型 1:查询列表 2:表单
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 页面设计json
        /// </summary>
        public string PageDesginJson { get; set; }

        /// <summary>
        /// 建模状态 0:未发布 1:已发布 2:已下架
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 数据源类型 1:数据表 2:视图 3:api
        /// </summary>
        public int DataSourceType { get; set; }

        /// <summary>
        /// 数据源
        /// </summary>
        public string DataSource { get; set; }

        /// <summary>
        /// 所属应用
        /// </summary>
        public Guid ApplicationId { get; set; }

        /// <summary>
        /// 模块
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// 所属应用名称
        /// </summary>
        public string ApplicationName { get; set; }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string ModuleName { get; set; }

        /// <summary>
        /// 应用设备
        /// </summary>
        public string ApplicationEquipment { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public int? Version { get; set; }
    }
}
