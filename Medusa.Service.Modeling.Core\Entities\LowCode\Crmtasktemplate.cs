using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// CRM任务模板表
    /// </summary>
    [SugarTable("crmtasktemplate")]
    public class Crmtasktemplate
    {
        /// <summary>
        /// 模版ID
        /// </summary>
        [SugarColumn(ColumnName = "templateid", IsPrimaryKey = true)]
        public Guid Templateid { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [SugarColumn(ColumnName = "templatename")]
        public string Templatename { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(ColumnName = "description")]
        public string Description { get; set; }

        /// <summary>
        /// 任务项配置JSON
        /// </summary>
        [SugarColumn(ColumnName = "taskConfigJson")]
        public string TaskConfigJson { get; set; }

        /// <summary>
        /// 是否删除 1已删除,0未删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 是否生效 1已发布,0待发布
        /// </summary>
        [SugarColumn(ColumnName = "isActive")]
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [SugarColumn(ColumnName = "createdUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "createdDate")]
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// 修改用户ID
        /// </summary>
        [SugarColumn(ColumnName = "modifiedUserId")]
        public string ModifiedUserId { get; set; }
    }
}
