using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 产品
    /// </summary>
    [EntityTable("crmproduct")]
    public class CRMProduct
    {
        /// <summary>
        /// '主键',
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// '修改用户Id',
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// '上级ID',
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// '产品编号',
        /// </summary>
        public string No { get; set; }

        /// <summary>
        /// '创建用户组织路径Id',
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// '产品大类名称',
        /// </summary>
        public string ProductTypeName { get; set; }

        /// <summary>
        /// '产品型号',
        /// </summary>
        public string ProductModel { get; set; }

        /// <summary>
        /// '克重',
        /// </summary>
        public decimal? Weight { get; set; }

        /// <summary>
        /// '创建日期',
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// '修改日期',
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// '存货代码',
        /// </summary>
        public string StockCode { get; set; }

        /// <summary>
        /// '描述',
        /// </summary>
        public string Desc { get; set; }

        /// <summary>
        /// '存货名称',
        /// </summary>
        public string StockName { get; set; }

        /// <summary>
        /// '产品别',
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// '附件',
        /// </summary>
        public string Files { get; set; }

        /// <summary>
        /// '创建用户Id',
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// '产品大类',
        /// </summary>
        public string ProductType { get; set; }

        /// <summary>
        /// '产品规格',
        /// </summary>
        public string ProductSpecifications { get; set; }

        /// <summary>
        /// '是否删除',
        /// </summary>
        public int IsDelete { get; set; }
    }
}
