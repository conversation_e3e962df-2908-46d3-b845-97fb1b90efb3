﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netcoreapp3.1</TargetFramework>
        <CodeAnalysisRuleSet>..\csharp.ruleset</CodeAnalysisRuleSet>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
        <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Modeling.Application.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
        <DocumentationFile>bin\Release\netcoreapp3.1\Medusa.Service.Modeling.Application.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="3.1.7" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="3.1.7" />
        <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="SmartFormat.NET" Version="2.4.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Medusa.Service.Modeling.Core\Medusa.Service.Modeling.Core.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\Medusa.Service.Cache\Medusa.Service.Cache.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.BPM.Reactor\MT.Enterprise.BPM.Reactor.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Core\MT.Enterprise.Core.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.SDK\MT.Enterprise.SDK.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Utils\MT.Enterprise.Utils.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Vision\MT.Enterprise.Vision.csproj" />
    </ItemGroup>

</Project>
