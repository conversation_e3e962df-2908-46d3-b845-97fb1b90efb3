using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// 其他任务历史跟进
    /// </summary>
    [SugarTable("crmtaskotherfollowuphistory")]
    public class Crmtaskotherfollowuphistory
    {
        /// <summary>
        /// 表中的唯一标识ID
        /// </summary>
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 任务主键ID
        /// </summary>
        [SugarColumn(ColumnName = "taskId")]
        public Guid TaskId { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        [SugarColumn(ColumnName = "taskStatus")]
        public string TaskStatus { get; set; }

        /// <summary>
        /// 跟进情况
        /// </summary>
        [SugarColumn(ColumnName = "followUpStatus")]
        public string FollowUpStatus { get; set; }

        /// <summary>
        /// 是否删除 1已删除,0未删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [SugarColumn(ColumnName = "createdUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "createdDate")]
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// 修改用户ID
        /// </summary>
        [SugarColumn(ColumnName = "modifiedUserId")]
        public string ModifiedUserId { get; set; }
    }
}
