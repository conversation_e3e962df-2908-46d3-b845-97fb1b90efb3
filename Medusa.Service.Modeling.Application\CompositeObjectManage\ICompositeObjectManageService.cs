using System;
using System.Data;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos;

namespace Medusa.Service.Modeling.Application.CompositeObjectManage
{
    
    
    
    public interface ICompositeObjectManageService : IServiceBase
    {
        
        
        
        
        
        PageResult<CompositeObjectDto> GetCompositeObjects(CompositeObjectQueryDto dto);

        
        
        
        
        
        CompositeObjectDto GetCompositeObject(Guid id);

        
        
        
        
        void SaveCompositeObject(CompositeObjectDto dto);

        
        
        
        
        void DeleteCompositeObject(Guid id);

        
        
        
        
        
        BOColumnDataDto GetCompositeObjectTreeList(Guid id);

        
        
        
        
        
        DataTable GetCompositeObjectDataList(BOColumnDataDto dto);
    }
}
