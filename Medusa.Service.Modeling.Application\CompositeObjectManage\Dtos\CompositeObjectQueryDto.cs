using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos
{
    
    
    
    public class CompositeObjectQueryDto : PageQueryDtoBase
    {
        
        
        
        public Guid? Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string BusinessObject { get; set; }

        
        
        
        public string ApplicationId { get; set; }
    }
}
