using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos
{
    
    
    
    public class BusinessObjectQueryDto : PageQueryDtoBase
    {
        
        
        
        public Guid? Id { get; set; }

        
        
        
        public Guid? DataBaseId { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string DataBaseName { get; set; }

        
        
        
        public string DataBaseType { get; set; }

        
        
        
        public int? State { get; set; }

        
        
        
        public bool? HasColumns { get; set; }

        
        
        
        public bool? IsEnable { get; set; }

        
        
        
        public bool? IsTree { get; set; }

        
        
        
        public string ApplicationId { get; set; }

        
        
        
        public string Description { get; set; }
    }
}
