using System;
using System.Collections.Generic;
using System.Dynamic;
using Medusa.Service.Modeling.Application.PageView.Dtos;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Application.PageView
{
    
    
    
    public interface IPageViewService : IServiceBase
    {
        
        
        
        
        
        List<ViewObjectResultDto> GetObject(ViewObjectQueryDto dto);

        
        
        
        
        
        
        List<ViewObjectResultDto> GetObjectField(Guid id, string objectType);
    }
}
