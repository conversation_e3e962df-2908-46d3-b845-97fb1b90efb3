using System;
using System.Collections.Generic;
using MT.Enterprise.Core.ORM;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 字典表
    /// </summary>
    [EntityTable("Dictionary")]
    public class Dictionary
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 字典编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 字典名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 字典值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int OrderNum { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 父Id
        /// </summary>
        public Guid? UpperId { get; set; }

        /// <summary>
        /// 分组编码
        /// </summary>
        public string TypeCode { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// 组织授权
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(DictionaryOrgPermission.DictionaryId))]
        public List<DictionaryOrgPermission> OrgPermission { get; set; }

        /// <summary>
        /// 人员授权
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [Navigate(NavigateType.OneToMany, nameof(DictionaryUserPermission.DictionaryId))]
        public List<DictionaryUserPermission> UserPermission { get; set; }
    }
}
