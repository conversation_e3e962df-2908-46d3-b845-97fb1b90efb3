using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 流程实例记录
    /// </summary>
    [EntityTable("ProcessInstanceRecord")]
    public class ProcessInstanceRecord
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// DataBaseId
        /// </summary>
        public string DataBaseId { get; set; }

        /// <summary>
        /// BusinessObjectId
        /// </summary>
        public string BusinessObjectId { get; set; }

        /// <summary>
        /// BusinessObjectName
        /// </summary>
        public string BusinessObjectName { get; set; }

        /// <summary>
        /// DataId
        /// </summary>
        public string DataId { get; set; }

        /// <summary>
        /// Data
        /// </summary>
        public string Data { get; set; }

        /// <summary>
        /// CreateDate
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// CreateUserId
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// ModifyDate
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// ModifyUserId
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// Topic
        /// </summary>
        public string Topic { get; set; }

        /// <summary>
        /// ProcInstNo
        /// </summary>
        public string ProcInstNo { get; set; }

        /// <summary>
        /// ProcInstStatus
        /// </summary>
        public string ProcInstStatus { get; set; }

        /// <summary>
        /// ProcInstStatusName
        /// </summary>
        public string ProcInstStatusName { get; set; }

        /// <summary>
        /// OperationStatus
        /// </summary>
        public string OperationStatus { get; set; }

        /// <summary>
        /// CallBackSetting
        /// </summary>
        public string CallBackSetting { get; set; }

        /// <summary>
        /// Btid
        /// </summary>
        public string Btid { get; set; }

        /// <summary>
        /// Boid
        /// </summary>
        public string Boid { get; set; }

        /// <summary>
        /// Bsid
        /// </summary>
        public string Bsid { get; set; }
    }
}
