using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.ViewManage;
using Medusa.Service.Modeling.Application.ViewManage.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// view controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "ViewManage.v1")]
    public class V1_ViewManageController : ProductControllerBase
    {
        #region //  服务注入
        readonly IViewManageService _viewManageService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_DataBaseManageController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="viewManageService">viewManageService</param>
        public V1_ViewManageController(IViewManageService viewManageService)
        {
            _viewManageService = viewManageService;
        }
        #endregion

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("views")]
        public PageResult<ViewDto> GetList([FromQuery] ViewQueryDto dto)
        {
            return _viewManageService.GetList(dto);
        }

        /// <summary>
        /// 获取视图
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpGet("views/{id}")]
        public ViewDto GetView([FromRoute] Guid id)
        {
            return _viewManageService.GetView(id);
        }

        /// <summary>
        /// 获取视图字段
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpGet("views/columns/{id}")]
        public List<ViewColumns> GetViewColumns([FromRoute] Guid id)
        {
            return _viewManageService.GetViewColumns(id);
        }

        /// <summary>
        /// 新增/修改视图
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("views/save")]
        public void SaveView([FromBody] ViewDto dto)
        {
            _viewManageService.SaveView(dto);
        }

        /// <summary>
        /// 删除视图
        /// </summary>
        /// <param name="id">id</param>
        [HttpDelete("views/{id}")]
        public void DeleteView([FromRoute] Guid id)
        {
            _viewManageService.DeleteView(id);
        }

        /// <summary>
        /// 发布视图
        /// </summary>
        /// <param name="id">id</param>
        [HttpGet("views/publish/{id}")]
        public void PublishView([FromRoute] Guid id)
        {
            _viewManageService.PublishView(id);
        }

        /// <summary>
        /// 获取视图列表For组合对象
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("views/composite")]
        public PageResult<ViewForCompositeDto> GetListForComposite([FromQuery] ViewQueryForCompositeDto dto)
        {
            return _viewManageService.GetListForComposite(dto);
        }

        /// <summary>
        /// 获取字典级联树
        /// </summary>
        /// <returns>结果</returns>
        [HttpGet("views/dictionar-cascadery-tree")]
        public JArray GetDictionarCascaderyTree()
        {
            return _viewManageService.GetDictionarCascaderyTree();
        }
    }
}
