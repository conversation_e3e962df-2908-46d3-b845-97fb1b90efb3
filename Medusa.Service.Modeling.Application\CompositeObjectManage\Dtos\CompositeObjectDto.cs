using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos
{
    
    
    
    public class CompositeObjectDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        [Required]
        public string Name { get; set; }

        
        
        
        [Required]
        public Guid DataBaseId { get; set; }

        
        
        
        public string DataBaseName { get; set; }

        
        
        
        [Required]
        public Guid BusinessObjectId { get; set; }

        
        
        
        public string BusinessObjectName { get; set; }

        
        
        
        public string BusinessObjectDescription { get; set; }

        
        
        
        public List<CompositeObjectRelationDto> CompositeObjectRelation { get; set; }

        
        
        
        [Required]
        public string ApplicationId { get; set; }

        
        
        
        [Required]
        public string ApplicationName { get; set; }

        
        
        
        public string ObjectType { get; set; }
    }
}
