using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 业务对象信息
    /// </summary>
    [EntityTable("BusinessObjects")]
    public partial class BusinessObject
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 业务对象名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 业务对象描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 数据库管理Id
        /// </summary>
        public Guid DataBaseId { get; set; }

        /// <summary>
        /// 是否自定义
        /// </summary>
        public bool IsCustom { get; set; }

        /// <summary>
        /// 状态 0:未发布 1:已发布
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDelete { get; set; }

        /// <summary>
        /// 是否树形结构
        /// </summary>
        public bool? IsTree { get; set; }

        /// <summary>
        /// 是否逻辑删除
        /// </summary>
        public bool? IsLogicalDelete { get; set; }

        /// <summary>
        /// ApplicationId
        /// </summary>
        public string ApplicationId { get; set; }

        /// <summary>
        /// ApplicationName
        /// </summary>
        public string ApplicationName { get; set; }
    }
}
