using System.Collections.Generic;
using Medusa.Service.Modeling.Core.Entity;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Application.ViewManage.Dtos
{
    
    
    
    public class ViewDto : View
    {
        
        
        
        public string AppName { get; set; }

        
        
        
        public string DatabaseName { get; set; }

        
        
        
        public string StateName
        {
            get
            {
                string result = string.Empty;
                if (State == 0)
                {
                    result = "已删除";
                }

                if (State == 1)
                {
                    result = "未发布";
                }

                if (State == 2)
                {
                    result = "已发布";
                }

                return result;
            }
        }

        
        
        
        public List<ViewRelations> Relations { get; set; } = new List<ViewRelations>();

        
        
        
        public List<ViewFilters> Filters { get; set; } = new List<ViewFilters>();

        
        
        
        public List<ViewColumns> Columns { get; set; } = new List<ViewColumns>();

        
        
        
        public List<ViewOrders> Orders { get; set; } = new List<ViewOrders>();

        
        
        
        public JObject ViewJson { get; set; }
    }
}
