using System;
using System.Threading.Tasks;
using Medusa.Service.Modeling.Application.ProductRegistration.Dto;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.SDK.ApiRequest;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Application.ProductRegistration
{
    public class ProductRegistrationService : IProductRegistrationService
    {
        private readonly IMemoryCache _cache;
        private readonly IRequestConnector _requestConnector;

        private readonly string _productLicenseApi;

        public ProductRegistrationService(IServiceProvider serviceProvider)
        {
            _cache = serviceProvider.GetService<IMemoryCache>();
            var appSettings = _cache.Get<JObject>("AppSettings");
            _requestConnector = serviceProvider.GetService<IRequestConnector>();
            _productLicenseApi = appSettings["PlatformSettings"]?["ProductLicense"]?.ToString();
        }

        public async Task ProductRegistrationCheck()
        {
            if (_cache.Get("bpm_product_license_key") != null)
            {
                return;
            }

            var requestId = Guid.NewGuid();
            var result = (await _requestConnector.GetAsync<string>(_productLicenseApi.Replace("{id}", requestId.ToString()))).ResouceContent;

            if (result != null)
            {
                result = result.TrimStart('"').TrimEnd('"');
            }

            if (!string.IsNullOrEmpty(result))
            {
                var encryptDto = MT.Enterprise.Utils.Crypto.Base64Decrypt(result);
                var licenseDtoString = MT.Enterprise.Utils.Crypto.DESDecrypt(encryptDto);
                var licenseDto = JsonConvert.DeserializeObject<LicenseDto>(licenseDtoString);
                TimeSpan span = DateTime.Now - licenseDto.RequestTime;
                if (licenseDto.RequestId == requestId && span.Minutes <= 5)
                {
                    licenseDto.Manufacturer = "盟拓软件(苏州)有限公司";
                    _cache.Set("bpm_product_license_key", licenseDto, TimeSpan.FromHours(2));
                }
            }
        }

        
        
        
        
        public LicenseDto GetProductLicense()
        {
            ProductRegistrationCheck();
            if (_cache.Get("bpm_product_license_key") != null)
            {
                return _cache.Get<LicenseDto>("bpm_product_license_key");
            }

            return null;
        }
    }
}