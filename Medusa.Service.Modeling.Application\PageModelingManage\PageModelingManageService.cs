using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.DirectoryServices;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using System.Security.Cryptography;
using System.Text;
using Consul;
using Medusa.Service.Cache.Entities.Platform;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.DynamicSql;
using Medusa.Service.Modeling.Application.OperationLog;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Application.ViewManage.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.Boost;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Core.ORM.Queryable.Dtos;
using MT.Enterprise.Utils.Extensions;
using MySqlX.XDevAPI.Relational;
using Nacos.Utilities;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.Util;
using NPOI.SS.Formula.Functions;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Org.BouncyCastle.Utilities.Collections;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.PageModelingManage
{
    
    
    
    public class PageModelingManageService : ServiceBase, IPageModelingManageService
    {
        #region 
        readonly MyDbContext _dbContext;
        private readonly IStructureCacheService _structureCacheService;
        private readonly IOperationLogService _operationLogService;
        readonly IDynamicSqlService _dynamicSqlService;

        
        
        
        
        
        public PageModelingManageService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _structureCacheService = serviceProvider.GetService<IStructureCacheService>();
            _operationLogService = serviceProvider.GetService<IOperationLogService>();
            _dynamicSqlService = serviceProvider.GetService<IDynamicSqlService>();
        }

        #endregion

        #region 建模

        
        
        
        
        
        public PageModelingDto GetPageModeling(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            if (item.Status == 2)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.undercarriage"));
            }

            var result = item.MapTo<PageModelingDto>();
            var tableType = item.DataSource.Split("||")[2];
            var tables = this.GetBusinessObjectFields(new ObjectColumnQueryDto
            {
                ObjectId = item.DataSource.Split("||")[1].ToOurGuid(),
                ObjectType = tableType.ToOurInt()
            });
            result.Tables = tables;
            return result;
        }

        
        
        
        
        
        
        public PageModelingDto GetPageModelingPreview(Guid id, Guid versionId)
        {
            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            var result = item.MapTo<PageModelingDto>();
            if (versionId != null && versionId != Guid.Empty)
            {
                var version = _dbContext.Modeling.Queryable<PageVersion>().InSingle(versionId);
                if (version == null)
                {
                    throw new StatusNotFoundException("页面版本信息不存在");
                }

                result.PageDesginJson = version.PageDesginJson;
                result.Status = version.Status;
                result.Version = version.Version;
                result.VersionId = versionId;
                if (!string.IsNullOrEmpty(version.DataSource))
                {
                    result.DataSource = version.DataSource;
                }
            }

            var tableType = result.DataSource.Split("||")[2];
            var tables = this.GetBusinessObjectFields(new ObjectColumnQueryDto
            {
                ObjectId = result.DataSource.Split("||")[1].ToOurGuid(),
                ObjectType = tableType.ToOurInt()
            });
            result.Tables = tables;
            return result;
        }

        
        
        
        
        
        public PageResult<PageModelingDto> GetPageModelings(PageModelingQueryDto dto)
        {
            var datsSource = $"{dto.ObjectType}||{dto.ObjectType}";
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<PageModeling>()
                   .LeftJoin<PageVersion>((pm, pv) => pm.Id == pv.PageModelingId)
                   .WhereIF(!string.IsNullOrEmpty(dto.Name), (pm, pv) => pm.Name.Contains(dto.Name))
                   .WhereIF(dto.Key.HasValue && dto.Key.Value != Guid.Empty, (pm, pv) => pm.Id == dto.Key)
                   .WhereIF(dto.Status.HasValue, (pm, pv) => pm.Status == dto.Status)
                   .WhereIF(dto.ApplicationId.HasValue && dto.ApplicationId.Value != Guid.Empty, (pm, pv) => pm.ApplicationId == dto.ApplicationId.Value)
                   .WhereIF(dto.ModuleId.HasValue && dto.ModuleId.Value != Guid.Empty, (pm, pv) => pm.ModuleId == dto.ModuleId)
                   .WhereIF(!string.IsNullOrEmpty(dto.ApplicationEquipment), (pm, pv) => SqlFunc.IsNull(pm.ApplicationEquipment, "PC") == dto.ApplicationEquipment)
                   .WhereIF(string.IsNullOrEmpty(dto.VersionType) || dto.VersionType == "current", (pm, pv) => SqlFunc.IsNull(pv.Version, 1) == pm.Version)
                   .WhereIF(!string.IsNullOrEmpty(dto.ObjectId) && !string.IsNullOrEmpty(dto.ObjectType), (pm, pv) => SqlFunc.IsNull(pv.DataSource, pm.DataSource).Contains(datsSource))
                    .OrderBy((pm, pv) => pm.CreateDate, SqlSugar.OrderByType.Desc)
                   .OrderBy((pm, pv) => SqlFunc.IsNull(pv.Version, pm.Version))
                   .Select((pm, pv) => new PageModelingDto
                   {
                       ApplicationEquipment = pm.ApplicationEquipment,
                       ApplicationId = pm.ApplicationId,
                       ApplicationName = pm.ApplicationName,
                       CreateDate = pm.CreateDate,
                       DataSource = SqlFunc.IsNull(pv.DataSource, pm.DataSource),
                       DataSourceType = pm.DataSourceType,
                       Id = pm.Id,
                       ModuleId = pm.ModuleId,
                       ModuleName = pm.ModuleName,
                       Name = pm.Name,
                       Status = pm.Status,
                       Type = pm.Type,
                       Version = SqlFunc.IsNull(pv.Version, pm.Version),
                       VersionId = pv.Id
                   });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<PageModelingDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        
        public Guid SavePageModeling(PageModelingDto dto)
        {
            var hasData = _dbContext.Modeling.Queryable<PageModeling>()
                 .Where(a => a.Name == dto.Name).Any();
            if (hasData)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.nameExist"));
            }

            var currentUserId = UserConstants.CurrentUser.Value.Account;
            var item = dto.MapTo<PageModeling>();
            item.Id = Guid.NewGuid();
            DateTime now = DateTime.Now;
            item.CreateDate = now;
            item.UpdateDate = now;
            item.CreateUser = currentUserId;
            item.UpdateUser = currentUserId;
            item.PageDesginJson = dto.PageDesginJson;
            item.Version = 1;
            _dbContext.Modeling.Insertable(item).ExecuteCommand();

            var version = new PageVersion()
            {
                Id = Guid.NewGuid(),
                PageModelingId = item.Id,
                PageDesginJson = item.PageDesginJson,
                Status = 0,
                Version = 1,
                CreateDate = now,
                UpdateDate = now,
                CreateUser = currentUserId,
                UpdateUser = item.UpdateUser,
                DataSource = item.DataSource
            };
            _dbContext.Modeling.Insertable(version).ExecuteCommand();
            return item.Id;
        }

        
        
        
        
        
        public Guid UpdatePageModelingDesgin(PageModelingDto dto)
        {
            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(dto.Id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            var hasData = _dbContext.Modeling.Queryable<PageModeling>()
               .Where(a => a.Name == dto.Name && a.Id != dto.Id).Any();
            if (hasData)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.nameExist"));
            }

            PageVersion pageVersion = null;
            if (dto.VersionId.HasValue && dto.VersionId != Guid.Empty)
            {
                pageVersion = _dbContext.Modeling.Queryable<PageVersion>().InSingle(dto.VersionId.Value);
                if (pageVersion == null)
                {
                    throw new StatusNotFoundException("页面版本信息不存在");
                }
            }
            else if (item.Version.HasValue)
            {
                pageVersion = _dbContext.Modeling.Queryable<PageVersion>().Where(w => w.PageModelingId == item.Id && w.Version == item.Version.Value).First();
            }

            DateTime now = DateTime.Now;
            var currentUserId = UserConstants.CurrentUser.Value.Account;
            PageVersion currentEditItem = null;

            
            if (pageVersion == null || pageVersion.Status == 1)
            {
                var maxPageVersion = _dbContext.Modeling.Queryable<PageVersion>()
                .Where(w => w.PageModelingId == item.Id)
                .OrderBy(w => w.Version, OrderByType.Desc)
                .First();

                currentEditItem = new PageVersion()
                {
                    Id = Guid.NewGuid(),
                    PageModelingId = item.Id,
                    PageDesginJson = dto.PageDesginJson,
                    Status = dto.OptType == "save" ? 0 : 1,
                    Version = maxPageVersion != null ? maxPageVersion.Version + 1 : 1,
                    CreateDate = now,
                    UpdateDate = now,
                    CreateUser = currentUserId,
                    UpdateUser = item.UpdateUser,
                    DataSource = item.DataSource
                };

                if (currentEditItem.Version > 1 && pageVersion != null)
                {
                    currentEditItem.SourceVersion = pageVersion.Version;
                }

                _dbContext.Modeling.Insertable(currentEditItem).ExecuteCommand();
            }
            else
            {
                pageVersion.PageDesginJson = dto.PageDesginJson;
                pageVersion.Status = dto.OptType == "save" ? 0 : 1;
                pageVersion.UpdateDate = now;
                pageVersion.UpdateUser = currentUserId;
                _dbContext.Modeling.Updateable(pageVersion).ExecuteCommand();
                currentEditItem = pageVersion;
            }

            if (dto.OptType == "save" && item.Status == 0 && pageVersion != null && item.Version == pageVersion.Version)
            {
                item.PageDesginJson = dto.PageDesginJson;
                item.UpdateDate = now;
                item.UpdateUser = currentUserId;
                _dbContext.Modeling.Updateable(item).ExecuteCommand();
            }
            else if (dto.OptType == "publish" && pageVersion != null && item.Version == pageVersion.Version)
            {
                item.Version = currentEditItem.Version;
                item.PageDesginJson = dto.PageDesginJson;
                item.UpdateDate = now;
                item.UpdateUser = currentUserId;
                item.Status = item.Status == 2 ? 2 : 1;
                _dbContext.Modeling.Updateable(item).ExecuteCommand();
            }

            return currentEditItem.Id;
        }

        
        
        
        
        public void Import(ExportImportTemplateDto dto)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().Where(w => w.OnlyCode == "Default").First();
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var ids = dto.BusinessObjectId.Split(";").ToList();
            var boc = _dbContext.Modeling.Queryable<BusinessObject, BusinessObjectColumn>((bo, boc) => bo.Id == boc.BusinessObjectId)
                .Where((bo, boc) => ids.Contains(bo.Id.ToString()) && bo.IsCustom)
                .OrderBy((bo, boc) => boc.IsSystemColumn.Value)
                .OrderBy((bo, boc) => boc.Order)
                .Select((bo, boc) => new
                {
                    businessObjectId = bo.Id,
                    businessObjectName = bo.Name,
                    businessObjectDescription = bo.Description,
                    columnName = boc.Name,
                    columnDescription = boc.Description,
                    boc.IsSystemColumn,
                    boc.IsPrimaryKey
                })
                .ToList();

            if (dto.File != null && boc != null && boc.Count > 0)
            {
                IWorkbook workbook = new XSSFWorkbook(dto.File.OpenReadStream());
                if (workbook.NumberOfSheets > 0)
                {
                    ids.ForEach(f =>
                    {
                        var columns = boc.FindAll(fa => fa.businessObjectId == Guid.Parse(f));
                        if (columns != null && columns.Count > 0)
                        {
                            ISheet sheet = workbook.GetSheet(columns[0].businessObjectName);
                            int headerCellCount = sheet.GetRow(0).LastCellNum;
                            int dataRowIndex = 2;
                            var dics = new List<Dictionary<string, object>>();
                            var dic_par = new Dictionary<string, List<SugarParameter>>();

                            
                            for (var i = dataRowIndex; i < sheet.LastRowNum + 1; i++)
                            {
                                var dic = new Dictionary<string, object>();
                                var dataRow = sheet.GetRow(i);
                                for (var j = 0; j < headerCellCount; j++)
                                {
                                    var header = sheet.GetRow(0).GetCell(j).StringCellValue;

                                    
                                    var column = columns.Where(a => a.columnName == header).FirstOrDefault();
                                    if (column == null)
                                    {
                                        continue;
                                    }

                                    var dataCel = dataRow.GetCell(j);
                                    if (dataCel != null && !dic.ContainsKey(header))
                                    {
                                        switch (dataCel.CellType)
                                        {
                                            case CellType.Numeric:
                                                if (DateUtil.IsCellDateFormatted(dataCel))
                                                {
                                                    dic.Add(header, dataCel.DateCellValue);
                                                }
                                                else
                                                {
                                                    dic.Add(header, dataCel.NumericCellValue);
                                                }

                                                break;
                                            case CellType.String:
                                                dic.Add(header, dataCel.StringCellValue);
                                                break;
                                            case CellType.Boolean:
                                                dic.Add(header, dataCel.BooleanCellValue);
                                                break;
                                        }
                                    }
                                }

                                var primaryKey = columns.Where(a => a.IsPrimaryKey.Value).FirstOrDefault();

                                
                                if (dic.ContainsKey(primaryKey.columnName))
                                {
                                    dic[primaryKey.columnName] = Guid.NewGuid().ToString();
                                }
                                else
                                {
                                    dic.Add(primaryKey.columnName, Guid.NewGuid().ToString());
                                }

                                var createDate = columns.Where(a => a.IsSystemColumn.Value && a.columnName == "CreateDate").FirstOrDefault();
                                if (createDate != null)
                                {
                                    if (dic.ContainsKey(createDate.columnName) && string.IsNullOrEmpty(dic[createDate.columnName].ToString()))
                                    {
                                        dic[createDate.columnName] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                                    }
                                    else if (!dic.ContainsKey(createDate.columnName))
                                    {
                                        dic.Add(createDate.columnName, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                                    }
                                }

                                var createUserId = columns.Where(a => a.IsSystemColumn.Value && a.columnName == "CreateUserId").FirstOrDefault();
                                if (createUserId != null)
                                {
                                    if (dic.ContainsKey(createUserId.columnName) && string.IsNullOrEmpty(dic[createUserId.columnName].ToString()))
                                    {
                                        dic[createUserId.columnName] = dto.UserId;
                                    }
                                    else if (!dic.ContainsKey(createUserId.columnName))
                                    {
                                        dic.Add(createUserId.columnName, dto.UserId);
                                    }
                                }

                                var createUserOrgPathId = columns.Where(a => a.IsSystemColumn.Value && a.columnName == "CreateUserOrgPathId").FirstOrDefault();
                                if (createUserOrgPathId != null)
                                {
                                    if (dic.ContainsKey(createUserOrgPathId.columnName) && string.IsNullOrEmpty(dic[createUserOrgPathId.columnName].ToString()))
                                    {
                                        dic[createUserOrgPathId.columnName] = dto.UserOrgPathId;
                                    }
                                    else if (!dic.ContainsKey(createUserOrgPathId.columnName))
                                    {
                                        dic.Add(createUserOrgPathId.columnName, dto.UserOrgPathId);
                                    }
                                }

                                var isDelete = columns.Where(a => a.IsSystemColumn.Value && a.columnName == "IsDelete").FirstOrDefault();
                                if (isDelete != null)
                                {
                                    if (dic.ContainsKey(isDelete.columnName) && string.IsNullOrEmpty(dic[isDelete.columnName].ToString()))
                                    {
                                        dic[isDelete.columnName] = 0;
                                    }
                                    else if (!dic.ContainsKey(isDelete.columnName))
                                    {
                                        dic.Add(isDelete.columnName, 0);
                                    }
                                }

                                dics.Add(dic);
                            }

                            if (dics != null && dics.Count > 0)
                            {
                                SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
                                client.Insertable(dics).AS(columns[0].businessObjectName).ExecuteCommand();
                            }
                        }
                    });
                }

                _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                {
                    Menu = dto.Menu,
                    Action = dto.Action,
                    OperationType = "导入数据"
                });
            }
        }

        
        
        
        
        public void DeletePageModelings(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            _dbContext.Modeling.Deleteable(item).ExecuteCommand();
        }

        
        
        
        
        
        public List<ObjectGroupDto> GetBusinessObjects(ObjectQueryDto dto)
        {
            var result = new List<ObjectGroupDto>();
            if (string.IsNullOrEmpty(dto.ObjectType))
            {
                dto.ObjectType = "1;2;3";
            }

            var strs = dto.ObjectType.Split(";").ToList();
            strs.ForEach(f =>
            {
                if (f == "1")
                {
                    var businessObjects_l = _dbContext.Modeling.Queryable<BusinessObject>()
                      .Where(a => !a.IsDelete.Value && a.State == 1 && a.DataBaseId == dto.DataBaseId && (SqlFunc.IsNullOrEmpty(a.ApplicationId) || a.ApplicationId.Contains(SqlFunc.ToString(dto.ApplicationId))))
                      .WhereIF(dto.IsTreeObject.HasValue && dto.IsTreeObject.Value, a => a.IsTree.Value)
                      .WhereIF(dto.Id.HasValue, a => a.Id == dto.Id)
                      .Select(a => new ObjectItemDto
                      {
                          ObjectId = a.Id,
                          ObjectName = a.Name,
                          ObjectDescription = a.Description,
                          IsTree = a.IsTree
                      }).ToList();

                    if (businessObjects_l.Count > 0)
                    {
                        if (dto.IsHasColumns.HasValue && dto.IsHasColumns.Value && !string.IsNullOrEmpty(dto.HasColumnObjectId))
                        {
                            var os = dto.HasColumnObjectId.Split(";").ToList();
                            os.ForEach(o =>
                            {
                                var chi = businessObjects_l.Where(w => w.ObjectId == o.ToOurGuid()).FirstOrDefault();
                                if (chi != null)
                                {
                                    chi.Structure = this.GetBusinessObjectFields(new ObjectColumnQueryDto
                                    {
                                        ObjectId = o.ToOurGuid(),
                                        ObjectType = 1
                                    });
                                }
                            });
                        }

                        result.Add(new ObjectGroupDto
                        {
                            ObjectType = 1,
                            Children = businessObjects_l
                        });
                    }
                }
                else if (f == "2")
                {
                    var compositeObjects_l = _dbContext.Modeling.Queryable<CompositeObject>()
                       .Where(a => !a.IsDelete.Value && a.DataBaseId == dto.DataBaseId && a.ApplicationId == SqlFunc.ToString(dto.ApplicationId))
                       .WhereIF(dto.Id.HasValue, a => a.Id == dto.Id)
                       .Select(a => new ObjectItemDto
                       {
                           ObjectId = a.Id,
                           ObjectName = a.Name,
                           ObjectDescription = a.Name,
                       }).ToList();

                    if (compositeObjects_l.Count > 0)
                    {
                        if (dto.IsHasColumns.HasValue && dto.IsHasColumns.Value && !string.IsNullOrEmpty(dto.HasColumnObjectId))
                        {
                            var os = dto.HasColumnObjectId.Split(";").ToList();
                            os.ForEach(o =>
                            {
                                var chi = compositeObjects_l.Where(w => w.ObjectId == o.ToOurGuid()).FirstOrDefault();
                                if (chi != null)
                                {
                                    chi.Structure = this.GetBusinessObjectFields(new ObjectColumnQueryDto
                                    {
                                        ObjectId = o.ToOurGuid(),
                                        ObjectType = 2
                                    });
                                }
                            });
                        }

                        result.Add(new ObjectGroupDto
                        {
                            ObjectType = 2,
                            Children = compositeObjects_l
                        });
                    }
                }
                else if (f == "3")
                {
                    var compositeObjects_v = _dbContext.Modeling.Queryable<View>()
                     .Where(a => a.State == 2 && a.DatabaseId == dto.DataBaseId && (SqlFunc.IsNullOrEmpty(a.AppId) || a.AppId == dto.ApplicationId))
                     .WhereIF(dto.Id.HasValue, a => a.Id == dto.Id)
                     .Select(a => new ObjectItemDto
                     {
                         ObjectId = a.Id,
                         ObjectName = a.Name,
                         ObjectDescription = a.Description,
                     }).ToList();

                    if (compositeObjects_v.Count > 0)
                    {
                        if (dto.IsHasColumns.HasValue && dto.IsHasColumns.Value && !string.IsNullOrEmpty(dto.HasColumnObjectId))
                        {
                            var os = dto.HasColumnObjectId.Split(";").ToList();
                            os.ForEach(o =>
                            {
                                var chi = compositeObjects_v.Where(w => w.ObjectId == o.ToOurGuid()).FirstOrDefault();
                                if (chi != null)
                                {
                                    chi.Structure = this.GetBusinessObjectFields(new ObjectColumnQueryDto
                                    {
                                        ObjectId = o.ToOurGuid(),
                                        ObjectType = 3
                                    });
                                }
                            });
                        }

                        result.Add(new ObjectGroupDto
                        {
                            ObjectType = 3,
                            Children = compositeObjects_v
                        });
                    }
                }
            });
            return result;
        }

        
        
        
        
        
        public List<ObjectStructureDto> GetBusinessObjectFields(ObjectColumnQueryDto dto)
        {
            var result = new List<ObjectStructureDto>();
            string json = _structureCacheService.GetRedis(new StructureCache.Dtos.StructureCacheSetDto()
            {
                BusinessObjectId = dto.ObjectId,
                BusinessObjectType = dto.ObjectType
            });
            if (!string.IsNullOrEmpty(json))
            {
                result = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ObjectStructureDto>>(json);
            }

            return result;
        }

        
        
        
        
        public void CopyPageModeling(PageModelingDto dto)
        {
            var hasData = _dbContext.Modeling.Queryable<PageModeling>()
                .Where(a => a.Name == dto.Name).Any();
            if (hasData)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.nameExist"));
            }

            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(dto.Id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            var currentUserId = UserConstants.CurrentUser.Value.Account;
            var now = DateTime.Now;
            item.Name = dto.Name;
            item.ApplicationId = dto.ApplicationId;
            item.ApplicationName = dto.ApplicationName;
            item.ModuleId = dto.ModuleId;
            item.ModuleName = dto.ModuleName;
            item.Id = Guid.NewGuid();
            item.CreateDate = now;
            item.UpdateDate = null;
            item.CreateUser = currentUserId;
            item.UpdateUser = string.Empty;
            item.Status = 0;
            item.Version = 1;
            _dbContext.Modeling.Insertable(item).ExecuteCommand();

            var version = new PageVersion()
            {
                Id = Guid.NewGuid(),
                PageModelingId = item.Id,
                PageDesginJson = item.PageDesginJson,
                Status = 0,
                Version = 1,
                CreateDate = now,
                CreateUser = currentUserId,
                DataSource = item.DataSource
            };
            _dbContext.Modeling.Insertable(version).ExecuteCommand();
        }

        
        
        
        
        
        public IWorkbook ExportImportTemplate(ExportImportTemplateDto dto)
        {
            var ids = dto.BusinessObjectId.Split(";").ToList();
            var boc = _dbContext.Modeling.Queryable<BusinessObject, BusinessObjectColumn>((bo, boc) => bo.Id == boc.BusinessObjectId)
                .Where((bo, boc) => ids.Contains(bo.Id.ToString()) && bo.IsCustom && !boc.IsPrimaryKey.Value)
                .OrderBy((bo, boc) => boc.IsSystemColumn.Value)
                .OrderBy((bo, boc) => boc.Order)
                .Select((bo, boc) => new
                {
                    businessObjectId = bo.Id,
                    businessObjectName = bo.Name,
                    businessObjectDescription = bo.Description,
                    columnName = boc.Name,
                    columnDescription = boc.Description,
                })
                .ToList();
            IWorkbook workbook = new XSSFWorkbook();
            ids.ForEach(f =>
            {
                var columns = boc.FindAll(fa => fa.businessObjectId == Guid.Parse(f));
                if (columns != null && columns.Count > 0)
                {
                    ISheet sheet = workbook.CreateSheet(columns[0].businessObjectName);
                    IRow firstTitle = sheet.CreateRow(0);
                    IRow secondTitle = sheet.CreateRow(1);
                    (ICellStyle borderStyle, ICellStyle titleStyle) = GetExcelStyle(workbook);
                    int i = 0;
                    columns.ForEach(cf =>
                    {
                        var cellFirstRow = firstTitle.CreateCell(i);
                        cellFirstRow.SetCellValue(cf.columnName);
                        cellFirstRow.CellStyle = titleStyle;

                        var cellSecondRow = secondTitle.CreateCell(i);
                        cellSecondRow.SetCellValue(cf.columnDescription);
                        cellSecondRow.CellStyle = titleStyle;
                        i++;
                    });
                }
            });

            return workbook;
        }

        
        
        
        
        public void Undercarriage(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            var currentUserId = UserConstants.CurrentUser.Value.Account;
            item.UpdateDate = DateTime.Now;
            item.UpdateUser = currentUserId;
            item.Status = 2;
            _dbContext.Modeling.Updateable(item).ExecuteCommand();
        }

        
        
        
        
        public void Publish(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            var currentUserId = UserConstants.CurrentUser.Value.Account;
            item.UpdateDate = DateTime.Now;
            item.UpdateUser = currentUserId;
            item.Status = 1;
            _dbContext.Modeling.Updateable(item).ExecuteCommand();
        }

        
        
        
        
        
        
        
        public (string, string, string, string) GenClass([FromQuery] string tableName, [FromQuery] string namespaceKey, [FromBody] DataBase db)
        {
            SqlSugarClient client = CustomDbClient.CustomDB(db);
            var columns = client.DbMaintenance.GetColumnInfosByTableName(tableName);
            StringBuilder sbCode = new StringBuilder();
            sbCode.AppendLine("using System;");
            sbCode.AppendLine("using System.Text;");
            sbCode.AppendLine($"namespace Com.Movitech.BPM.AbstractModel.From.{namespaceKey}");
            sbCode.AppendLine("{");
            sbCode.AppendLine("/// <summary>");
            sbCode.AppendLine($"/// {tableName}");
            sbCode.AppendLine("/// </summary>");
            sbCode.AppendLine($"public interface I{tableName} : IBaseEntity");
            sbCode.AppendLine("{");
            columns.ForEach(f =>
            {
                var type = "string";
                switch (f.DataType)
                {
                    case "bigint":
                        type = "long";
                        break;
                    case "datetime":
                        type = "DateTime";
                        break;
                }

                sbCode.AppendLine($"{type} {f.DbColumnName} {{ get; set; }}");
            });
            sbCode.AppendLine("}");
            sbCode.AppendLine("}");

            StringBuilder sbEntity = new StringBuilder();
            sbEntity.AppendLine("using System;");
            sbEntity.AppendLine("using Com.Movitech.BPM.AbstractModel;");
            sbEntity.AppendLine($"using Com.Movitech.BPM.AbstractModel.From.{namespaceKey};");
            sbEntity.AppendLine("using Com.Movitech.ORM.Common;");
            sbEntity.AppendLine("using Com.Movitech.ORM.Framework;");
            sbEntity.AppendLine($"namespace Com.Movitech.BPM.Entity.From.{namespaceKey}");
            sbEntity.AppendLine("{");
            sbEntity.AppendLine("/// <summary>");
            sbEntity.AppendLine($"/// {tableName}");
            sbEntity.AppendLine("/// </summary>");
            sbEntity.AppendLine($"[Serializable, TableName(\"{tableName}\", CacheStrategy.Temporary)]");
            sbEntity.AppendLine($"public class {tableName} : BaseEntity, I{tableName}, IBaseEntity");
            sbEntity.AppendLine("{");
            columns.ForEach(f =>
            {
                var type = "string";
                switch (f.DataType)
                {
                    case "bigint":
                        type = "long";
                        break;
                    case "datetime":
                        type = "DateTime";
                        break;
                }

                sbEntity.AppendLine($"private {type} {f.DbColumnName.First().ToString().ToLower()}{f.DbColumnName.Substring(1)} {{ get; set; }}");
            });
            sbEntity.AppendLine("public override void Persist()");
            sbEntity.AppendLine("{");
            sbEntity.AppendLine("base.Persist();");
            sbEntity.AppendLine("}");
            columns.ForEach(f =>
            {
                var type = "string";
                switch (f.DataType)
                {
                    case "bigint":
                        type = "long";
                        break;
                    case "datetime":
                        type = "DateTime";
                        break;
                }

                sbEntity.AppendLine($"[TableColumn(\"{f.DbColumnName}\", true)]");
                sbEntity.AppendLine($"public {type} {f.DbColumnName}");
                sbEntity.AppendLine($"{{");
                sbEntity.AppendLine($"get");
                sbEntity.AppendLine($"{{");
                sbEntity.AppendLine($"return this.{f.DbColumnName.First().ToString().ToLower()}{f.DbColumnName.Substring(1)};");
                sbEntity.AppendLine($"}}");
                sbEntity.AppendLine($"set");
                sbEntity.AppendLine($"{{");
                sbEntity.AppendLine($"this.{f.DbColumnName.First().ToString().ToLower()}{f.DbColumnName.Substring(1)} = value;");
                sbEntity.AppendLine($"}}");
                sbEntity.AppendLine($"}}");
            });
            sbEntity.AppendLine("}");
            sbEntity.AppendLine("}");

            StringBuilder sbCodeDao = new StringBuilder();
            sbCodeDao.AppendLine("using System.Collections.Generic;");
            sbCodeDao.AppendLine($"namespace Com.Movitech.BPM.AbstractModel.From.{namespaceKey}");
            sbCodeDao.AppendLine("{");
            sbCodeDao.AppendLine("/// <summary>");
            sbCodeDao.AppendLine($"/// Description");
            sbCodeDao.AppendLine("/// </summary>");
            sbCodeDao.AppendLine($"public interface I{tableName}DAO");
            sbCodeDao.AppendLine("{");
            sbCodeDao.AppendLine($"I{tableName} RetrieveNew();");
            sbCodeDao.AppendLine($"I{tableName} RetrieveInstance(string id);");
            sbCodeDao.AppendLine($"I{tableName} RetrieveInstanceByProcInstID(long ProcInstID);");
            sbCodeDao.AppendLine($"List<I{tableName}> RetrieveListByWhere(string where);");
            sbCodeDao.AppendLine($"List<I{tableName}> RetrieveByPage(int PageNo, int PageSize, string sortBy, bool isDesc, string where, ref int RowCount);");
            sbCodeDao.AppendLine("}");
            sbCodeDao.AppendLine("}");

            StringBuilder sbEntityDao = new StringBuilder();
            sbEntityDao.AppendLine("using System.Collections.Generic;");
            sbEntityDao.AppendLine($"using Com.Movitech.BPM.AbstractModel.From.{namespaceKey};");
            sbEntityDao.AppendLine("using Com.Movitech.ORM.Common;");
            sbEntityDao.AppendLine("using Com.Movitech.ORM.Framework;");
            sbEntityDao.AppendLine($"namespace Com.Movitech.BPM.Entity.From.{namespaceKey}");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine("/// <summary>");
            sbEntityDao.AppendLine($"/// Description");
            sbEntityDao.AppendLine("/// </summary>");
            sbEntityDao.AppendLine($"public class {tableName}DAO : I{tableName}DAO");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"public I{tableName} RetrieveNew()");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"return new {tableName}();");
            sbEntityDao.AppendLine("}");
            sbEntityDao.AppendLine($"public I{tableName} RetrieveInstance(string id)");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"{tableName} entity = null;");
            sbEntityDao.AppendLine("try");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine("Key key = new Key(true);");
            sbEntityDao.AppendLine("key.Add(\"ID\", id);");
            sbEntityDao.AppendLine($"entity = SqlProvider.ORMBroker.RetrieveInstance<{tableName}>(key);");
            sbEntityDao.AppendLine("}");
            sbEntityDao.AppendLine("catch");
            sbEntityDao.AppendLine("{}");
            sbEntityDao.AppendLine("return entity;");
            sbEntityDao.AppendLine("}");

            sbEntityDao.AppendLine($"public I{tableName} RetrieveInstanceByProcInstID(long ProcInstID)");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"{tableName} entity = null;");
            sbEntityDao.AppendLine("try");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine("Key key = new Key(true);");
            sbEntityDao.AppendLine("key.Add(\"ProcInstID\", ProcInstID);");
            sbEntityDao.AppendLine($"entity = SqlProvider.ORMBroker.RetrieveInstance<{tableName}>(key);");
            sbEntityDao.AppendLine("}");
            sbEntityDao.AppendLine("catch");
            sbEntityDao.AppendLine("{}");
            sbEntityDao.AppendLine("return entity;");
            sbEntityDao.AppendLine("}");

            sbEntityDao.AppendLine($"public List<I{tableName}> RetrieveListByWhere(string where)");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"List<{tableName}> collection = null;");
            sbEntityDao.AppendLine($"List<I{tableName}> list2=new List<I{tableName}>();");
            sbEntityDao.AppendLine("try");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"string sql = string.Format(\"SELECT * FROM {tableName} where 1=1 {{0}}\", where);");
            sbEntityDao.AppendLine($"collection = ObjectFactory.GetCollection<{tableName}>(SqlProvider.ORMBroker.Execute(sql)) as List<{tableName}>;");
            sbEntityDao.AppendLine($"list2 = new List<I{tableName}>(collection.ToArray());");
            sbEntityDao.AppendLine("}");
            sbEntityDao.AppendLine("catch");
            sbEntityDao.AppendLine("{}");
            sbEntityDao.AppendLine("return list2;");
            sbEntityDao.AppendLine("}");

            sbEntityDao.AppendLine($"public List<I{tableName}> RetrieveByPage(int PageNo, int PageSize, string sortBy, bool isDesc, string where, ref int RowCount)");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"List<{tableName}> collection = null;");
            sbEntityDao.AppendLine($"List<I{tableName}> list2=new List<I{tableName}>();");
            sbEntityDao.AppendLine("try");
            sbEntityDao.AppendLine("{");
            sbEntityDao.AppendLine($"string str = string.Format(\"Select * From {tableName} {{0}}\", string.IsNullOrEmpty(where) ? \"\" : (\" where \" + where));");
            sbEntityDao.AppendLine($"collection = ObjectFactory.GetCollection<{tableName}>(OrmSession.GetSqlResult(string.Format(\"({{0}}) a\", str), \"ID\", sortBy, isDesc, PageNo, PageSize, ref RowCount)) as List<{tableName}>;");
            sbEntityDao.AppendLine($"list2 = new List<I{tableName}>(collection.ToArray());");
            sbEntityDao.AppendLine("}");
            sbEntityDao.AppendLine("catch");
            sbEntityDao.AppendLine("{}");
            sbEntityDao.AppendLine("return list2;");
            sbEntityDao.AppendLine("}");

            sbEntityDao.AppendLine("}");
            sbEntityDao.AppendLine("}");

            var a = sbCode.ToString();
            var b = sbEntity.ToString();
            var c = sbCodeDao.ToString();
            var d = sbEntityDao.ToString();
            return (a, b, c, d);
        }

        
        
        
        
        
        
        public PageResult<PageModelingVersionDto> GetPageModelingVersions(Guid id, PageQueryDtoBase dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<PageVersion>()
                .Where(w => w.PageModelingId == id)
                .OrderBy(w => w.Version, OrderByType.Desc)
                .Select(w => new PageModelingVersionDto
                {
                    Id = w.Id,
                    Version = w.Version,
                    SourceVersion = w.SourceVersion,
                    Status = w.Status,
                    PageModelingId = w.PageModelingId
                });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<PageModelingVersionDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        
        public List<PageModelingDto> GetPageModelingVersions(List<string> id)
        {
            var pm = _dbContext.Modeling.Queryable<PageModeling>()
                .Where(pm => id.Contains(pm.Id.ToString()))
                .Select(pm => new PageModelingDto { Id = pm.Id, Name = pm.Name })
                .ToList();
            var pv = _dbContext.Modeling.Queryable<PageVersion>()
                .Where(pv => id.Contains(pv.PageModelingId.ToString()))
                .ToList();
            pm.ForEach(f =>
            {
                var versions = pv.FindAll(fa => fa.PageModelingId == f.Id).OrderByDescending(fa => fa.Version).Select(fa => new PageModelingVersionDto { Version = fa.Version }).Take(10).ToList();
                f.Versions = versions != null && versions.Count > 0 ? versions : new List<PageModelingVersionDto> { new PageModelingVersionDto { Version = 1 } };
                f.Version = versions != null && versions.Count > 0 ? versions.First().Version : 1;
            });
            return pm;
        }

        
        
        
        
        public void CreateVersion(PageModelingVersionDto dto)
        {
            var pageVersion = _dbContext.Modeling.Queryable<PageVersion>().InSingle(dto.Id);
            if (pageVersion == null)
            {
                throw new StatusNotFoundException("页面版本信息不存在");
            }

            var maxVersion = _dbContext.Modeling.Queryable<PageVersion>()
                       .Where(w => w.PageModelingId == pageVersion.PageModelingId)
                       .OrderBy(w => w.Version, OrderByType.Desc)
                       .First();

            DateTime now = DateTime.Now;
            var currentUserId = UserConstants.CurrentUser.Value.Account;

            
            var version = new PageVersion()
            {
                Id = Guid.NewGuid(),
                PageModelingId = pageVersion.PageModelingId,
                PageDesginJson = pageVersion.PageDesginJson,
                Status = 0,
                Version = maxVersion != null ? maxVersion.Version + 1 : 1,
                SourceVersion = pageVersion.Version,
                CreateDate = now,
                UpdateDate = now,
                CreateUser = currentUserId,
                UpdateUser = currentUserId,
                DataSource = pageVersion.DataSource
            };
            _dbContext.Modeling.Insertable(version).ExecuteCommand();
        }

        
        
        
        
        public void PublicVersion(PageModelingVersionDto dto)
        {
            var pageVersion = _dbContext.Modeling.Queryable<PageVersion>().InSingle(dto.Id);
            if (pageVersion == null)
            {
                throw new StatusNotFoundException("页面版本信息不存在");
            }

            DateTime now = DateTime.Now;
            var currentUserId = UserConstants.CurrentUser.Value.Account;
            pageVersion.Status = 1;
            pageVersion.UpdateDate = now;
            pageVersion.UpdateUser = currentUserId;
            _dbContext.Modeling.Updateable(pageVersion).ExecuteCommand();
        }

        
        
        
        
        public void UseVersion(PageModelingVersionDto dto)
        {
            var pageVersion = _dbContext.Modeling.Queryable<PageVersion>().InSingle(dto.Id);
            if (pageVersion == null)
            {
                throw new StatusNotFoundException("页面版本信息不存在");
            }

            if (pageVersion.Status == 0)
            {
                throw new StatusNotFoundException("页面版本未发布");
            }

            var pageModeling = _dbContext.Modeling.Queryable<PageModeling>().InSingle(pageVersion.PageModelingId);
            if (pageModeling == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            DateTime now = DateTime.Now;
            var currentUserId = UserConstants.CurrentUser.Value.Account;
            pageModeling.PageDesginJson = pageVersion.PageDesginJson;
            pageModeling.Version = pageVersion.Version;
            pageModeling.UpdateDate = now;
            pageModeling.UpdateUser = currentUserId;
            pageModeling.DataSource = pageVersion.DataSource;
            _dbContext.Modeling.Updateable(pageModeling).ExecuteCommand();
        }

        
        
        
        
        
        public Guid UpdatePageModeling(PageModelingDto dto)
        {
            var item = _dbContext.Modeling.Queryable<PageModeling>().InSingle(dto.Id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.notfound"));
            }

            var hasData = _dbContext.Modeling.Queryable<PageModeling>()
               .Where(a => a.Name == dto.Name && a.Id != dto.Id).Any();
            if (hasData)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.pageModeling.nameExist"));
            }

            PageVersion pageVersion = null;
            if (dto.VersionId.HasValue && dto.VersionId != Guid.Empty)
            {
                pageVersion = _dbContext.Modeling.Queryable<PageVersion>().InSingle(dto.VersionId.Value);
                if (pageVersion == null)
                {
                    throw new StatusNotFoundException("页面版本信息不存在");
                }
            }
            else if (item.Version.HasValue)
            {
                pageVersion = _dbContext.Modeling.Queryable<PageVersion>().Where(w => w.PageModelingId == item.Id && w.Version == item.Version.Value).First();
            }

            if (pageVersion != null)
            {
                pageVersion.PageDesginJson = dto.PageDesginJson;
                pageVersion.DataSource = dto.DataSource;
                pageVersion.UpdateDate = DateTime.Now;
                pageVersion.UpdateUser = UserConstants.CurrentUser.Value.Account;
                _dbContext.Modeling.Updateable(pageVersion).ExecuteCommand();
            }

            if (pageVersion == null || pageVersion.Version == item.Version)
            {
                item.UpdateDate = DateTime.Now;
                item.UpdateUser = UserConstants.CurrentUser.Value.Account;
                item.PageDesginJson = dto.PageDesginJson;
                item.DataSource = dto.DataSource;
                item.DataSourceType = dto.DataSourceType;
                item.Name = dto.Name;
                item.ApplicationId = dto.ApplicationId;
                item.ApplicationName = dto.ApplicationName;
                item.ModuleId = dto.ModuleId;
                item.ModuleName = dto.ModuleName;
                _dbContext.Modeling.Updateable(item).ExecuteCommand();
            }

            return item.Id;
        }

        
        
        
        
        
        public PageModelingObjectResultDto GetPageModelingObjects(List<PageModelingDto> dto)
        {
            var ywdx_id = new List<Guid>();
            var zhdx_id = new List<Guid>();
            var st_id = new List<Guid>();
            var ym_id = new List<PageModeling>();

            
            dto.ForEach(f =>
            {
                
                var pm = _dbContext.Modeling.Queryable<PageModeling>()
                .LeftJoin<PageVersion>((pm, pv) => pm.Id == pv.PageModelingId)
                .Where((pm, pv) => pm.Id == f.Id && SqlFunc.IsNull(pv.Version, 1) == f.Version)
                .Select((pm, pv) => new
                {
                    id = pm.Id,
                    name = pm.Name,
                    version = f.Version,
                    desginJson = SqlFunc.IsNull(pv.PageDesginJson, pm.PageDesginJson)
                })
                .First();
                if (pm != null)
                {
                    TQDesinJson(pm.desginJson, ywdx_id, zhdx_id, st_id, ym_id);
                    var idx = ym_id.FindIndex(y => y.Id == pm.id);
                    if (idx > -1)
                    {
                        ym_id.RemoveAt(idx);
                    }

                    ym_id.Add(new PageModeling { Id = pm.id, Name = pm.name, Version = pm.version });
                }
            });

            
            if (zhdx_id.Count > 0)
            {
                var compositeObjects = _dbContext.Modeling.Queryable<CompositeObject>()
                    .Where(w => zhdx_id.Contains(w.Id))
                    .ToList();
                compositeObjects.ForEach(f =>
                {
                    AddObject(f.BusinessObjectId.ToString(), "1", ywdx_id, zhdx_id, st_id);
                });

                var compositeObjectRelations = _dbContext.Modeling.Queryable<CompositeObjectRelation>()
                    .Where(w => zhdx_id.Contains(w.CompositeObjectId))
                    .ToList();
                compositeObjectRelations.ForEach(f =>
                {
                    AddObject(f.BusinessObjectId.ToString(), f.ObjectType == "view" ? "3" : "1", ywdx_id, zhdx_id, st_id);
                });
            }

            
            if (st_id.Count > 0)
            {
                var viewRelations = _dbContext.Modeling.Queryable<ViewRelations>()
                    .Where(w => st_id.Contains(w.ViewId))
                    .ToList();
                viewRelations.ForEach(f =>
                {
                    AddObject(f.ParentBusinessObjectId.ToString(), "1", ywdx_id, zhdx_id, st_id);
                });
            }

            var result = new PageModelingObjectResultDto();

            if (ywdx_id.Count > 0)
            {
                result.BusinessObject = _dbContext.Modeling.Queryable<BusinessObject>()
                    .Where(w => ywdx_id.Contains(w.Id))
                    .ToList();
            }

            if (zhdx_id.Count > 0)
            {
                result.CompositeObject = _dbContext.Modeling.Queryable<CompositeObject>()
                    .Where(w => zhdx_id.Contains(w.Id))
                    .ToList();
            }

            if (st_id.Count > 0)
            {
                result.View = _dbContext.Modeling.Queryable<View>()
                    .Where(w => st_id.Contains(w.Id))
                    .ToList();
            }

            if (ym_id.Count > 0)
            {
                result.PageModeling = ym_id;
            }

            return result;
        }

        
        
        
        
        
        public JObject Sync(PageModelingSyncDto dto)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(dto.TargetDataBaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var resultObject = new JObject
            {
                { "code", "200" },
                { "msg", "success" }
            };

            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            var userId = UserConstants.CurrentUser.Value.Account;
            switch (dto.Type)
            {
                case "application":
                    if (dto.Applications.Any(a => a.Exec == "create"))
                    {
                        var exec_ae_insert = new List<ApplicationEntity>();
                        var exec_m_insert = new List<ModuleEntity>();
                        dto.Applications.FindAll(w => w.Exec == "create").ForEach(f =>
                        {
                            var app = client.Queryable<ApplicationEntity>().Where(w => !w.IsDelete && w.Name == f.Name).First();
                            if (app == null)
                            {
                                exec_ae_insert.Add(new ApplicationEntity()
                                {
                                    Id = f.Id,
                                    Name = f.Name,
                                    Describe = f.Describe,
                                    CreateDate = DateTime.Now,
                                    CreateUser = userId,
                                    IsDelete = false
                                });
                            }
                            else
                            {
                                f.Id = app.Id;
                            }

                            var modules = f.Modules.FindAll(f => f.Exec == "create").Select(s => new ModuleEntity { Id = s.Id, Name = s.Name, Describe = s.Describe, CreateDate = DateTime.Now, CreateUser = userId, ApplicationId = f.Id, ApplicationName = f.Name, IsDelete = false }).ToList();
                            exec_m_insert.AddRange(modules);
                        });
                        try
                        {
                            client.BeginTran();
                            if (exec_ae_insert.Count > 0)
                            {
                                client.Insertable(exec_ae_insert).ExecuteCommand();
                            }

                            if (exec_m_insert.Count > 0)
                            {
                                client.Insertable(exec_m_insert).ExecuteCommand();
                            }

                            client.CommitTran();
                        }
                        catch (Exception ex)
                        {
                            client.RollbackTran();
                            resultObject = new JObject
                            {
                                { "code", "500" },
                                { "msg", ex.Message }
                            };
                        }
                    }

                    break;
                case "object":
                    if (dto.Objects.Any(a => a.Exec == "create"))
                    {
                        
                        var target_bases = client.Queryable<DataBase>().ToList();

                        
                        var target_applications = client.Queryable<ApplicationEntity>().Where(ae => !ae.IsDelete).ToList();

                        
                        var target_businessObjects = client.Queryable<BusinessObject>().Where(bo => !bo.IsDelete.Value).ToList();

                        
                        var target_businessObjectColumns = client.Queryable<BusinessObjectColumn>().ToList();

                        var exec_bo_insert = new List<BusinessObject>();
                        var exec_bo_update = new List<BusinessObject>();
                        var exec_boc_insert = new List<BusinessObjectColumn>();
                        var exec_boc_update = new List<BusinessObjectColumn>();
                        var exec_v_insert = new List<View>();
                        var exec_v_update = new List<View>();
                        var exec_vc_insert = new List<ViewColumns>();
                        var exec_br_insert = new List<ViewRelations>();
                        var exec_vf_insert = new List<ViewFilters>();
                        var exec_vo_insert = new List<ViewOrders>();
                        var exec_co_insert = new List<CompositeObject>();
                        var exec_co_update = new List<CompositeObject>();
                        var exec_cor_insert = new List<CompositeObjectRelation>();

                        
                        var sync_businessObjects = dto.Objects.FindAll(o => o.Exec == "create" && o.Type == "businessObject");
                        if (sync_businessObjects.Count > 0)
                        {
                            
                            var sync_businessObject_ids = sync_businessObjects.Select(sbo => sbo.Id).ToList();

                            
                            var sync_businessObject_info = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((bo, db) => bo.DataBaseId == db.Id)
                                .Where(bo => sync_businessObject_ids.Contains(bo.Id))
                                .Select((bo, db) => new { bo, db })
                                .ToList();

                            
                            var sync_businessObjectColumn_info = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(boc => sync_businessObject_ids.Contains(boc.BusinessObjectId)).ToList();

                            sync_businessObjects.ForEach(sbo =>
                            {
                                
                                var bc_businessObject_info = sync_businessObject_info.Find(sboi => sboi.bo.Id == sbo.Id);

                                
                                var bc_base = target_bases.Find(tb => tb.OnlyCode == bc_businessObject_info.db.OnlyCode);

                                
                                if (bc_base != null)
                                {
                                    
                                    var target_businessObject = target_businessObjects.Where(bo => bo.Name == bc_businessObject_info.bo.Name && bo.DataBaseId == bc_base.Id).FirstOrDefault();

                                    
                                    if (target_businessObject != null && target_businessObject.IsCustom)
                                    {
                                        target_businessObject.Description = bc_businessObject_info.bo.Description;
                                        target_businessObject.IsDelete = bc_businessObject_info.bo.IsDelete;
                                        exec_bo_update.Add(target_businessObject);

                                        
                                        var target_businessObjectColumn = target_businessObjectColumns.Where(boc => boc.BusinessObjectId == target_businessObject.Id).OrderBy(boc => boc.Order).ToList();

                                        
                                        var bc_businessObjectColumn_info = sync_businessObjectColumn_info.FindAll(sboci => sboci.BusinessObjectId == bc_businessObject_info.bo.Id).OrderBy(sboci => sboci.Order).ToList();
                                        bc_businessObjectColumn_info.ForEach(bboci =>
                                        {
                                            var bc_target_businessObjectColumn = target_businessObjectColumn.Find(tboc => tboc.Name == bboci.Name);
                                            if (bc_target_businessObjectColumn != null)
                                            {
                                                bc_target_businessObjectColumn.Description = bboci.Description;
                                                bc_target_businessObjectColumn.IsEnable = bboci.IsEnable;
                                                bc_target_businessObjectColumn.Length = bboci.Length;
                                                bc_target_businessObjectColumn.Decimal = bboci.Decimal;
                                                bc_target_businessObjectColumn.DefaultValue = bboci.DefaultValue;
                                                bc_target_businessObjectColumn.Order = bboci.Order;
                                                exec_boc_update.Add(bc_target_businessObjectColumn);
                                            }
                                            else
                                            {
                                                exec_boc_insert.Add(new BusinessObjectColumn
                                                {
                                                    Id = bboci.Id,
                                                    Name = bboci.Name,
                                                    Description = bboci.Description,
                                                    BusinessObjectId = target_businessObject.Id,
                                                    CreateDate = DateTime.Now,
                                                    Decimal = bboci.Decimal,
                                                    DefaultValue = bboci.DefaultValue,
                                                    DisplayType = bboci.DisplayType,
                                                    IsEnable = bboci.IsEnable,
                                                    IsIdentity = bboci.IsIdentity,
                                                    IsNullable = bboci.IsNullable,
                                                    IsPrimaryKey = bboci.IsPrimaryKey,
                                                    IsSystemColumn = bboci.IsSystemColumn,
                                                    Length = bboci.Length,
                                                    Order = bboci.Order,
                                                    Type = bboci.Type,
                                                });
                                            }
                                        });
                                    }

                                    
                                    else if (target_businessObject == null)
                                    {
                                        
                                        var bc_application = target_applications.FindAll(ta => bc_businessObject_info.bo.ApplicationName.Split(";").Contains(ta.Name));
                                        var insert = new BusinessObject
                                        {
                                            Id = bc_businessObject_info.bo.Id,
                                            Name = bc_businessObject_info.bo.Name,
                                            Description = bc_businessObject_info.bo.Description,
                                            IsCustom = bc_businessObject_info.bo.IsCustom,
                                            IsDelete = bc_businessObject_info.bo.IsDelete,
                                            IsLogicalDelete = bc_businessObject_info.bo.IsLogicalDelete,
                                            IsTree = bc_businessObject_info.bo.IsTree,
                                            State = bc_businessObject_info.bo.State,
                                            CreateDate = DateTime.Now,
                                            DataBaseId = bc_base.Id
                                        };
                                        if (bc_businessObject_info.bo.IsCustom)
                                        {
                                            insert.ApplicationId = string.Join(";", bc_application.Select(s => s.Id));
                                            insert.ApplicationName = string.Join(";", bc_application.Select(s => s.Name));
                                        }

                                        exec_bo_insert.Add(insert);

                                        
                                        var bc_businessObjectColumn_info = sync_businessObjectColumn_info.FindAll(sboci => sboci.BusinessObjectId == bc_businessObject_info.bo.Id).OrderBy(sboci => sboci.Order).ToList();
                                        bc_businessObjectColumn_info.ForEach(bboci =>
                                        {
                                            exec_boc_insert.Add(new BusinessObjectColumn
                                            {
                                                Id = bboci.Id,
                                                Name = bboci.Name,
                                                Description = bboci.Description,
                                                BusinessObjectId = insert.Id,
                                                CreateDate = DateTime.Now,
                                                Decimal = bboci.Decimal,
                                                DefaultValue = bboci.DefaultValue,
                                                DisplayType = bboci.DisplayType,
                                                IsEnable = bboci.IsEnable,
                                                IsIdentity = bboci.IsIdentity,
                                                IsNullable = bboci.IsNullable,
                                                IsPrimaryKey = bboci.IsPrimaryKey,
                                                IsSystemColumn = bboci.IsSystemColumn,
                                                Length = bboci.Length,
                                                Order = bboci.Order,
                                                Type = bboci.Type
                                            });
                                        });
                                    }
                                }
                            });

                            if (exec_bo_insert.Count > 0)
                            {
                                client.Insertable(exec_bo_insert).ExecuteCommand();
                            }

                            if (exec_bo_update.Count > 0)
                            {
                                client.Updateable(exec_bo_update).ExecuteCommand();
                            }

                            if (exec_boc_insert.Count > 0)
                            {
                                client.Insertable(exec_boc_insert).ExecuteCommand();
                            }

                            if (exec_boc_update.Count > 0)
                            {
                                client.Updateable(exec_boc_update).ExecuteCommand();
                            }
                        }

                        
                        var sync_views = dto.Objects.FindAll(w => w.Exec == "create" && w.Type == "view");
                        if (sync_views.Count > 0)
                        {
                            
                            var sync_view_ids = sync_views.Select(sv => sv.Id).ToList();

                            
                            var sync_view_info = _dbContext.Modeling.Queryable<View, DataBase>((v, db) => v.DatabaseId == db.Id)
                                .LeftJoin<ApplicationEntity>((v, db, ae) => !SqlFunc.IsNullOrEmpty(v.AppId) && v.AppId == ae.Id)
                                .Where(v => sync_view_ids.Contains(v.Id))
                                .Select((v, db, ae) => new { v, db, ae })
                                .ToList();

                            
                            var sync_viewColumn_info = _dbContext.Modeling.Queryable<ViewColumns>().Where(vc => sync_view_ids.Contains(vc.ViewId)).ToList();

                            
                            var sync_viewRelation_info = _dbContext.Modeling.Queryable<ViewRelations>()
                                .LeftJoin<BusinessObject>((vr, bo1) => vr.BusinessObjectId == bo1.Id)
                                .LeftJoin<BusinessObject>((vr, bo1, bo2) => vr.ParentBusinessObjectId == bo2.Id)
                                .LeftJoin<BusinessObjectColumn>((vr, bo1, bo2, boc1) => vr.BusinessObjectColumnId == boc1.Id)
                                .LeftJoin<BusinessObjectColumn>((vr, bo1, bo2, boc1, boc2) => vr.ParentBusinessObjectColumnId == boc2.Id)
                                .Where(vr => sync_view_ids.Contains(vr.ViewId))
                                .Select((vr, bo1, bo2, boc1, boc2) => new { vr, bo1, bo2, boc1, boc2 })
                                .ToList();

                            
                            var sync_viewFilter_info = _dbContext.Modeling.Queryable<ViewFilters>()
                                .LeftJoin<BusinessObject>((vf, bo) => vf.BusinessObjectId == bo.Id)
                                .LeftJoin<BusinessObjectColumn>((vf, bo, boc) => vf.BusinessObjectColumnId == boc.Id)
                                .Where(vf => sync_view_ids.Contains(vf.ViewId))
                                .Select((vf, bo, boc) => new { vf, bo, boc })
                                .ToList();

                            
                            var sync_viewOrder_info = _dbContext.Modeling.Queryable<ViewOrders>()
                                .LeftJoin<BusinessObject>((vo, bo) => vo.BusinessObjectId == bo.Id)
                                .LeftJoin<BusinessObjectColumn>((vo, bo, boc) => vo.BusinessObjectColumnId == boc.Id)
                                .Where(vo => sync_view_ids.Contains(vo.ViewId))
                                .Select((vo, bo, boc) => new { vo, bo, boc })
                                .ToList();

                            sync_views.ForEach(sv =>
                            {
                                
                                var bc_view_info = sync_view_info.Find(svi => svi.v.Id == sv.Id);

                                
                                var bc_base = target_bases.Find(tb => tb.OnlyCode == bc_view_info.db.OnlyCode);

                                
                                if (bc_base != null)
                                {
                                    var id = Guid.Empty;

                                    
                                    var target_view = client.Queryable<View>().Where(v => v.Name == bc_view_info.v.Name && v.DatabaseId == bc_base.Id).First();

                                    
                                    if (target_view != null && target_view.IsCustom)
                                    {
                                        id = target_view.Id;
                                        target_view.Description = bc_view_info.v.Description;
                                        exec_v_update.Add(target_view);

                                        
                                        client.Deleteable<ViewColumns>().Where(vc => vc.ViewId == target_view.Id).ExecuteCommand();
                                        client.Deleteable<ViewFilters>().Where(vf => vf.ViewId == target_view.Id).ExecuteCommand();
                                        client.Deleteable<ViewOrders>().Where(vo => vo.ViewId == target_view.Id).ExecuteCommand();
                                    }

                                    
                                    else if (target_view == null)
                                    {
                                        
                                        var bc_application = target_applications.Find(ta => bc_view_info.ae.Name == ta.Name);
                                        var insert = new View
                                        {
                                            Id = bc_view_info.v.Id,
                                            Name = bc_view_info.v.Name,
                                            Description = bc_view_info.v.Description,
                                            IsCustom = bc_view_info.v.IsCustom,
                                            GroupFilter = bc_view_info.v.GroupFilter,
                                            State = 0,
                                            CreateDate = DateTime.Now,
                                            DatabaseId = bc_base.Id
                                        };
                                        if (bc_view_info.v.IsCustom && bc_application != null)
                                        {
                                            insert.AppId = bc_application.Id;
                                        }

                                        exec_v_insert.Add(insert);

                                        id = insert.Id;

                                        
                                        var bc_viewRelation_info = sync_viewRelation_info.FindAll(svri => svri.vr.ViewId == bc_view_info.v.Id).ToList();
                                        bc_viewRelation_info.ForEach(bvrr =>
                                        {
                                            var insert_relation = new ViewRelations
                                            {
                                                Id = bvrr.vr.Id,
                                                ViewId = insert.Id,
                                                ObjectType = bvrr.vr.ObjectType,
                                                JoinType = bvrr.vr.JoinType,
                                                OrderNumber = bvrr.vr.OrderNumber,
                                                BusinessObjectId = bvrr.vr.BusinessObjectId,
                                                BusinessObjectColumnId = bvrr.vr.BusinessObjectColumnId,
                                                ParentBusinessObjectId = bvrr.vr.ParentBusinessObjectId,
                                                ParentBusinessObjectColumnId = bvrr.vr.ParentBusinessObjectColumnId
                                            };

                                            if (insert_relation.BusinessObjectId != Guid.Empty)
                                            {
                                                var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == bvrr.bo1.Name && f.DataBaseId == bc_base.Id);
                                                insert_relation.BusinessObjectId = v_businessObject == null ? Guid.Empty : v_businessObject.Id;
                                            }

                                            if (insert_relation.BusinessObjectColumnId != Guid.Empty)
                                            {
                                                var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == bvrr.boc1.Name && f.BusinessObjectId == insert_relation.BusinessObjectId);
                                                insert_relation.BusinessObjectColumnId = v_businessObjectColumn == null ? Guid.Empty : v_businessObjectColumn.Id;
                                            }

                                            if (insert_relation.ParentBusinessObjectId != Guid.Empty)
                                            {
                                                var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == bvrr.bo2.Name && f.DataBaseId == bc_base.Id);
                                                if (v_businessObject != null)
                                                {
                                                    insert_relation.ParentBusinessObjectId = v_businessObject.Id;
                                                }
                                            }

                                            if (insert_relation.ParentBusinessObjectColumnId != Guid.Empty)
                                            {
                                                var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == bvrr.boc2.Name && f.BusinessObjectId == insert_relation.ParentBusinessObjectId);
                                                if (v_businessObjectColumn != null)
                                                {
                                                    insert_relation.ParentBusinessObjectColumnId = v_businessObjectColumn.Id;
                                                }
                                            }

                                            exec_br_insert.Add(insert_relation);
                                        });
                                    }

                                    
                                    var bc_viewColumn_info = sync_viewColumn_info.FindAll(svci => svci.ViewId == bc_view_info.v.Id).ToList();
                                    bc_viewColumn_info.ForEach(bvci =>
                                    {
                                        var insert_column = new ViewColumns
                                        {
                                            Id = bvci.Id,
                                            ViewId = id,
                                            BusinessObjectName = bvci.BusinessObjectName,
                                            BusinessObjectColumnName = bvci.BusinessObjectColumnName,
                                            BusinessObjectColumnAlias = bvci.BusinessObjectColumnAlias,
                                            BusinessObjectColumnDescription = bvci.BusinessObjectColumnDescription,
                                            FunctionBusinessObjectType = bvci.FunctionBusinessObjectType,
                                            FunctionBusinessObjectName = bvci.FunctionBusinessObjectName,
                                            FunctionBusinessObjectColumnName = bvci.FunctionBusinessObjectColumnName,
                                            FunctionId = bvci.FunctionId,
                                            ForeignKeyObjectName = bvci.ForeignKeyObjectName,
                                            ForeignKeyObjectColumnName = bvci.ForeignKeyObjectColumnName,
                                            ColumnType = bvci.ColumnType,
                                            OrderNumber = bvci.OrderNumber,
                                            DisplayType = bvci.DisplayType,
                                            IsPrimaryKey = bvci.IsPrimaryKey
                                        };

                                        if (!string.IsNullOrEmpty(insert_column.BusinessObjectName) && bvci.BusinessObjectId.HasValue && bvci.BusinessObjectId.Value != Guid.Empty)
                                        {
                                            var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == insert_column.BusinessObjectName && f.DataBaseId == bc_base.Id);
                                            if (v_businessObject != null)
                                            {
                                                insert_column.BusinessObjectId = v_businessObject.Id;
                                            }
                                        }

                                        if (!string.IsNullOrEmpty(insert_column.BusinessObjectColumnName) && bvci.BusinessObjectColumnId.HasValue && bvci.BusinessObjectColumnId.Value != Guid.Empty && insert_column.BusinessObjectId.HasValue && insert_column.BusinessObjectId.Value != Guid.Empty)
                                        {
                                            var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == insert_column.BusinessObjectColumnName && f.BusinessObjectId == insert_column.BusinessObjectId);
                                            if (v_businessObjectColumn != null)
                                            {
                                                insert_column.BusinessObjectColumnId = v_businessObjectColumn.Id;
                                            }
                                        }

                                        if (!string.IsNullOrEmpty(insert_column.FunctionBusinessObjectName) && bvci.FunctionBusinessObjectId.HasValue && bvci.FunctionBusinessObjectId.Value != Guid.Empty)
                                        {
                                            var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == insert_column.FunctionBusinessObjectName && f.DataBaseId == bc_base.Id);
                                            if (v_businessObject != null)
                                            {
                                                insert_column.FunctionBusinessObjectId = v_businessObject.Id;
                                            }
                                        }

                                        if (!string.IsNullOrEmpty(insert_column.FunctionBusinessObjectColumnName) && bvci.FunctionBusinessObjectColumnId.HasValue && bvci.FunctionBusinessObjectColumnId.Value != Guid.Empty && insert_column.FunctionBusinessObjectId.HasValue && insert_column.FunctionBusinessObjectId.Value != Guid.Empty)
                                        {
                                            var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == insert_column.FunctionBusinessObjectColumnName && f.BusinessObjectId == insert_column.FunctionBusinessObjectId);
                                            if (v_businessObjectColumn != null)
                                            {
                                                insert_column.FunctionBusinessObjectColumnId = v_businessObjectColumn.Id;
                                            }
                                        }

                                        if (!string.IsNullOrEmpty(insert_column.ForeignKeyObjectName) && bvci.ForeignKeyObjectId.HasValue && bvci.ForeignKeyObjectId.Value != Guid.Empty)
                                        {
                                            var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == insert_column.ForeignKeyObjectName && f.DataBaseId == bc_base.Id);
                                            if (v_businessObject != null)
                                            {
                                                insert_column.ForeignKeyObjectId = v_businessObject.Id;
                                            }
                                        }

                                        if (!string.IsNullOrEmpty(insert_column.ForeignKeyObjectColumnName) && bvci.ForeignKeyObjectColumnId.HasValue && bvci.ForeignKeyObjectColumnId.Value != Guid.Empty && insert_column.ForeignKeyObjectId.HasValue && insert_column.ForeignKeyObjectId.Value != Guid.Empty)
                                        {
                                            var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == insert_column.ForeignKeyObjectColumnName && f.BusinessObjectId == insert_column.ForeignKeyObjectId);
                                            if (v_businessObjectColumn != null)
                                            {
                                                insert_column.ForeignKeyObjectColumnId = v_businessObjectColumn.Id;
                                            }
                                        }

                                        exec_vc_insert.Add(insert_column);
                                    });

                                    
                                    var bc_viewFilter_info = sync_viewFilter_info.FindAll(svfi => svfi.vf.ViewId == bc_view_info.v.Id).ToList();
                                    bc_viewFilter_info.ForEach(bvfi =>
                                    {
                                        var insert_filter = new ViewFilters
                                        {
                                            Id = bvfi.vf.Id,
                                            ViewId = id,
                                            FilterName = bvfi.vf.FilterName,
                                            Judge = bvfi.vf.Judge,
                                            FilterSource = bvfi.vf.FilterSource,
                                            FilterValue = bvfi.vf.FilterValue,
                                            BusinessObjectId = bvfi.vf.BusinessObjectId,
                                            BusinessObjectColumnId = bvfi.vf.BusinessObjectColumnId,
                                            OrderNumber = bvfi.vf.OrderNumber
                                        };

                                        if (insert_filter.BusinessObjectId != Guid.Empty)
                                        {
                                            var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == bvfi.bo.Name && f.DataBaseId == bc_base.Id);
                                            insert_filter.BusinessObjectId = v_businessObject == null ? Guid.Empty : v_businessObject.Id;
                                        }

                                        if (insert_filter.BusinessObjectColumnId != Guid.Empty)
                                        {
                                            var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == bvfi.boc.Name && f.BusinessObjectId == insert_filter.BusinessObjectId);
                                            insert_filter.BusinessObjectColumnId = v_businessObjectColumn == null ? Guid.Empty : v_businessObjectColumn.Id;
                                        }

                                        exec_vf_insert.Add(insert_filter);
                                    });

                                    
                                    var bc_viewOrder_info = sync_viewOrder_info.FindAll(svoi => svoi.vo.ViewId == bc_view_info.v.Id).ToList();
                                    bc_viewOrder_info.ForEach(bvoi =>
                                    {
                                        var insert_order = new ViewOrders
                                        {
                                            Id = bvoi.vo.Id,
                                            ViewId = id,
                                            BusinessObjectId = bvoi.vo.BusinessObjectId,
                                            BusinessObjectColumnId = bvoi.vo.BusinessObjectColumnId,
                                            OrderType = bvoi.vo.OrderType,
                                            OrderNumber = bvoi.vo.OrderNumber
                                        };

                                        if (insert_order.BusinessObjectId != Guid.Empty)
                                        {
                                            var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == bvoi.bo.Name && f.DataBaseId == bc_base.Id);
                                            insert_order.BusinessObjectId = v_businessObject == null ? Guid.Empty : v_businessObject.Id;
                                        }

                                        if (insert_order.BusinessObjectColumnId != Guid.Empty)
                                        {
                                            var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == bvoi.boc.Name && f.BusinessObjectId == insert_order.BusinessObjectId);
                                            insert_order.BusinessObjectColumnId = v_businessObjectColumn == null ? Guid.Empty : v_businessObjectColumn.Id;
                                        }

                                        exec_vo_insert.Add(insert_order);
                                    });
                                }
                            });

                            if (exec_v_insert.Count > 0)
                            {
                                client.Insertable(exec_v_insert).ExecuteCommand();
                            }

                            if (exec_v_update.Count > 0)
                            {
                                client.Updateable(exec_v_update).ExecuteCommand();
                            }

                            if (exec_vc_insert.Count > 0)
                            {
                                client.Insertable(exec_vc_insert).ExecuteCommand();
                            }

                            if (exec_br_insert.Count > 0)
                            {
                                client.Insertable(exec_br_insert).ExecuteCommand();
                            }

                            if (exec_vf_insert.Count > 0)
                            {
                                client.Insertable(exec_vf_insert).ExecuteCommand();
                            }

                            if (exec_vo_insert.Count > 0)
                            {
                                client.Insertable(exec_vo_insert).ExecuteCommand();
                            }
                        }

                        
                        var sync_compositeObject = dto.Objects.FindAll(w => w.Exec == "create" && w.Type == "compositeObject");
                        if (sync_compositeObject.Count > 0)
                        {
                            
                            var sync_compositeObject_ids = sync_compositeObject.Select(sco => sco.Id).ToList();

                            
                            var sync_compositeObject_info = _dbContext.Modeling.Queryable<CompositeObject, DataBase>((co, db) => co.DataBaseId == db.Id)
                                .LeftJoin<BusinessObject>((co, db, bo) => co.BusinessObjectId == bo.Id)
                                .Where(co => sync_compositeObject_ids.Contains(co.Id))
                                .Select((co, db, bo) => new { co, db, bo })
                                .ToList();

                            
                            var sync_compositeObjectRelation_info = _dbContext.Modeling.Queryable<CompositeObjectRelation>()
                                .LeftJoin<BusinessObject>((cor, bo) => cor.BusinessObjectId == bo.Id)
                                .LeftJoin<BusinessObjectColumn>((cor, bo, boc1) => cor.BusinessObjectColumnId == boc1.Id)
                                .LeftJoin<BusinessObjectColumn>((cor, bo, boc1, boc2) => cor.ParentBusinessObjectColumnId == boc2.Id)
                                .Where(cor => sync_compositeObject_ids.Contains(cor.CompositeObjectId))
                                .Select((cor, bo, boc1, boc2) => new { cor, bo, boc1, boc2 })
                                .ToList();

                            sync_compositeObject.ForEach(sco =>
                            {
                                
                                var bc_compositeObject_info = sync_compositeObject_info.Find(scoi => scoi.co.Id == sco.Id);

                                
                                var bc_base = target_bases.Find(tb => tb.OnlyCode == bc_compositeObject_info.db.OnlyCode);

                                
                                if (bc_base != null)
                                {
                                    var id = Guid.Empty;

                                    
                                    var target_compositeObject = client.Queryable<CompositeObject>().Where(co => co.Name == bc_compositeObject_info.co.Name && co.DataBaseId == bc_base.Id).First();

                                    
                                    if (target_compositeObject != null)
                                    {
                                        id = target_compositeObject.Id;
                                        target_compositeObject.Name = bc_compositeObject_info.co.Name;
                                        target_compositeObject.DataBaseId = bc_base.Id;
                                        target_compositeObject.IsDelete = bc_compositeObject_info.co.IsDelete;
                                        exec_co_update.Add(target_compositeObject);

                                        
                                        client.Deleteable<CompositeObjectRelation>().Where(cor => cor.CompositeObjectId == target_compositeObject.Id).ExecuteCommand();
                                    }

                                    
                                    else if (target_compositeObject == null)
                                    {
                                        
                                        var bc_application = target_applications.Find(ta => ta.Name == bc_compositeObject_info.co.ApplicationName);
                                        var insert = new CompositeObject
                                        {
                                            Id = bc_compositeObject_info.co.Id,
                                            Name = bc_compositeObject_info.co.Name,
                                            DataBaseId = bc_base.Id,
                                            IsDelete = bc_compositeObject_info.co.IsDelete
                                        };
                                        if (bc_application != null)
                                        {
                                            insert.ApplicationId = bc_application.Id.ToString();
                                            insert.ApplicationName = bc_application.Name;
                                        }

                                        if (bc_compositeObject_info.co.BusinessObjectId != Guid.Empty)
                                        {
                                            var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == bc_compositeObject_info.bo.Name && f.DataBaseId == bc_base.Id);
                                            insert.BusinessObjectId = v_businessObject == null ? Guid.Empty : v_businessObject.Id;
                                        }

                                        exec_co_insert.Add(insert);

                                        id = insert.Id;
                                    }

                                    
                                    var bc_compositeObjectRelation_info = sync_compositeObjectRelation_info.FindAll(scori => scori.cor.CompositeObjectId == bc_compositeObject_info.co.Id).ToList();
                                    bc_compositeObjectRelation_info.ForEach(bcori =>
                                    {
                                        var insert_relation = new CompositeObjectRelation
                                        {
                                            Id = bcori.cor.Id,
                                            CompositeObjectId = id,
                                            BusinessObjectId = bcori.cor.BusinessObjectId,
                                            BusinessObjectColumnId = bcori.cor.BusinessObjectId,
                                            ParentBusinessObjectColumnId = bcori.cor.ParentBusinessObjectColumnId,
                                            ExtraCondition = bcori.cor.ExtraCondition,
                                            ParentId = bcori.cor.ParentId,
                                            JoinRelation = bcori.cor.JoinRelation,
                                            JoinType = bcori.cor.JoinType,
                                            ObjectType = bcori.cor.ObjectType
                                        };

                                        if (insert_relation.BusinessObjectId != Guid.Empty)
                                        {
                                            var v_businessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == bcori.bo.Name && f.DataBaseId == bc_base.Id);
                                            insert_relation.BusinessObjectId = v_businessObject == null ? Guid.Empty : v_businessObject.Id;
                                        }

                                        if (insert_relation.BusinessObjectColumnId != Guid.Empty)
                                        {
                                            var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == bcori.boc1.Name && f.BusinessObjectId == insert_relation.BusinessObjectId);
                                            insert_relation.BusinessObjectColumnId = v_businessObjectColumn == null ? Guid.Empty : v_businessObjectColumn.Id;
                                        }

                                        if (insert_relation.ParentBusinessObjectColumnId != Guid.Empty)
                                        {
                                            if (insert_relation.ParentId.HasValue && insert_relation.ParentId.Value != Guid.Empty)
                                            {
                                                var pRelation = bc_compositeObjectRelation_info.Find(f => f.cor.Id == insert_relation.ParentId.Value);
                                                var pBusinessObject = exec_bo_insert.Concat(exec_bo_update).Concat(target_businessObjects).ToList().Find(f => f.Name == pRelation.bo.Name && f.DataBaseId == bc_base.Id);
                                                var v_businessObjectColumn = exec_boc_insert.Concat(exec_boc_update).Concat(target_businessObjectColumns).ToList().Find(f => f.Name == bcori.boc2.Name && f.BusinessObjectId == pBusinessObject.Id);
                                                insert_relation.ParentBusinessObjectColumnId = v_businessObjectColumn == null ? Guid.Empty : v_businessObjectColumn.Id;
                                            }
                                        }

                                        exec_cor_insert.Add(insert_relation);
                                    });
                                }
                            });

                            if (exec_co_insert.Count > 0)
                            {
                                client.Insertable(exec_co_insert).ExecuteCommand();
                            }

                            if (exec_co_update.Count > 0)
                            {
                                client.Updateable(exec_co_update).ExecuteCommand();
                            }

                            if (exec_cor_insert.Count > 0)
                            {
                                client.Insertable(exec_cor_insert).ExecuteCommand();
                            }
                        }

                        
                        exec_bo_insert
                            .Concat(exec_bo_update)
                            .ToList()
                            .FindAll(f => f.IsCustom)
                            .ForEach(f =>
                        {
                            client.CodeFirst.InitTables(CommonHelper.CreateDataTableClass(f, exec_boc_insert.Concat(exec_boc_update).ToList().FindAll(fa => fa.BusinessObjectId == f.Id)));
                        });

                        
                        exec_v_insert.Concat(exec_v_update)
                            .ToList()
                            .FindAll(f => f.IsCustom)
                            .ForEach(f =>
                        {
                        });
                    }

                    break;
                case "pageModeling":
                    if (dto.PageModelings.Any(a => a.Exec == "create"))
                    {
                        
                        var target_bases = client.Queryable<DataBase>().ToList();

                        
                        var target_applications = client.Queryable<ApplicationEntity>().Where(ae => !ae.IsDelete).ToList();

                        
                        var target_modules = client.Queryable<ModuleEntity>().Where(ae => !ae.IsDelete).ToList();

                        
                        var target_businessObjects = client.Queryable<BusinessObject>().Where(bo => !bo.IsDelete.Value).ToList();

                        
                        var target_compositeObjects = client.Queryable<CompositeObject>().ToList();

                        
                        var target_views = client.Queryable<View>().ToList();

                        
                        var target_pageModelings = client.Queryable<PageModeling>().ToList();

                        
                        var target_dictionarys = client.Queryable<Dictionary>().Where(d => d.Status == 0).ToList();

                        
                        var bases = _dbContext.Modeling.Queryable<DataBase>().ToList();

                        
                        var applications = _dbContext.Modeling.Queryable<ApplicationEntity>().Where(ae => !ae.IsDelete).ToList();

                        
                        var modules = _dbContext.Modeling.Queryable<ModuleEntity>().Where(ae => !ae.IsDelete).ToList();

                        
                        var businessObjects = _dbContext.Modeling.Queryable<BusinessObject>().Where(bo => !bo.IsDelete.Value).ToList();

                        
                        var compositeObjects = _dbContext.Modeling.Queryable<CompositeObject>().ToList();

                        
                        var views = _dbContext.Modeling.Queryable<View>().ToList();

                        
                        var pageModelings = _dbContext.Modeling.Queryable<PageModeling>().ToList();

                        
                        var dictionarys = client.Queryable<Dictionary>().Where(d => d.Status == 0).ToList();

                        var exec_pm_insert = new List<PageModeling>();
                        var exec_pv_insert = new List<PageVersion>();
                        var sync_pageModeling = dto.PageModelings.FindAll(w => w.Exec == "create");
                        sync_pageModeling.ForEach(spm =>
                        {
                            
                            var bc_pageModeling_info = _dbContext.Modeling.Queryable<PageModeling>()
                            .LeftJoin<PageVersion>((pm, pv) => pm.Id == pv.PageModelingId && pv.Version == spm.Version)
                            .Where((pm, pv) => pm.Id == spm.Id)
                            .Select((pm, pv) => new { pm, pv })
                            .First();
                            if (bc_pageModeling_info != null)
                            {
                                
                                var newDesginJson = string.Empty;
                                var oldDesginJson = bc_pageModeling_info.pv != null ? bc_pageModeling_info.pv.PageDesginJson : bc_pageModeling_info.pm.PageDesginJson;
                                if (!string.IsNullOrEmpty(oldDesginJson))
                                {
                                    newDesginJson = BGDesinJson(oldDesginJson, target_bases, target_applications, target_modules, target_businessObjects, target_compositeObjects, target_views, target_pageModelings.Concat(exec_pm_insert).ToList(), target_dictionarys, bases, applications, modules, businessObjects, compositeObjects, views, pageModelings, dictionarys);
                                }

                                var newDataSource = string.Empty;
                                if (!string.IsNullOrEmpty(bc_pageModeling_info.pm.DataSource))
                                {
                                    var dataSorceArr = bc_pageModeling_info.pm.DataSource.Split("||");
                                    var oldDataBase = bases.Find(b => b.Id == Guid.Parse(dataSorceArr[0]));
                                    var newDataBase = target_bases.Find(tb => tb.OnlyCode == (oldDataBase != null ? oldDataBase.OnlyCode : string.Empty));
                                    if (newDataBase != null)
                                    {
                                        switch (dataSorceArr[2])
                                        {
                                            case "1":
                                                var oldBusinessObject = businessObjects.Find(bo => bo.Id == Guid.Parse(dataSorceArr[1]));
                                                var newBusinessObject = target_businessObjects.Find(tbo => tbo.Name == (oldBusinessObject != null ? oldBusinessObject.Name : string.Empty) && tbo.DataBaseId == newDataBase.Id);
                                                newDataSource = newBusinessObject != null ? $"{newDataBase.Id}||{newBusinessObject.Id}||1" : string.Empty;
                                                break;
                                            case "2":
                                                var oldCompositeObject = compositeObjects.Find(co => co.Id == Guid.Parse(dataSorceArr[1]));
                                                var newCompositeObject = target_compositeObjects.Find(tco => tco.Name == (oldCompositeObject != null ? oldCompositeObject.Name : string.Empty) && tco.DataBaseId == newDataBase.Id);
                                                newDataSource = newCompositeObject != null ? $"{newDataBase.Id}||{newCompositeObject.Id}||2" : string.Empty;
                                                break;
                                            case "3":
                                                var oldView = views.Find(v => v.Id == Guid.Parse(dataSorceArr[1]));
                                                var newView = target_views.Find(tv => tv.Name == (oldView != null ? oldView.Name : string.Empty) && tv.DatabaseId == newDataBase.Id);
                                                newDataSource = newView != null ? $"{newDataBase.Id}||{newView.Id}||3" : string.Empty;
                                                break;
                                        }
                                    }
                                }

                                DateTime now = DateTime.Now;

                                
                                var target_pageModeling = target_pageModelings.Find(tpm => tpm.Name == bc_pageModeling_info.pm.Name);
                                if (target_pageModeling != null)
                                {
                                    
                                    var target_pageModelingVersion = client.Queryable<PageVersion>().Where(pv => pv.PageModelingId == target_pageModeling.Id)
                                    .OrderByDescending(pv => pv.Version)
                                    .ToList();
                                    int maxVersion = 1;
                                    if (target_pageModelingVersion.Count == 0)
                                    {
                                        exec_pv_insert.Add(new PageVersion
                                        {
                                            Id = Guid.NewGuid(),
                                            CreateDate = now,
                                            UpdateDate = now,
                                            CreateUser = userId,
                                            UpdateUser = userId,
                                            PageDesginJson = target_pageModeling.PageDesginJson,
                                            Status = 1,
                                            PageModelingId = target_pageModeling.Id,
                                            DataSource = target_pageModeling.DataSource,
                                            Version = 1
                                        });
                                    }
                                    else
                                    {
                                        maxVersion = target_pageModelingVersion.First().Version;
                                    }

                                    var insert_pv = new PageVersion
                                    {
                                        Id = Guid.NewGuid(),
                                        CreateDate = now,
                                        UpdateDate = now,
                                        CreateUser = userId,
                                        UpdateUser = userId,
                                        PageDesginJson = newDesginJson,
                                        Version = maxVersion + 1,
                                        SourceVersion = maxVersion,
                                        Status = 0,
                                        PageModelingId = target_pageModeling.Id
                                    };

                                    if (!string.IsNullOrEmpty(newDataSource))
                                    {
                                        insert_pv.DataSource = newDataSource;
                                    }

                                    exec_pv_insert.Add(insert_pv);
                                }
                                else
                                {
                                    
                                    var bc_application = target_applications.Find(ta => ta.Name == bc_pageModeling_info.pm.ApplicationName);
                                    if (bc_application != null)
                                    {
                                        
                                        var bc_module = target_modules.Find(tm => tm.Name == bc_pageModeling_info.pm.ModuleName && tm.ApplicationId == bc_application.Id);
                                        if (bc_module != null)
                                        {
                                            var insert_pm = new PageModeling
                                            {
                                                Id = Guid.NewGuid(),
                                                CreateDate = now,
                                                UpdateDate = now,
                                                CreateUser = userId,
                                                UpdateUser = userId,
                                                Name = bc_pageModeling_info.pm.Name,
                                                Type = bc_pageModeling_info.pm.Type,
                                                PageDesginJson = newDesginJson,
                                                Status = 0,
                                                DataSourceType = bc_pageModeling_info.pm.DataSourceType,
                                                ApplicationEquipment = bc_pageModeling_info.pm.ApplicationEquipment,
                                                ApplicationId = bc_application.Id,
                                                ApplicationName = bc_application.Name,
                                                ModuleId = bc_module.Id,
                                                ModuleName = bc_module.Name,
                                                Version = 1,
                                            };

                                            if (!string.IsNullOrEmpty(newDataSource))
                                            {
                                                insert_pm.DataSource = newDataSource;
                                            }

                                            exec_pm_insert.Add(insert_pm);

                                            var insert_pv = new PageVersion
                                            {
                                                Id = Guid.NewGuid(),
                                                CreateDate = now,
                                                UpdateDate = now,
                                                CreateUser = userId,
                                                UpdateUser = userId,
                                                PageDesginJson = newDesginJson,
                                                Version = 1,
                                                Status = 0,
                                                PageModelingId = insert_pm.Id
                                            };

                                            if (!string.IsNullOrEmpty(newDataSource))
                                            {
                                                insert_pv.DataSource = newDataSource;
                                            }

                                            exec_pv_insert.Add(insert_pv);
                                        }
                                    }
                                }
                            }
                        });

                        if (exec_pm_insert.Count > 0)
                        {
                            client.Insertable(exec_pm_insert).ExecuteCommand();
                        }

                        if (exec_pv_insert.Count > 0)
                        {
                            client.Insertable(exec_pv_insert).ExecuteCommand();
                        }
                    }

                    break;
            }

            return resultObject;
        }

        private string BGDesinJson(
            string json,
            List<DataBase> target_bases,
            List<ApplicationEntity> target_applications,
            List<ModuleEntity> target_modules,
            List<BusinessObject> target_businessObjects,
            List<CompositeObject> target_compositeObjects,
            List<View> target_views,
            List<PageModeling> target_pageModelings,
            List<Dictionary> target_dictionarys,
            List<DataBase> bases,
            List<ApplicationEntity> applications,
            List<ModuleEntity> modules,
            List<BusinessObject> businessObjects,
            List<CompositeObject> compositeObjects,
            List<View> views,
            List<PageModeling> pageModelings,
            List<Dictionary> dictionarys)
        {
            var djObject = JObject.Parse(json);
            var infoJobject = JObject.Parse(djObject["info"].ToString());
            var oldDataBase = bases.Find(b => b.Id == Guid.Parse(infoJobject["dataBaseId"].ToString()));
            var newDataBase = target_bases.Find(tb => tb.OnlyCode == (oldDataBase != null ? oldDataBase.OnlyCode : string.Empty));
            infoJobject["dataBaseId"] = newDataBase != null ? newDataBase.Id.ToString() : string.Empty;
            switch (infoJobject["businessObjectType"].ToString())
            {
                case "1":
                    var oldBusinessObject = businessObjects.Find(bo => bo.Id == Guid.Parse(infoJobject["businessObjectId"].ToString()));
                    var newBusinessObject = target_businessObjects.Find(tbo => tbo.Name == (oldBusinessObject != null ? oldBusinessObject.Name : string.Empty) && tbo.DataBaseId == newDataBase.Id);
                    infoJobject["businessObjectId"] = newBusinessObject != null ? newBusinessObject.Id.ToString() : string.Empty;
                    break;
                case "2":
                    var oldCompositeObject = compositeObjects.Find(co => co.Id == Guid.Parse(infoJobject["businessObjectId"].ToString()));
                    var newCompositeObject = target_compositeObjects.Find(tco => tco.Name == (oldCompositeObject != null ? oldCompositeObject.Name : string.Empty) && tco.DataBaseId == newDataBase.Id);
                    infoJobject["businessObjectId"] = newCompositeObject != null ? newCompositeObject.Id.ToString() : string.Empty;
                    break;
                case "3":
                    var oldView = views.Find(v => v.Id == Guid.Parse(infoJobject["businessObjectId"].ToString()));
                    var newView = target_views.Find(tv => tv.Name == (oldView != null ? oldView.Name : string.Empty) && tv.DatabaseId == newDataBase.Id);
                    infoJobject["businessObjectId"] = newView != null ? newView.Id.ToString() : string.Empty;
                    break;
            }

            var oldApplication = applications.Find(a => a.Id == Guid.Parse(infoJobject["applicationId"].ToString()));
            var newApplication = target_applications.Find(ta => ta.Name == (oldApplication != null ? oldApplication.Name : string.Empty));
            infoJobject["applicationId"] = newApplication != null ? newApplication.Id.ToString() : string.Empty;
            infoJobject["applicationName"] = newApplication != null ? newApplication.Name : string.Empty;
            var oldModule = modules.Find(a => a.Id == Guid.Parse(infoJobject["moduleId"].ToString()));
            var newModule = target_modules.Find(tm => tm.Name == (oldModule != null ? oldModule.Name : string.Empty) && tm.ApplicationId == (newApplication != null ? newApplication.Id : Guid.Empty));
            infoJobject["moduleId"] = newModule != null ? newModule.Id.ToString() : string.Empty;
            infoJobject["moduleName"] = newModule != null ? newModule.Name : string.Empty;

            
            if (infoJobject.ContainsKey("virtualComponent"))
            {
                var virtualComponentArray = JArray.Parse(infoJobject["virtualComponent"].ToString());
                for (var i = 0; i < virtualComponentArray.Count; i++)
                {
                    var virtualComponentObject = JObject.Parse(virtualComponentArray[i].ToString());
                    switch (virtualComponentObject["action"].ToString())
                    {
                        case "add":
                        case "view":
                        case "edit":
                        case "copyAndAdd":
                            if ((virtualComponentObject["model"].ToString() == "modal" ||
                            virtualComponentObject["model"].ToString() == "new") &&
                            virtualComponentObject["isCustomPage"].ToString().ToLower() == "false" &&
                            !string.IsNullOrEmpty(virtualComponentObject["contentPage"].ToString()))
                            {
                                var oldPageModeling = pageModelings.Find(pm => pm.Id == Guid.Parse(virtualComponentObject["contentPage"].ToString()));
                                var newPageModeling = target_pageModelings.Find(tpm => tpm.Name == (oldPageModeling != null ? oldPageModeling.Name : string.Empty));
                                virtualComponentObject["contentPage"] = newPageModeling != null ? newPageModeling.Id.ToString() : string.Empty;
                            }

                            break;
                        case "start":
                            var processObject = JObject.Parse(virtualComponentObject["process"].ToString());
                            if (!string.IsNullOrEmpty(processObject["businessObjectId"].ToString()))
                            {
                                var businessObjectId_process = processObject["businessObjectId"].ToString().Split("||");
                                var businessObjectId = BGObject(
                                    businessObjectId_process[0],
                                    businessObjectId_process[1],
                                    target_bases,
                                    target_businessObjects,
                                    target_compositeObjects,
                                    target_views,
                                    bases,
                                    businessObjects,
                                    compositeObjects,
                                    views);
                                processObject["businessObjectId"] = $"{businessObjectId}||{businessObjectId_process[1]}";
                                virtualComponentObject["process"] = processObject;
                            }

                            break;
                    }

                    virtualComponentArray[i] = virtualComponentObject;
                }

                infoJobject["virtualComponent"] = virtualComponentArray;
            }

            
            if (djObject.ContainsKey("components"))
            {
                var componentArray = JArray.Parse(djObject["components"].ToString());
                for (var i = 0; i < componentArray.Count; i++)
                {
                    componentArray[i] = BGForEachComponents(
                        componentArray[i].ToString(),
                        target_bases,
                        target_businessObjects,
                        target_compositeObjects,
                        target_views,
                        target_dictionarys,
                        bases,
                        businessObjects,
                        compositeObjects,
                        views,
                        dictionarys);
                }

                djObject["components"] = componentArray;
            }

            djObject["info"] = infoJobject;

            return djObject.ToJsonString();
        }

        private void TQDesinJson(string json, List<Guid> ywdx_id, List<Guid> zhdx_id, List<Guid> st_id, List<PageModeling> ym_id)
        {
            var djObject = JObject.Parse(json);
            var infoJobject = JObject.Parse(djObject["info"].ToString());

            
            var businessObjectId = infoJobject["businessObjectId"].ToString();
            var businessObjectType = infoJobject["businessObjectType"].ToString();
            AddObject(businessObjectId, businessObjectType, ywdx_id, zhdx_id, st_id);

            
            if (infoJobject.ContainsKey("virtualComponent"))
            {
                var virtualComponentArray = JArray.Parse(infoJobject["virtualComponent"].ToString());
                foreach (var virtualComponent in virtualComponentArray)
                {
                    var virtualComponentObject = JObject.Parse(virtualComponent.ToString());
                    switch (virtualComponentObject["action"].ToString())
                    {
                        case "add":
                        case "view":
                        case "edit":
                        case "copyAndAdd":
                            if ((virtualComponentObject["model"].ToString() == "modal" ||
                            virtualComponentObject["model"].ToString() == "new") &&
                            virtualComponentObject["isCustomPage"].ToString().ToLower() == "false" &&
                            !string.IsNullOrEmpty(virtualComponentObject["contentPage"].ToString()))
                            {
                                var pm = _dbContext.Modeling.Queryable<PageModeling>()
                                .LeftJoin<PageVersion>((pm, pv) => pm.Id == pv.PageModelingId && SqlFunc.IsNull(pm.Version, 1) == pv.Version)
                                .Where((pm, pv) => pm.Id == SqlFunc.ToGuid(virtualComponentObject["contentPage"].ToString()))
                                .Select((pm, pv) => new
                                {
                                    id = pm.Id,
                                    name = pm.Name,
                                    version = SqlFunc.IsNull(pm.Version, 1),
                                    desginJson = SqlFunc.IsNull(pv.PageDesginJson, pm.PageDesginJson)
                                })
                                .First();
                                if (pm != null)
                                {
                                    TQDesinJson(pm.desginJson, ywdx_id, zhdx_id, st_id, ym_id);
                                    var idx = ym_id.FindIndex(y => y.Id == pm.id);
                                    if (idx > -1)
                                    {
                                        ym_id.RemoveAt(idx);
                                    }

                                    ym_id.Add(new PageModeling { Id = pm.id, Name = pm.name, Version = pm.version });
                                }
                            }

                            break;
                        case "start":
                            var processObject = JObject.Parse(virtualComponentObject["process"].ToString());
                            if (!string.IsNullOrEmpty(processObject["businessObjectId"].ToString()))
                            {
                                var businessObjectId_process = processObject["businessObjectId"].ToString().Split("||");
                                AddObject(businessObjectId_process[0], businessObjectId_process[1], ywdx_id, zhdx_id, st_id);
                            }

                            break;
                    }
                }
            }

            
            if (djObject.ContainsKey("components"))
            {
                var componentArray = JArray.Parse(djObject["components"].ToString());
                foreach (var component in componentArray)
                {
                    var componentObject = JObject.Parse(component.ToString());
                    ForEachComponents(component.ToString(), ywdx_id, zhdx_id, st_id);
                }
            }
        }

        private void ForEachComponents(string control, List<Guid> ywdx_id, List<Guid> zhdx_id, List<Guid> st_id)
        {
            var controlObject = JObject.Parse(control.ToString());
            switch (controlObject["type"].ToString())
            {
                case "mmt-select":
                case "mmt-check-box":
                case "mmt-radio":
                    if (controlObject["optionsModel"].ToString() == "businessObject" &&
                        !string.IsNullOrEmpty(controlObject["businessObjectId"].ToString()))
                    {
                        var businessObjectId_component = controlObject["businessObjectId"].ToString().Split("||");
                        AddObject(businessObjectId_component[0], businessObjectId_component[1], ywdx_id, zhdx_id, st_id);
                    }

                    break;
                case "mmt-tree":
                    if (!string.IsNullOrEmpty(controlObject["dataSource"].ToString()))
                    {
                        var dataSource1 = controlObject["dataSource"].ToString().Split("||");
                        AddObject(dataSource1[0], "1", ywdx_id, zhdx_id, st_id);
                    }

                    break;
                case "mmt-cascader":
                case "mmt-tree-select":
                    if (!string.IsNullOrEmpty(controlObject["dataSource"].ToString()))
                    {
                        var dataSource2 = controlObject["dataSource"].ToString().Split("||");
                        AddObject(dataSource2[0], dataSource2[1], ywdx_id, zhdx_id, st_id);
                    }

                    break;
                case "mmt-collapse":
                case "mmt-card":
                    var childrenArray1 = JArray.Parse(controlObject["children"].ToString());
                    foreach (var children in childrenArray1)
                    {
                        ForEachComponents(children.ToString(), ywdx_id, zhdx_id, st_id);
                    }

                    break;
                case "mmt-tabs":
                    var tabArray = JArray.Parse(controlObject["tabs"].ToString());
                    foreach (var tab in tabArray)
                    {
                        var childrenArray2 = JArray.Parse(tab["children"].ToString());
                        foreach (var children in childrenArray2)
                        {
                            ForEachComponents(children.ToString(), ywdx_id, zhdx_id, st_id);
                        }
                    }

                    break;
                case "mmt-row":
                    var childrenArray3 = JArray.Parse(controlObject["children"].ToString());
                    foreach (var children in childrenArray3)
                    {
                        if (controlObject["isCustomCol"].ToOurLowerString() == "true")
                        {
                            var componentArray = JArray.Parse(children["component"].ToString());
                            foreach (var component in componentArray)
                            {
                                ForEachComponents(component.ToString(), ywdx_id, zhdx_id, st_id);
                            }
                        }
                        else
                        {
                            ForEachComponents(children.ToString(), ywdx_id, zhdx_id, st_id);
                        }
                    }

                    break;
                case "mmt-grid":
                    var rowArray = JArray.Parse(controlObject["rows"].ToString());
                    foreach (var row in rowArray)
                    {
                        var columnArray = JArray.Parse(row["columns"].ToString());
                        foreach (var column in columnArray)
                        {
                            var listArray = JArray.Parse(column["list"].ToString());
                            foreach (var list in listArray)
                            {
                                ForEachComponents(list.ToString(), ywdx_id, zhdx_id, st_id);
                            }
                        }
                    }

                    break;
            }
        }

        private JObject BGForEachComponents(
            string control,
            List<DataBase> target_bases,
            List<BusinessObject> target_businessObjects,
            List<CompositeObject> target_compositeObjects,
            List<View> target_views,
            List<Dictionary> target_dictionarys,
            List<DataBase> bases,
            List<BusinessObject> businessObjects,
            List<CompositeObject> compositeObjects,
            List<View> views,
            List<Dictionary> dictionarys)
        {
            var controlObject = JObject.Parse(control.ToString());
            switch (controlObject["type"].ToString())
            {
                case "mmt-select":
                case "mmt-check-box":
                case "mmt-radio":
                    if (controlObject["optionsModel"].ToString() == "businessObject" &&
                        !string.IsNullOrEmpty(controlObject["businessObjectId"].ToString()))
                    {
                        var businessObjectId_component = controlObject["businessObjectId"].ToString().Split("||");
                        var businessObjectId = BGObject(
                              businessObjectId_component[0],
                              businessObjectId_component[1],
                              target_bases,
                              target_businessObjects,
                              target_compositeObjects,
                              target_views,
                              bases,
                              businessObjects,
                              compositeObjects,
                              views);
                        controlObject["businessObjectId"] = $"{businessObjectId}||{businessObjectId_component[1]}";
                    }
                    else if (controlObject["optionsModel"].ToString() == "dictionary" &&
                        !string.IsNullOrEmpty(controlObject["dictionaryId"].ToString()))
                    {
                        var oldDictionary = dictionarys.Find(d => d.Id == Guid.Parse(controlObject["dictionaryId"].ToString()));
                        var newDictionary = target_dictionarys.Find(d => d.Code == (oldDictionary != null ? oldDictionary.Code : string.Empty));
                        controlObject["dictionaryId"] = newDictionary != null ? newDictionary.Id.ToString() : string.Empty;
                    }

                    break;
                case "mmt-tree":
                    if (!string.IsNullOrEmpty(controlObject["dataSource"].ToString()))
                    {
                        var dataSource1 = controlObject["dataSource"].ToString().Split("||");
                        var businessObjectId = BGObject(
                           dataSource1[0],
                           "1",
                           target_bases,
                           target_businessObjects,
                           target_compositeObjects,
                           target_views,
                           bases,
                           businessObjects,
                           compositeObjects,
                           views);
                        controlObject["businessObjectId"] = $"{businessObjectId}||1";
                    }

                    break;
                case "mmt-cascader":
                case "mmt-tree-select":
                    if (!string.IsNullOrEmpty(controlObject["dataSource"].ToString()))
                    {
                        var dataSource2 = controlObject["dataSource"].ToString().Split("||");
                        var businessObjectId = BGObject(
                          dataSource2[0],
                          dataSource2[1],
                          target_bases,
                          target_businessObjects,
                          target_compositeObjects,
                          target_views,
                          bases,
                          businessObjects,
                          compositeObjects,
                          views);
                        controlObject["businessObjectId"] = $"{businessObjectId}||{dataSource2[1]}";
                    }

                    break;
                case "mmt-collapse":
                case "mmt-card":
                    var childrenArray1 = JArray.Parse(controlObject["children"].ToString());
                    for (var i = 0; i < childrenArray1.Count; i++)
                    {
                        childrenArray1[i] = BGForEachComponents(
                          childrenArray1[i].ToString(),
                          target_bases,
                          target_businessObjects,
                          target_compositeObjects,
                          target_views,
                          target_dictionarys,
                          bases,
                          businessObjects,
                          compositeObjects,
                          views,
                          dictionarys);
                    }

                    controlObject["children"] = childrenArray1;
                    break;
                case "mmt-tabs":
                    var tabArray = JArray.Parse(controlObject["tabs"].ToString());
                    for (var i = 0; i < tabArray.Count; i++)
                    {
                        var childrenArray2 = JArray.Parse(tabArray[i]["children"].ToString());
                        for (var j = 0; j < childrenArray2.Count; j++)
                        {
                            childrenArray2[j] = BGForEachComponents(
                             childrenArray2[j].ToString(),
                             target_bases,
                             target_businessObjects,
                             target_compositeObjects,
                             target_views,
                             target_dictionarys,
                             bases,
                             businessObjects,
                             compositeObjects,
                             views,
                             dictionarys);
                        }

                        tabArray[i]["children"] = childrenArray2;
                    }

                    controlObject["tabs"] = tabArray;
                    break;
                case "mmt-row":
                    var childrenArray3 = JArray.Parse(controlObject["children"].ToString());
                    for (var i = 0; i < childrenArray3.Count; i++)
                    {
                        var componentArray = JArray.Parse(childrenArray3[i]["component"].ToString());
                        for (var j = 0; j < componentArray.Count; j++)
                        {
                            componentArray[j] = BGForEachComponents(
                             componentArray[j].ToString(),
                             target_bases,
                             target_businessObjects,
                             target_compositeObjects,
                             target_views,
                             target_dictionarys,
                             bases,
                             businessObjects,
                             compositeObjects,
                             views,
                             dictionarys);
                        }

                        childrenArray3[i]["component"] = componentArray;
                    }

                    controlObject["children"] = childrenArray3;
                    break;
                case "mmt-grid":
                    var rowArray = JArray.Parse(controlObject["rows"].ToString());
                    for (var i = 0; i < rowArray.Count; i++)
                    {
                        var columnArray = JArray.Parse(rowArray[i]["columns"].ToString());
                        for (var j = 0; j < columnArray.Count; j++)
                        {
                            var listArray = JArray.Parse(columnArray[j]["list"].ToString());
                            for (var k = 0; k < listArray.Count; k++)
                            {
                                listArray[k] = BGForEachComponents(
                                 listArray[k].ToString(),
                                 target_bases,
                                 target_businessObjects,
                                 target_compositeObjects,
                                 target_views,
                                 target_dictionarys,
                                 bases,
                                 businessObjects,
                                 compositeObjects,
                                 views,
                                 dictionarys);
                            }

                            columnArray[j]["list"] = listArray;
                        }

                        rowArray[i]["columns"] = columnArray;
                    }

                    controlObject["rows"] = rowArray;
                    break;
            }

            return controlObject;
        }

        private void AddObject(string id, string type, List<Guid> ywdx_id, List<Guid> zhdx_id, List<Guid> st_id)
        {
            var gid = Guid.Parse(id);
            switch (type)
            {
                case "1":
                    if (!ywdx_id.Contains(gid))
                    {
                        ywdx_id.Add(gid);
                    }

                    break;
                case "2":
                    if (!zhdx_id.Contains(gid))
                    {
                        zhdx_id.Add(gid);
                    }

                    break;
                case "3":
                    if (!st_id.Contains(gid))
                    {
                        st_id.Add(gid);
                    }

                    break;
            }
        }

        private string BGObject(
            string id,
            string type,
            List<DataBase> target_bases,
            List<BusinessObject> target_businessObjects,
            List<CompositeObject> target_compositeObjects,
            List<View> target_views,
            List<DataBase> bases,
            List<BusinessObject> businessObjects,
            List<CompositeObject> compositeObjects,
            List<View> views)
        {
            var result = string.Empty;
            switch (type)
            {
                case "1":
                    var oldBusinessObject = businessObjects.Find(bo => bo.Id == Guid.Parse(id));
                    var oldDataBase = bases.Find(b => b.Id == (oldBusinessObject != null ? oldBusinessObject.DataBaseId : Guid.Empty));
                    var newDataBase = target_bases.Find(tb => tb.OnlyCode == (oldDataBase != null ? oldDataBase.OnlyCode : string.Empty));
                    var newBusinessObject = target_businessObjects.Find(tbo => tbo.Name == (oldBusinessObject != null ? oldBusinessObject.Name : string.Empty) && tbo.DataBaseId == (newDataBase != null ? newDataBase.Id : Guid.Empty));
                    result = newBusinessObject != null ? newBusinessObject.Id.ToString() : string.Empty;
                    break;
                case "2":
                    var oldCompositeObject = compositeObjects.Find(co => co.Id == Guid.Parse(id));
                    oldDataBase = bases.Find(b => b.Id == (oldCompositeObject != null ? oldCompositeObject.DataBaseId : Guid.Empty));
                    newDataBase = target_bases.Find(tb => tb.OnlyCode == (oldDataBase != null ? oldDataBase.OnlyCode : string.Empty));
                    var newCompositeObject = target_compositeObjects.Find(tco => tco.Name == (oldCompositeObject != null ? oldCompositeObject.Name : string.Empty) && tco.DataBaseId == (newDataBase != null ? newDataBase.Id : Guid.Empty));
                    result = newCompositeObject != null ? newCompositeObject.Id.ToString() : string.Empty;
                    break;
                case "3":
                    var oldView = views.Find(v => v.Id == Guid.Parse(id));
                    oldDataBase = bases.Find(b => b.Id == (oldView != null ? oldView.DatabaseId : Guid.Empty));
                    newDataBase = target_bases.Find(tb => tb.OnlyCode == (oldDataBase != null ? oldDataBase.OnlyCode : string.Empty));
                    var newView = target_views.Find(tv => tv.Name == (oldView != null ? oldView.Name : string.Empty) && tv.DatabaseId == (newDataBase != null ? newDataBase.Id : Guid.Empty));
                    result = newView != null ? newView.Id.ToString() : string.Empty;
                    break;
            }

            return result;
        }

        private void ForEachCopyData(SqlSugarClient client, List<ObjectStructureDto> fields, Dictionary<string, List<Dictionary<string, object>>> inList, object parentData, object newParentData, ObjectStructureDto parentStructure, Dictionary<string, object> copyUser, Guid? parentId, List<string> copyFields)
        {
            List<ObjectStructureDto> li = new List<ObjectStructureDto>();
            if (parentId.HasValue)
            {
                li = fields.Where(w => w.IsCustom && w.ObjectType == "businessObject" && !w.IsMain && w.ParentId.HasValue && w.ParentId.Value == parentId).ToList();
            }
            else
            {
                li = fields.Where(w => w.IsCustom && w.ObjectType == "businessObject" && !w.IsMain && !w.ParentId.HasValue).ToList();
            }

            li.ForEach(lif =>
            {
                var query = client.Queryable(lif.Name, "Cust" + lif.Name);
                var pcolumn = lif.Columns.Find(f => f.IsPrimaryKey.Value).Name;
                var pcC = parentStructure.Columns.Where(w => w.Id == lif.MainDataTableColumnId).FirstOrDefault();
                var dcC = lif.Columns.Where(w => w.Id == lif.DetailDataTableColumnId).FirstOrDefault();
                var conditonModels = new List<IConditionalModel>
                {
                    new ConditionalModel()
                    {
                        FieldName = $"Cust{lif.Name}.{dcC.Name}",
                        ConditionalType = ConditionalType.Equal,
                        FieldValue = parentData.ToString()
                    }
                };
                query.Where(conditonModels);
                var tableDate = query.ToList();
                if (tableDate != null)
                {
                    var tableDateArray = JArray.FromObject(tableDate);
                    foreach (var array in tableDateArray)
                    {
                        var data = new Dictionary<string, object>();
                        var tableDateObject = JObject.Parse(array.ToString());
                        lif.Columns.ForEach(f =>
                        {
                            if (tableDateObject[f.Name].Type != JTokenType.Null && copyFields.Contains($"{lif.Name}_{f.Name}"))
                            {
                                data.Add(f.Name, tableDateObject[f.Name]);

                                if (tableDateObject[f.Name].Type == JTokenType.Boolean)
                                {
                                    data[f.Name] = tableDateObject[f.Name].ToOurBoolean();
                                }
                            }
                        });

                        if (data.ContainsKey(pcolumn))
                        {
                            data[pcolumn] = Guid.NewGuid();
                        }
                        else
                        {
                            data.Add(pcolumn, Guid.NewGuid());
                        }

                        if (data.ContainsKey(dcC.Name))
                        {
                            data[dcC.Name] = newParentData.ToString();
                        }
                        else
                        {
                            data.Add(dcC.Name, newParentData.ToString());
                        }

                        if (!data.ContainsKey("CreateDate"))
                        {
                            data.Add("CreateDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        }

                        if (!data.ContainsKey("CreateUserId"))
                        {
                            data.Add("CreateUserId", copyUser["userAccount"]);
                        }

                        if (!data.ContainsKey("CreateUserOrgPathId"))
                        {
                            data.Add("CreateUserOrgPathId", copyUser["userOrgPath"]);
                        }

                        if (!data.ContainsKey("IsDelete"))
                        {
                            data.Add("IsDelete", 0);
                        }

                        if (copyFields.Contains(lif.Name))
                        {
                            if (inList.ContainsKey(lif.Name))
                            {
                                inList[lif.Name].Add(data);
                            }
                            else
                            {
                                inList.Add(lif.Name, new List<Dictionary<string, object>> { data });
                            }
                        }

                        
                        ForEachCopyData(client, fields, inList, tableDateObject[dcC.Name], data[dcC.Name], lif, copyUser, lif.CompositeObjectRelationId, copyFields);
                    }
                }
            });
        }

        private void ForEachComponents(JArray arry, List<ItemPairBase> dic)
        {
            foreach (var arr in arry)
            {
                var arrBo = JObject.Parse(arr.ToString());
                if (arrBo["type"].ToString() == "mmt-button")
                {
                    dic.Add(new ItemPairBase { Label = arrBo["text"].ToString(), Value = arrBo["id"].ToString() });
                }
                else if (arrBo["type"].ToString() == "mmt-table")
                {
                    var headerLeftButtonGroup = JObject.Parse(arrBo["headerLeftButton"].ToString());
                    var headerLeftButton = JArray.Parse(headerLeftButtonGroup["children"].ToString());
                    if (headerLeftButton != null && headerLeftButton.Count > 0)
                    {
                        foreach (var button in headerLeftButton)
                        {
                            var bo = JObject.Parse(button.ToString());
                            dic.Add(new ItemPairBase { Label = bo["text"].ToString(), Value = bo["id"].ToString() });
                        }
                    }

                    var headerRightButtonGroup = JObject.Parse(arrBo["headerRightButton"].ToString());
                    var headerRightButton = JArray.Parse(headerRightButtonGroup["children"].ToString());
                    if (headerRightButton != null && headerRightButton.Count > 0)
                    {
                        foreach (var button in headerRightButton)
                        {
                            var bo = JObject.Parse(button.ToString());
                            dic.Add(new ItemPairBase { Label = bo["text"].ToString(), Value = bo["id"].ToString() });
                        }
                    }

                    var rowButtonGroup = JObject.Parse(arrBo["rowButton"].ToString());
                    var rowButton = JArray.Parse(rowButtonGroup["children"].ToString());
                    if (rowButton != null && rowButton.Count > 0)
                    {
                        foreach (var button in rowButton)
                        {
                            var bo = JObject.Parse(button.ToString());
                            dic.Add(new ItemPairBase { Label = bo["text"].ToString(), Value = bo["id"].ToString() });
                        }
                    }
                }
                else if (arrBo["type"].ToString() == "mmt-collapse" || arrBo["type"].ToString() == "mmt-card" || arrBo["type"].ToString() == "mmt-button-group")
                {
                    var children = JArray.Parse(arrBo["children"].ToString());
                    this.ForEachComponents(children, dic);
                }
                else if (arrBo["type"].ToString() == "mmt-tabs")
                {
                    var tabs = JArray.Parse(arrBo["tabs"].ToString());
                    foreach (var tab in tabs)
                    {
                        var tabBo = JObject.Parse(tab.ToString());
                        var children = JArray.Parse(tabBo["children"].ToString());
                        this.ForEachComponents(children, dic);
                    }
                }
                else if (arrBo["type"].ToString() == "mmt-row")
                {
                    var rows = JArray.Parse(arrBo["children"].ToString());
                    foreach (var row in rows)
                    {
                        var rowBo = JObject.Parse(row.ToString());
                        var component = JArray.Parse(rowBo["component"].ToString());
                        this.ForEachComponents(component, dic);
                    }
                }
            }
        }

        
        
        
        
        
        private (ICellStyle borderStyle, ICellStyle titleStyle) GetExcelStyle(IWorkbook workbook)
        {
            #region 

            
            ICellStyle borderStyle = workbook.CreateCellStyle();
            borderStyle.BorderLeft = BorderStyle.Thin;
            borderStyle.BorderRight = BorderStyle.Thin;
            borderStyle.BorderTop = BorderStyle.Thin;
            borderStyle.BorderBottom = BorderStyle.Thin;
            #endregion

            #region 

            
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;  
            titleStyle.VerticalAlignment = VerticalAlignment.Center;  
            IFont font = workbook.CreateFont(); 
            font.IsBold = true; 
            font.FontHeightInPoints = 10; 
            font.FontName = "微软雅黑"; 
            titleStyle.SetFont(font); 
            titleStyle.FillForegroundColor = HSSFColor.Grey25Percent.Index; 
            titleStyle.FillPattern = FillPattern.SolidForeground;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            #endregion

            return (borderStyle, titleStyle);
        }

        #endregion
    }
}
