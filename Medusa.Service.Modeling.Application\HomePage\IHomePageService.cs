using System;
using System.Collections.Generic;
using System.Text;
using Medusa.Service.Modeling.Application.CRMReport.Dto;
using Medusa.Service.Modeling.Application.HomePage.Dto;

namespace Medusa.Service.Modeling.Application.HomePage
{
    
    
    
    public interface IHomePageService
    {
        
        
        
        
        GetSubmittedToMeDto GetSubmittedToMe();

        
        
        
        
        List<DailyWorkReportDto> GetMySubmitted();

        
        
        
        
        
        List<CustomerComplaintDto> GetCustomerComplaint(CustomerComplaintQueryDto queryDto);

        
        
        
        
        bool CheckUser();
    }
}
