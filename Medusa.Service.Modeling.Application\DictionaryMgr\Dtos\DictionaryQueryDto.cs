using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.DictionaryMgr.Dtos
{
    
    
    
    public class DictionaryQueryDto : PageQueryDtoBase
    {
        
        
        
        public bool? EnableOrgPermission { get; set; }

        
        
        
        public string OrgPath { get; set; }

        
        
        
        public bool? EnableUserPermission { get; set; }

        
        
        
        public string UserLoginId { get; set; }
    }
}
