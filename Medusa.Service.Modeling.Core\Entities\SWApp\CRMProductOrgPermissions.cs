using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 字典组织权限
    /// </summary>
    [EntityTable("CRMProductOrgPermissions")]
    public class CRMProductOrgPermissions
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 组织路径
        /// </summary>
        public string OrgPath { get; set; }

        /// <summary>
        /// 组织名称
        /// </summary>
        public string OrgName { get; set; }

        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// '是否删除',
        /// </summary>
        public int IsDelete { get; set; }
    }
}
