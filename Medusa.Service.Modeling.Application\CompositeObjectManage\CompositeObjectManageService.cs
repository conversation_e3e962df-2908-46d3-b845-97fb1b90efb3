using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Utils.Extensions;
using MySqlX.XDevAPI;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.CompositeObjectManage
{
    
    
    
    public class CompositeObjectManageService : ServiceBase, ICompositeObjectManageService
    {
        #region 
        readonly MyDbContext _dbContext;
        private readonly IStructureCacheService _structureCacheService;

        
        
        
        
        
        public CompositeObjectManageService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _structureCacheService = serviceProvider.GetService<IStructureCacheService>();
        }

        #endregion

        
        
        
        
        
        public PageResult<CompositeObjectDto> GetCompositeObjects(CompositeObjectQueryDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<CompositeObject, BusinessObject, DataBase>((a, b, c) => a.BusinessObjectId == b.Id && b.DataBaseId == c.Id)
                .WhereIF(!string.IsNullOrEmpty(dto.Name), (a, b, c) => a.Name.Contains(dto.Name))
                .WhereIF(!string.IsNullOrEmpty(dto.BusinessObject), (a, b, c) => b.Name.Contains(dto.BusinessObject) || b.Description.Contains(dto.BusinessObject))
                .WhereIF(!string.IsNullOrEmpty(dto.ApplicationId), (a, b, c) => a.ApplicationId == dto.ApplicationId)
                .WhereIF(dto.Id.HasValue, (a, b, c) => a.Id == dto.Id)
                .Where((a, b, c) => !a.IsDelete.Value)
                .Select((a, b, c) => new CompositeObjectDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    DataBaseId = a.DataBaseId,
                    DataBaseName = c.Name,
                    BusinessObjectId = b.Id,
                    BusinessObjectName = b.Name,
                    BusinessObjectDescription = b.Description,
                    ApplicationId = a.ApplicationId,
                    ApplicationName = a.ApplicationName
                });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<CompositeObjectDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        
        public CompositeObjectDto GetCompositeObject(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<CompositeObject, DataBase, BusinessObject>((a, b, c) => a.DataBaseId == b.Id && a.BusinessObjectId == c.Id)
                .Where((a, b, c) => !a.IsDelete.Value && a.Id == id)
                .Select((a, b, c) => new CompositeObjectDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    DataBaseId = b.Id,
                    DataBaseName = b.Name,
                    BusinessObjectId = c.Id,
                    BusinessObjectName = c.Name,
                    BusinessObjectDescription = c.Description,
                    ApplicationId = a.ApplicationId,
                    ApplicationName = a.ApplicationName
                }).First();
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.compositeObject.notfound"));
            }

            var compositeObjectRelation = new List<CompositeObjectRelationDto>();
            compositeObjectRelation.AddRange(_dbContext.Modeling.Queryable<CompositeObjectRelation, BusinessObjectColumn, BusinessObject, BusinessObjectColumn>((a, b, c, d) => a.ParentBusinessObjectColumnId == b.Id && a.BusinessObjectId == c.Id && a.BusinessObjectColumnId == d.Id)
                .Where((a, b, c, d) => a.CompositeObjectId == id && a.ObjectType == "businessObject")
                .Select((a, b, c, d) => new CompositeObjectRelationDto
                {
                    Id = a.Id,
                    JoinRelation = a.JoinRelation,
                    JoinType = a.JoinType,
                    ParentId = a.ParentId,
                    CompositeObjectId = a.CompositeObjectId,
                    ParentBusinessObjectColumnId = a.ParentBusinessObjectColumnId,
                    ParentBusinessObjectColumnName = b.Name,
                    ParentBusinessObjectColumnDescription = b.Description,
                    BusinessObjectId = a.BusinessObjectId,
                    BusinessObjectName = c.Name,
                    BusinessObjectDescription = c.Description,
                    BusinessObjectColumnId = a.BusinessObjectColumnId,
                    BusinessObjectColumnName = d.Name,
                    BusinessObjectColumnDescription = d.Description,
                    ObjectType = a.ObjectType,
                    ExtraCondition = a.ExtraCondition
                }).ToList());
            compositeObjectRelation.AddRange(_dbContext.Modeling.Queryable<CompositeObjectRelation, BusinessObjectColumn, View, ViewColumns>((a, b, c, d) => a.ParentBusinessObjectColumnId == b.Id && a.BusinessObjectId == c.Id && a.BusinessObjectColumnId == d.Id)
                .Where((a, b, c, d) => a.CompositeObjectId == id && a.ObjectType == "view")
                .Select((a, b, c, d) => new CompositeObjectRelationDto
                {
                    Id = a.Id,
                    JoinRelation = a.JoinRelation,
                    JoinType = a.JoinType,
                    ParentId = a.ParentId,
                    CompositeObjectId = a.CompositeObjectId,
                    ParentBusinessObjectColumnId = a.ParentBusinessObjectColumnId,
                    ParentBusinessObjectColumnName = b.Name,
                    ParentBusinessObjectColumnDescription = b.Description,
                    BusinessObjectId = a.BusinessObjectId,
                    BusinessObjectName = c.Name,
                    BusinessObjectDescription = c.Description,
                    BusinessObjectColumnId = a.BusinessObjectColumnId,
                    BusinessObjectColumnName = d.BusinessObjectColumnAlias,
                    BusinessObjectColumnDescription = d.BusinessObjectColumnDescription,
                    ObjectType = a.ObjectType,
                    ExtraCondition = a.ExtraCondition
                }).ToList());
            item.CompositeObjectRelation = compositeObjectRelation;
            return item;
        }

        
        
        
        
        public void SaveCompositeObject(CompositeObjectDto dto)
        {
            var id = Guid.NewGuid();
            if (dto.Id == Guid.Empty)
            {
                var compositeObjects = _dbContext.Modeling.Queryable<CompositeObject>().Where(d => d.DataBaseId == dto.DataBaseId && d.Name == dto.Name).ToList();
                if (compositeObjects.Count > 0)
                {
                    throw new StatusNotFoundException("组合对象名称名称已存在");
                }

                var com = dto.MapTo<CompositeObject>();
                com.Id = id;
                com.IsDelete = false;
                var rel = dto.CompositeObjectRelation.MapTo<List<CompositeObjectRelation>>();
                rel.ForEach(f =>
                {
                    if (f.Id == Guid.Empty)
                    {
                        f.Id = Guid.NewGuid();
                    }

                    f.CompositeObjectId = com.Id;
                });
                _dbContext.Modeling.Insertable(com).ExecuteCommand();
                _dbContext.Modeling.Insertable(rel).ExecuteCommand();
            }
            else
            {
                var compositeObjects = _dbContext.Modeling.Queryable<CompositeObject>().Where(d => d.DataBaseId == dto.DataBaseId && d.Name == dto.Name && d.Id != dto.Id).ToList();
                if (compositeObjects.Count > 0)
                {
                    throw new StatusNotFoundException("组合对象名称名称已存在");
                }

                var com = dto.MapTo<CompositeObject>();
                com.IsDelete = false;
                _dbContext.Modeling.Updateable(com).ExecuteCommand();
                _dbContext.Modeling.Deleteable<CompositeObjectRelation>().Where(w => w.CompositeObjectId == dto.Id).ExecuteCommand();
                var insert = dto.CompositeObjectRelation.MapTo<List<CompositeObjectRelation>>();
                insert.ForEach(f =>
                {
                    f.CompositeObjectId = dto.Id;
                });
                if (insert != null && insert.Count > 0)
                {
                    _dbContext.Modeling.Insertable(insert).ExecuteCommand();
                }

                id = dto.Id;
            }

            _structureCacheService.SetRedis(new StructureCache.Dtos.StructureCacheSetDto()
            {
                BusinessObjectId = id,
                BusinessObjectType = 2
            });
        }

        
        
        
        
        public void DeleteCompositeObject(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<CompositeObject>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.compositeObject.notfound"));
            }

            item.IsDelete = true;
            _dbContext.Modeling.Updateable(item).ExecuteCommand();
        }

        
        
        
        
        
        public BOColumnDataDto GetCompositeObjectTreeList(Guid id)
        {
            CompositeObjectDto compositeObj = GetCompositeObject(id);

            List<BOColumnTreeDto> list = new List<BOColumnTreeDto>();
            BOColumnTreeDto mainTree = GetColumnTreeByObjectID(compositeObj.BusinessObjectId);
            list.Add(mainTree);
            List<CompositeObjectRelationDto> relList = compositeObj.CompositeObjectRelation;
            foreach (CompositeObjectRelationDto rel in relList)
            {
                BOColumnTreeDto relTree = GetColumnTreeByObjectID(rel.BusinessObjectId);
                list.Add(relTree);
            }

            BOColumnDataDto columnsInfo = new BOColumnDataDto();
            columnsInfo.ObjectColumns = list;
            columnsInfo.DataBaseName = compositeObj.DataBaseName;
            columnsInfo.ObjectName = compositeObj.BusinessObjectName;
            return columnsInfo;
        }

        
        
        
        
        
        public DataTable GetCompositeObjectDataList(BOColumnDataDto dto)
        {
            string cols = string.Empty;
            foreach (BOColumnTreeDto col in dto.ObjectColumns)
            {
                cols += col.Key.Replace("-", ".") + ",";
            }

            string sql = string.Format(@"SELECT {0} FROM [{1}] ", cols.TrimEnd(','), dto.ObjectName);
            string joinSql = CompositeRelationJoinSql(dto.CompositeObjectID);
            DataTable dt = _dbContext.Modeling.Ado.GetDataTable(sql + joinSql);
            return dt;
        }

        
        
        
        
        
        private string CompositeRelationJoinSql(string compositeObjectID)
        {
            string joinSql = @"select 'inner join ['+o2.Name+'] on ['+o2.Name+'].'+c2.Name+'=['+o1.Name+'].' +c1.Name from CompositeObjects c inner join CompositeObjectRelations r on c.id=r.CompositeObjectId
              inner join BusinessObjects o1 on c.BusinessObjectId=o1.Id
              inner join BusinessObjects o2 on r.BusinessObjectId=o2.Id
              inner join BusinessObjectColumns c1 on r.ParentBusinessObjectColumnId=c1.Id
              inner join BusinessObjectColumns c2 on r.BusinessObjectColumnId=c2.Id where c.id='" + compositeObjectID + "'";

            object sqlValue = _dbContext.Modeling.Ado.GetScalar(joinSql);
            return sqlValue != null ? sqlValue.ToString() : string.Empty;
        }

        
        
        
        
        
        private BOColumnTreeDto GetColumnTreeByObjectID(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((dt, db) => new JoinQueryInfos(JoinType.Inner, dt.DataBaseId == db.Id))
                     .Where((dt, db) => !dt.IsDelete.Value && dt.Id == id)
                      .Select((dt, db) => new ObjectDto
                      {
                          Id = dt.Id,
                          DataBaseId = dt.DataBaseId,
                          Name = dt.Name,
                          CreateDate = dt.CreateDate,
                          Description = dt.Description,
                          DataBaseName = db.Name,
                          DataBaseDescription = db.Description,
                          DataBaseType = db.Type,
                          IsCustom = dt.IsCustom,
                          IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                          State = dt.State,
                          IsTree = dt.IsTree,
                          IsLogicalDelete = dt.IsLogicalDelete
                      }).First();
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            List<BusinessObjectColumn> colList = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == id).OrderBy(a => a.Order).ToList();
            StringBuilder sb = new StringBuilder();

            List<BOColumnTreeDto> cTreeList = new List<BOColumnTreeDto>();
            foreach (BusinessObjectColumn col in colList)
            {
                BOColumnTreeDto bo = new BOColumnTreeDto();
                bo.Level = "2";
                bo.Title = string.IsNullOrEmpty(col.Description) ? col.Name : col.Description;
                bo.Key = "[" + item.Name + "]-" + col.Name;
                cTreeList.Add(bo);
            }

            BOColumnTreeDto pTree = new BOColumnTreeDto();
            pTree.Level = "1";
            pTree.Title = string.IsNullOrEmpty(item.Description) ? item.Name : item.Description;
            pTree.Key = item.Name;
            pTree.Children = cTreeList;
            return pTree;
        }
    }
}
