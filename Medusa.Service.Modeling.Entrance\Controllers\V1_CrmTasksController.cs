using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.ApplicationMgr.Dtos;
using Medusa.Service.Modeling.Application.CRMTasks;
using Medusa.Service.Modeling.Application.CRMTasks.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.LowCode;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// 任务接口
    /// </summary>
    [Route("v1/crmtasks")]
    [ApiExplorerSettings(GroupName = "tasks.v1")]
    public class V1_CrmTasksController : ProductControllerBase
    {
        #region //  服务注入
        readonly ITasksService _tasksService;
        readonly ICrmTasksService _crmTasksService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_CrmTasksController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public V1_CrmTasksController(IServiceProvider serviceProvider)
        {
            _tasksService = serviceProvider.GetService<ITasksService>();
            _crmTasksService = serviceProvider.GetService<ICrmTasksService>();
        }
        #endregion

        /// <summary>
        /// 任务模板分页查询
        /// </summary>
        /// <param name="dto">查询条件</param>
        /// <returns>ReturnTaskTemplateDto</returns>
        [HttpGet("searchtasktemplate")]
        public PageResult<ReturnTaskTemplateDto> SearchTaskTemplate([FromQuery] SearchTaskTemplateDto dto)
        {
            return _tasksService.SearchTaskTemplate(dto);
        }

        /// <summary>
        /// 新增任务模板
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost]
        public void AddTaskTemplate([FromBody] CrmtasktemplateDto dto)
        {
            _tasksService.AddTaskTemplate(dto);
        }

        /// <summary>
        /// 新增任务模板时获取任务项配置
        /// </summary>
        /// <returns>TaskConfigDto</returns>
        [HttpGet("configs")]
        public List<TaskConfigDto> GetTaskConfig()
        {
            return _tasksService.GetTaskConfig();
        }

        /// <summary>
        /// 修改任务模板
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPut]
        public void UpdateTaskTemplate([FromBody] CrmtasktemplateDto dto)
        {
            _tasksService.UpdateTaskTemplate(dto);
        }

        /// <summary>
        /// 任务模板应用历史分页查询
        /// </summary>
        /// <param name="dto">查询条件</param>
        /// <returns>Crmtaskapplicationhistory</returns>
        [HttpGet("searchtasktemplatehistory")]
        public PageResult<Crmtaskapplicationhistory> SearchTaskTemplateHistory([FromQuery] SearchTaskTemplateHistoryDto dto)
        {
            return _tasksService.SearchTaskTemplateHistory(dto);
        }

        /// <summary>
        /// 新增任务模板应用
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("AddTaskTemplateApplication")]
        public void AddTaskTemplateApplication([FromBody] CrmtasktemplateApplicationDto dto)
        {
            _tasksService.AddTaskTemplateApplication(dto);
        }

        /// <summary>
        /// 任务模板应用状态修改
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPut("ChangeTaskTemplateApplication")]
        public void ChangeTaskTemplateApplication([FromBody] ChangeTemplateApplicationDto dto)
        {
            _tasksService.ChangeTaskTemplateApplication(dto);
        }

        /// <summary>
        /// 新增任务模板应用时获取生效周期枚举
        /// </summary>
        /// <returns>CronConfigDto</returns>
        [HttpGet("cronConfigs")]
        public List<CronConfigDto> GetCronConfig()
        {
            return _tasksService.GetCronConfig();
        }

        /// <summary>
        /// 任务分页查询
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>Crmtask</returns>
        [HttpGet("searchtask")]
        public PageResult<Crmtask> SearchTask([FromQuery] SearchTaskDto dto)
        {
            return _tasksService.SearchTask(dto);
        }

        /// <summary>
        /// 各种拜访的保存方法，废弃，不存在这个入口了，所有的数据都是由：日报填写等做出来的
        /// </summary>
        /// <param name="data">data</param>
        [HttpPost("visit")]
        public void SaveVisit([FromBody] TaskVisitDto data)
        {
            _crmTasksService.SaveVisit(data);
        }

        /// <summary>
        /// 获取此任务下多少个配置项，供前台显示哪些需要展示的报表
        /// </summary>
        /// <param name="taskId">taskId</param>
        /// <returns>配置项</returns>
        [HttpGet("{id}/confignames")]
        public List<string> TaskConfigs([FromRoute(Name = "id")] Guid taskId)
        {
            return _crmTasksService.GetTaskConfigs(taskId);
        }

        /// <summary>
        /// 现场拜访统计列表
        /// </summary>
        /// <param name="taskQuery">查询条件</param>
        /// <returns>现场拜访统计</returns>
        [HttpGet("xianchang")]
        public CrmPageResult<XianChangVisit> GetXianChangVisits([FromQuery] TaskVisitQueryDto taskQuery)
        {
            return _crmTasksService.GetXianChangVisists(taskQuery);
        }

        /// <summary>
        /// 电话拜访统计列表
        /// </summary>
        /// <param name="taskQuery">查询条件</param>
        /// <returns>现场拜访统计</returns>
        [HttpGet("dianhua")]
        public CrmPageResult<DianHuaVisit> GetDianHuaVisists([FromQuery] TaskVisitQueryDto taskQuery)
        {
            return _crmTasksService.GetDianHuaVisists(taskQuery);
        }

        /// <summary>
        /// 满意度调查
        /// </summary>
        /// <param name="taskQuery">查询条件</param>
        /// <returns>现场拜访统计</returns>
        [HttpGet("satisfaction")]
        public CrmPageResult<SatisfactionVisitDto> GetSatisfactionisists([FromQuery] TaskVisitQueryDto taskQuery)
        {
            return _crmTasksService.GetSatisfactionVisits(taskQuery);
        }

        /// <summary>
        /// 满意度调查
        /// </summary>
        /// <param name="taskQuery">查询条件</param>
        /// <returns>现场拜访统计</returns>
        [HttpGet("saleclue")]
        public CrmPageResult<SaleClueVisitDto> GetSaleClue([FromQuery] TaskVisitQueryDto taskQuery)
        {
            return _crmTasksService.GetSaleClueVisits(taskQuery);
        }

        /// <summary>
        /// 新增crmtask
        /// </summary>
        /// <param name="templateId">id</param>
        /// <exception cref="Exception">ex</exception>
        [HttpPost("crm-task")]
        public void AddCrmTask([FromQuery(Name = "templateId")] Guid? templateId)
        {
            try
            {
                _tasksService.AddCrmTask(templateId);
            }
            catch (Exception ex)
            {
                throw new Exception($"新增crmtask错误======>{ex.Message}");
            }
        }
    }
}
