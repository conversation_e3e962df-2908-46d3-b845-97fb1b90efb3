using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.EOPMessage.Dto;
using Medusa.Service.Modeling.Application.HomePage.Dto;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.Boost;
using Medusa.Service.Modeling.Core.Entities.SWApp;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.Enums;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.HomePage
{
    
    
    
    public class HomePageService : ServiceBase, IHomePageService
    {
        private readonly MyDbContext _dbContext;
        readonly string setPath = string.Empty;

        
        
        
        
        public HomePageService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            setPath = appSettings["BusinessOrg"]?["SET"]?.ToString();
        }

        
        
        
        public MyDbContext P_DbContext => _dbContext;

        
        
        
        
        public List<DailyWorkReportDto> GetMySubmitted()
        {
            var userAccount = UserConstants.CurrentUser.Value.Account;
            var data = _dbContext.Modeling.Queryable<CRMDailyReport>()
              .Where(x => x.IsDelete == 0 && x.TBRCode == userAccount)
              .Select(j => new DailyWorkReportDto
              {
                  Id = j.ID,
                  ReportDate = j.ReportDate,
                  ReportName = j.IsOutID == "1" ? "出差报告" : "工作报告",
                  ReportUser = j.TBR,
                  IsRead = j.IsReadID == "1" ? true : false,
              })
              .OrderByDescending(x => x.ReportDate)
              .ToPageList(1, 5);
            return data;
        }

        
        
        
        
        public GetSubmittedToMeDto GetSubmittedToMe()
        {
            GetSubmittedToMeDto returnItem = new GetSubmittedToMeDto();
            returnItem.ReportList = new List<DailyWorkReportDto>();
            returnItem.Rates = new List<DailyWorkReportThreeDaysRateDto>();

            var userId = UserConstants.CurrentUser.Value.Id;
            var users = GetDirectSubordinateUser(userId);
            var data = _dbContext.Modeling.Queryable<CRMDailyReport>()
               .Where(x => x.IsDelete == 0 && users.Contains(x.TBRCode))
               .OrderByDescending(x => x.ReportDate).ToList();

            returnItem.ReportList = data.Select(j => new DailyWorkReportDto
            {
                Id = j.ID,
                ReportDate = j.ReportDate,
                ReportName = j.IsOutID == "1" ? "出差报告" : "工作报告",
                ReportUser = j.TBR,
                IsRead = j.IsReadID == "1",
            }).OrderBy(j => j.ReportDate).Take(5).ToList();

            var threeDays = data.Where(c => !string.IsNullOrEmpty(c.ReportDate) && Convert.ToDateTime(c.ReportDate).Date >= DateTime.Now.AddDays(-4).Date && Convert.ToDateTime(c.ReportDate).Date != DateTime.Now.Date).ToList();
            returnItem.Rates = threeDays.GroupBy(c => c.ReportDate).OrderBy(c => c.Key).Select(c => new DailyWorkReportThreeDaysRateDto
            {
                Id = Guid.NewGuid(),
                Title = c.Key,
                Value = (int)Math.Round(c.Select(p => p.TBRCode).Distinct().Count() / (decimal)users.Count * 100M, 0),
            }).ToList();

            return returnItem;
        }

        
        
        
        
        
        public List<string> GetDirectSubordinateUser(Guid userId)
        {
            var user = _dbContext.Boost.Queryable<Core.Entities.Boost.Users>()
                                .Where(a => a.UpperUserId == userId && a.Status == 1)
                                .Select(a => new
                                {
                                    a.UserLoginId,
                                }).ToList();

            return user.Select(a => a.UserLoginId).ToList();
        }

        
        
        
        
        
        public List<CustomerComplaintDto> GetCustomerComplaint(CustomerComplaintQueryDto queryDto)
        {
            var data = _dbContext.Modeling.Queryable<Crmcustomercomplaintamount, ProcessInstanceRecord, AfterSaleProductInfo>((c, p, a) => new object[]
            {
                JoinType.Inner, c.Id == p.DataId,
                JoinType.Inner, c.Id == a.Id
            })
             .Where((c, p, a) => c.IsDelete == 0 && p.ProcInstStatus == ProcInstStatusEnums.Approved.ToString())
             .WhereIF(queryDto.StartDate.HasValue && queryDto.EndDate.HasValue, c => c.CreateDate > queryDto.StartDate && c.CreateDate < queryDto.EndDate.Value.AddDays(1).AddSeconds(-1))
             .Select((c, p, a) => new
             {
                 c.CustomerName,
                 a.ProductType,
                 amount = Convert.ToInt32(a.Amount),
             }).MergeTable();
            var test = data.ToSqlString();
            var result = new List<CustomerComplaintDto>();

            if (queryDto.Type == "customer")
            {
                result = data.GroupBy(x => x.CustomerName).Select(x => new CustomerComplaintDto
                {
                    Name = x.CustomerName,
                    CustomerComplaintTotal = SqlFunc.AggregateCount(x.CustomerName),
                    CustomerComplaintAmount = SqlFunc.AggregateSum(x.amount)
                }).ToList();
            }
            else if (queryDto.Type == "product")
            {
                result = data.GroupBy(x => x.ProductType).Select(x => new CustomerComplaintDto
                {
                    Name = x.ProductType,
                    CustomerComplaintTotal = SqlFunc.AggregateCount(x.ProductType),
                    CustomerComplaintAmount = SqlFunc.AggregateSum(x.amount)
                }).ToList();
            }

            return result;
        }

        
        
        
        
        public bool CheckUser()
        {
            var userId = UserConstants.CurrentUser.Value.Id;
            var users = GetDirectSubordinateUser(userId);
            return users.Any();
        }
    }
}
