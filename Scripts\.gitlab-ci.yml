stages:
  - deploy

dev_deploy:
  stage: deploy
  only:
    - master
  script:
    - tar -cvzf /tmp/$PROJECT_NAME.tgz api
    - deploy_copy /tmp/$PROJECT_NAME.tgz /var/www/boost/$PROJECT_NAME
    - deploy_run "cd /var/www/boost/$PROJECT_NAME && tar -xvzf $PROJECT_NAME.tgz"
    - deploy_run "cd /var/www/boost/$PROJECT_NAME && rm $PROJECT_NAME.tgz"
    - deploy_run "rm -rf /var/www/boost/$PROJECT_NAME/src"
    - deploy_run "mv /var/www/boost/$PROJECT_NAME/api /var/www/boost/$PROJECT_NAME/src"
    - deploy_run "pm2 restart $PROJECT_NAME"
