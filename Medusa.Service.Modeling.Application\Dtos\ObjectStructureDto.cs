using System;

namespace Medusa.Service.Modeling.Application.Dtos
{
    
    
    
    public class ObjectStructureDto : ObjectDto
    {
        
        
        
        public bool IsMain { get; set; }

        
        
        
        public Guid MainDataTableColumnId { get; set; }

        
        
        
        public Guid DetailDataTableColumnId { get; set; }

        
        
        
        public int JoinType { get; set; }

        
        
        
        public int JoinRelation { get; set; }

        
        
        
        public Guid? CompositeObjectRelationId { get; set; }

        
        
        
        public Guid? ParentId { get; set; }

        
        
        
        public string ObjectType { get; set; }

        
        
        
        public object ExtraCondition { get; set; }
    }
}
