using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.SDK.Gateway;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.Users
{
    
    
    
    public class UserService : ServiceBase, IUserService
    {
        #region 

        
        
        
        
        
        public UserService(IServiceProvider serviceProvider)
        {
        }
        #endregion

        #region 

        
        
        
        
        
        public UserLoginDto Login(LoginModel model)
        {
            UserLoginDto dto = new UserLoginDto
            {
                Id = Guid.NewGuid(),
                Name = "管理员",
                Account = "admin",
                IsAdmin = true
            };
            return dto;
        }
        #endregion

    }
}
