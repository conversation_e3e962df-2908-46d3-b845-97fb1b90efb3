using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Medusa.Service.Modeling.Application.Dtos;

namespace Medusa.Service.Modeling.Application.PageModelingManage.Dtos
{
    
    
    
    public class PageModelingDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        [Required]
        public string Name { get; set; }

        
        
        
        [Required]
        public string PageDesgin<PERSON>son { get; set; }

        
        
        
        [Required]
        public int Type { get; set; }

        
        
        
        [Required]
        public int Status { get; set; }

        
        
        
        [Required]
        public int DataSourceType { get; set; }

        
        
        
        [Required]
        public string DataSource { get; set; }

        
        
        
        public DateTime? CreateDate { get; set; }

        
        
        
        public List<ObjectStructureDto> Tables { get; set; }

        
        
        
        public string ApplicationName { get; set; }

        
        
        
        public Guid ApplicationId { get; set; }

        
        
        
        public string ModuleName { get; set; }

        
        
        
        public Guid ModuleId { get; set; }

        
        
        
        public string ApplicationEquipment { get; set; }

        
        
        
        public int? Version { get; set; }

        
        
        
        public Guid? VersionId { get; set; }

        
        
        
        public string OptType { get; set; } = "save";

        
        
        
        public List<PageModelingVersionDto> Versions { get; set; }
    }
}
