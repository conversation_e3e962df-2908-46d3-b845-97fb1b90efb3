using System;

namespace Medusa.Service.Modeling.Core
{
    /// <summary>
    /// 头信息中的用户对象
    /// </summary>
    public class HeaderUser
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// 名字
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 英文名字
        /// </summary>
        public string EnglishName { get; set; }

        /// <summary>
        /// 系统语言
        /// </summary>
        public string Language { get; set; }
    }
}