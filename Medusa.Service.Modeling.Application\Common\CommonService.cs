using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Linq.Dynamic.Core;
using Medusa.Service.Modeling.Application.Common.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using Nacos.Utilities;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.Common
{
    
    
    
    public class CommonService : ServiceBase, ICommonService
    {
        #region 
        readonly MyDbContext _dbContext;
        private readonly ILogger _logger;

        
        
        
        
        
        public CommonService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _logger = serviceProvider.GetService<ILogger<CommonService>>();
        }

        #endregion

        
        
        
        
        
        public PageResult<dynamic> BusinessObjectData(SearchDto dto)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().Where(w => w.OnlyCode == dto.ObjectDataBaseCode).First();
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            if (string.IsNullOrEmpty(dto.Name))
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            
            
            
            
            
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            ISugarQueryable<ExpandoObject> query = client.Queryable(dto.Name, "Cust" + dto.Name);
            if (dto.Where != null && dto.Where.Count > 0)
            {
                var conditonModels = new List<IConditionalModel>();
                dto.Where.ForEach(f =>
                {
                    conditonModels.Add(new ConditionalModel()
                    {
                        FieldName = f.Name,
                        ConditionalType = !string.IsNullOrEmpty(f.ConditionalType) ? (ConditionalType)Enum.Parse(typeof(ConditionalType), f.ConditionalType) : ConditionalType.Equal,
                        FieldValue = f.Value.ToString()
                    });
                });
                conditonModels = conditonModels.FindAll(w => !string.IsNullOrEmpty(((ConditionalModel)w).FieldValue)).ToList();
                query.Where(conditonModels);
            }

            if (dto.OrderBy != null && dto.OrderBy.Count > 0)
            {
                var orderByModels = new List<OrderByModel>();
                dto.OrderBy.ForEach(f =>
                {
                    orderByModels.Add(new OrderByModel()
                    {
                        FieldName = f.Name,
                        OrderByType = !string.IsNullOrEmpty(f.OrderByType) ? (OrderByType)Enum.Parse(typeof(OrderByType), f.OrderByType) : OrderByType.Asc
                    });
                });
                query.OrderBy(orderByModels);
            }

            var result = new PageResult<dynamic> { Total = 0, Items = null };
            if (dto.IsAll)
            {
                var datas = query.ToList();
                result.Total = datas.Count;
                result.Items = datas.ToDynamicList();
            }
            else
            {
                var count = 0;
                var datas = query.ToPageList(dto.Page, dto.PageSize, ref count);
                result.Total = count;
                result.Items = datas.ToDynamicList();
            }

            _logger.LogInformation($"获取数据：{result.ToJsonString()}");
            return result;
        }

        
        
        
        
        
        public PageResult<dynamic> BusinessObjectDataForIntegrationCenter(SearchForIntegrationCenterDto dto)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().Where(w => w.OnlyCode == dto.ObjectDataBaseCode).First();
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            if (string.IsNullOrEmpty(dto.Name))
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            
            
            
            
            
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            ISugarQueryable<ExpandoObject> query = client.Queryable(dto.Name, "Cust" + dto.Name);
            if (dto.Where != null && dto.Where.Count > 0)
            {
                var conditonModels = new List<IConditionalModel>();
                foreach (var where in dto.Where)
                {
                    var czfIndex = where.Key.LastIndexOf("_czf");
                    var key = czfIndex > -1 ? where.Key.Substring(0, czfIndex) : where.Key;
                    var model = (ConditionalModel)conditonModels.Find(f => ((ConditionalModel)f).FieldName == key);
                    if (model == null)
                    {
                        model = new ConditionalModel()
                        {
                            FieldName = key,
                            ConditionalType = ConditionalType.Equal,
                            FieldValue = string.Empty
                        };
                        conditonModels.Add(model);
                    }

                    if (where.Key.EndsWith("_czf"))
                    {
                        model.ConditionalType = !string.IsNullOrEmpty(dto.Where[where.Key].ToString()) ? (ConditionalType)Enum.Parse(typeof(ConditionalType), dto.Where[where.Key].ToString()) : ConditionalType.Equal;
                    }
                    else
                    {
                        model.FieldValue = dto.Where[where.Key].ToString();
                    }
                }

                conditonModels = conditonModels.FindAll(w => !string.IsNullOrEmpty(((ConditionalModel)w).FieldValue)).ToList();
                query.Where(conditonModels);
            }

            if (dto.OrderBy != null && dto.OrderBy.Count > 0)
            {
                var orderByModels = new List<OrderByModel>();
                foreach (var orderBy in dto.OrderBy)
                {
                    var model = orderByModels.Find(f => f.FieldName == orderBy.Key);
                    if (model == null)
                    {
                        model = new OrderByModel()
                        {
                            FieldName = orderBy.Key,
                        };
                        orderByModels.Add(model);
                    }

                    model.OrderByType = !string.IsNullOrEmpty(dto.OrderBy[orderBy.Key].ToString()) ? (OrderByType)Enum.Parse(typeof(OrderByType), dto.OrderBy[orderBy.Key].ToString()) : OrderByType.Asc;
                }

                query.OrderBy(orderByModels);
            }

            var result = new PageResult<dynamic> { Total = 0, Items = null };
            if (dto.IsAll)
            {
                var datas = query.ToList();
                result.Total = datas.Count;
                result.Items = datas.ToDynamicList();
            }
            else
            {
                var count = 0;
                var datas = query.ToPageList(dto.Page, dto.PageSize, ref count);
                result.Total = count;
                result.Items = datas.ToDynamicList();
            }

            _logger.LogInformation($"集成中心获取数据：{result.ToJsonString()}");
            return result;
        }

        
        
        
        
        public void SaveBusinessObjectData(TableDto dto)
        {
            _logger.LogInformation($"外部调用数据：TableName:{dto.TableName},TablePrimaryKey:{dto.TablePrimaryKey},Data:{dto.Data.ToString()}");
            if (string.IsNullOrEmpty(dto.TableName))
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            var dataBase = _dbContext.Modeling.Queryable<DataBase>().Where(d => d.OnlyCode == "Default").First();
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var columns = _dbContext.Modeling.Queryable<BusinessObject, BusinessObjectColumn>((a, b) => a.Id == b.BusinessObjectId)
                .Where((a, b) => a.Name == dto.TableName)
                .Select((a, b) => b).ToList();
            if (columns == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            if (!client.DbMaintenance.IsAnyTable(dto.TableName, false))
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            if (string.IsNullOrEmpty(dto.TablePrimaryKey))
            {
                dto.TablePrimaryKey = "ID";
            }

            if (dto.Data != null)
            {
                try
                {
                    client.BeginTran();

                    var data = client.Utilities.DeserializeObject<DataTable>(dto.Data.ToString());
                    data.TableName = dto.TableName;
                    if (!data.Columns.Contains("ID"))
                    {
                        data.Columns.Add("ID");
                    }

                    if (!data.Columns.Contains("CreateDate"))
                    {
                        data.Columns.Add("CreateDate");
                    }

                    if (!data.Columns.Contains("ModifyDate"))
                    {
                        data.Columns.Add("ModifyDate");
                    }

                    if (!data.Columns.Contains("CreateUserId"))
                    {
                        data.Columns.Add("CreateUserId");
                    }

                    if (!data.Columns.Contains("ModifyUserId"))
                    {
                        data.Columns.Add("ModifyUserId");
                    }

                    foreach (DataRow dr in data.Rows)
                    {
                        if (dr["ID"] == null || string.IsNullOrEmpty(dr["ID"].ToString()))
                        {
                            dr["ID"] = Guid.NewGuid();
                        }

                        dr["CreateDate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        dr["ModifyDate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        dr["CreateUserId"] = "sys";
                        dr["ModifyUserId"] = "sys";
                        var numbers = columns.FindAll(w => w.DisplayType == "number");
                        numbers.ForEach(f =>
                        {
                            if (data.Columns.Contains(f.Name))
                            {
                                var value = dr[f.Name];
                                decimal num = 0;
                                if (value != null && !string.IsNullOrEmpty(value.ToString()) && decimal.TryParse(value.ToString(), out num))
                                {
                                    dr[f.Name] = num;
                                }
                                else
                                {
                                    dr[f.Name] = null;
                                }
                            }
                        });

                        var bools = columns.FindAll(w => w.DisplayType == "bool");
                        bools.ForEach(f =>
                        {
                            if (data.Columns.Contains(f.Name))
                            {
                                var value = dr[f.Name];
                                bool num = false;
                                if (value != null && !string.IsNullOrEmpty(value.ToString()) && bool.TryParse(value.ToString(), out num))
                                {
                                    dr[f.Name] = num ? 1 : 0;
                                }
                                else
                                {
                                    dr[f.Name] = null;
                                }
                            }
                        });
                    }

                    var x = client.Storageable(data).WhereColumns(new string[] { dto.TablePrimaryKey }).ToStorage();
                    x.AsInsertable.IgnoreColumns(new string[] { "ModifyDate", "ModifyUserId" }).ExecuteCommand();
                    var pars = new List<string> { "CreateDate", "CreateUserId" };
                    if (dto.TablePrimaryKey != "ID")
                    {
                        pars.Add("ID");
                    }

                    x.AsUpdateable.IgnoreColumns(pars.ToArray()).ExecuteCommand();
                    client.CommitTran();
                }
                catch (Exception ex)
                {
                    client.RollbackTran();
                    throw new StatusNotFoundException("数据存储异常:" + ex.Message);
                }
            }
        }

        
        
        
        public void SpecialMdmRecordInfo()
        {
            var dtList = new List<Dictionary<string, object>>();

            var list = _dbContext.Modeling.Queryable<dynamic>().AS("mdmrecordinfo").ToList();
            var groupList = list.Where(a => !a.IsDelete && a.isMDMData == "0").GroupBy(a => a.userID).Select(a => new { userID = a.Key, Items = a })
                                .Where(a => a.Items.Count() > 1)
                                .ToList();
            foreach (var groupItem in groupList)
            {
                List<dynamic> dynamicList = groupItem.Items.OrderByDescending(a => a.modifiedTime).ToList();
                dynamic dynamicFirst = dynamicList.First();
                DateTime dtFirst = DateTime.Parse(dynamicFirst.modifiedTime);
                DateTime dtOther = dtFirst.AddSeconds(-1);
                for (int i = 1; i < dynamicList.Count(); i++)
                {
                    var dt = new Dictionary<string, object>();
                    dt.Add("ID", dynamicList[i].ID);
                    dt.Add("IsDelete", 1);
                    dt.Add("stdIsDeleted", 1);
                    dt.Add("modifiedTime", dtOther);
                    dtList.Add(dt);
                }
            }

            if (dtList.Count > 0)
            {
                int result = _dbContext.Modeling.Updateable(dtList).AS("mdmrecordinfo").WhereColumns("ID").ExecuteCommand();
            }
        }

        
        
        
        public void SpecialPersonInCharge()
        {
            var insertList = new List<Dictionary<string, object>>();
            var updateList = new List<Dictionary<string, object>>();

            var orgList = _dbContext.Modeling.Queryable<dynamic>().AS("mdmorganization").Where("status=@status and (personInCharge is not null and personInCharge<>'')", new { status = 1 }).Select("personInCharge,oId,objectId,firstLevelOrganization").ToList();
            var groupOrgList = orgList.GroupBy(a => a.personInCharge).ToList();

            var recordList = _dbContext.Modeling.Queryable<dynamic>().AS("mdmrecordinfo").ToList();
            var employeeList = _dbContext.Modeling.Queryable<dynamic>().AS("mdmemployee").ToList();
            foreach (var groupOrg in groupOrgList)
            {
                var personInCharge = groupOrg.Key;

                foreach (var org in groupOrg.ToList())
                {
                    var recordItem = recordList.FirstOrDefault(a => a.userID == personInCharge && a.oIdDepartment == int.Parse(org.oId));
                    if (recordItem == null)
                    {
                        var bsRecordItem = recordList.FirstOrDefault(a => a.userID == personInCharge);
                        var employeeItem = employeeList.FirstOrDefault(a => a.userID == personInCharge);

                        
                        var dt = new Dictionary<string, object>();
                        dt.Add("ID", Guid.NewGuid());
                        dt.Add("userID", personInCharge);
                        dt.Add("oIdDepartment", org.oId);
                        if (employeeItem != null)
                        {
                            dt.Add("pObjectDataID", employeeItem.objectId);
                        }

                        if (bsRecordItem != null)
                        {
                            dt.Add("jobnumber", bsRecordItem.jobNumber);
                        }

                        dt.Add("startDate", "1900/1/1");
                        dt.Add("stopDate", "9999/12/31");
                        dt.Add("employType", "0");
                        dt.Add("serviceType", "0");
                        dt.Add("serviceStatus", "0");
                        dt.Add("approvalStatus", "4");
                        dt.Add("employmentForm", "ce697722-29ae-4e76-8c89-33d3214b8676");
                        dt.Add("isCharge", "1");
                        dt.Add("oIdOrganization", org.firstLevelOrganization);
                        dt.Add("objectId", Guid.NewGuid());
                        dt.Add("createdBy", "10000");
                        dt.Add("createdTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        dt.Add("CreateUserId", "10000");
                        dt.Add("CreateDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        dt.Add("modifiedBy", "10000");
                        dt.Add("modifiedTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        dt.Add("ModifyUserId", "10000");
                        dt.Add("ModifyDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        dt.Add("businessModifiedBy", "10000");
                        dt.Add("businessModifiedTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        dt.Add("employeeStatus", 3);
                        dt.Add("entryType", 1);
                        dt.Add("isCurrentRecord", 1);
                        dt.Add("IsDelete", 0);
                        dt.Add("stdIsDeleted", 0);
                        dt.Add("isMDMData", 2);
                        dt.Add("isMainJob", 0);
                        insertList.Add(dt);
                    }
                    else
                    {
                        
                        if (int.Parse(recordItem.isMDMData) == 2 && int.Parse(recordItem.isMainJob) == 0)
                        {
                            var dt = new Dictionary<string, object>();
                            dt.Add("ID", recordItem.ID);
                            dt.Add("IsDelete", 0);
                            dt.Add("stdIsDeleted", 0);
                            dt.Add("modifiedTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            updateList.Add(dt);
                        }
                    }
                }
            }

            var dataBase = _dbContext.Modeling.Queryable<DataBase>().Where(d => d.OnlyCode == "Default").First();
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            try
            {
                client.BeginTran();

                
                int dCount = client.Updateable<object>().AS("mdmrecordinfo").SetColumns("IsDelete", 0).SetColumns("stdIsDeleted", 0).SetColumns("modifiedTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")).Where("isMDMData=2 and isMainJob=0").ExecuteCommand();

                
                int iCount = client.Insertable(insertList).AS("mdmrecordinfo").ExecuteCommand();

                
                int uCount = client.Updateable(updateList).AS("mdmrecordinfo").WhereColumns("ID").ExecuteCommand();
                client.CommitTran();
            }
            catch (Exception ex)
            {
                client.RollbackTran();
                throw new StatusNotFoundException("数据存储异常:" + ex.Message);
            }
        }
    }
}
