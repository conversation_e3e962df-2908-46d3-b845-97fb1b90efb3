using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 流水号
    /// </summary>
    [EntityTable("SerialNumber")]
    public partial class SerialNumber
    {
        /// <summary>
        /// 流水号
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 流水长度
        /// </summary>
        public int NoLength { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreateDate { get; set; }
    }
}
