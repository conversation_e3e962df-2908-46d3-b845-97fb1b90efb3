using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.DynamicSql;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Application.ViewManage.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.Cache;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.ViewManage
{
    
    
    
    public class ViewManageService : ServiceBase, IViewManageService
    {
        #region 
        readonly MyDbContext _dbContext;
        readonly IDynamicSqlService _dynamicSqlService;
        readonly string _dbType;
        private readonly IStructureCacheService _structureCacheService;

        
        
        
        
        public ViewManageService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _dynamicSqlService = serviceProvider.GetService<IDynamicSqlService>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            _dbType = appSettings["Persistence"]?["DbType"]?.ToString();
            _structureCacheService = serviceProvider.GetService<IStructureCacheService>();
        }

        #endregion

        
        
        
        
        
        public PageResult<ViewDto> GetList(ViewQueryDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<View, ApplicationEntity, DataBase>((a, b, c) => new JoinQueryInfos(JoinType.Left, a.AppId == b.Id, JoinType.Inner, a.DatabaseId == c.Id))
                   .WhereIF(!string.IsNullOrEmpty(dto.Name), a => a.Name.Contains(dto.Name))
                   .WhereIF(!string.IsNullOrEmpty(dto.Description), a => a.Description.Contains(dto.Description))
                   .WhereIF(dto.Id.HasValue, a => a.Id == dto.Id)
                   .WhereIF(dto.ApplicationId.HasValue && dto.ApplicationId != Guid.Empty, a => SqlFunc.IsNullOrEmpty(a.AppId) || a.AppId == dto.ApplicationId)
                   .Where((a, b, c) => a.State != 0)
                   .OrderBy((a, b, c) => a.CreateDate, OrderByType.Desc)
                   .Select((a, b, c) => new ViewDto
                   {
                       Id = a.Id,
                       Name = a.Name,
                       Description = a.Description,
                       State = a.State,
                       AppId = a.AppId,
                       AppName = b.Name,
                       DatabaseId = a.DatabaseId,
                       DatabaseName = c.Name,
                       IsCustom = a.IsCustom
                   });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<ViewDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        
        public ViewDto GetView(Guid id)
        {
            View view = _dbContext.Modeling.Queryable<View>().InSingle(id);
            List<ViewRelations> viewRelations = _dbContext.Modeling.Queryable<ViewRelations>().Where(x => x.ViewId == id).OrderBy(x => x.OrderNumber).ToList();
            List<ViewFilters> viewFilters = _dbContext.Modeling.Queryable<ViewFilters>().Where(x => x.ViewId == id).OrderBy(x => x.OrderNumber).ToList();
            List<ViewColumns> viewColumns = _dbContext.Modeling.Queryable<ViewColumns>().Where(x => x.ViewId == id).OrderBy(x => x.OrderNumber).ToList();
            List<ViewOrders> viewOrders = _dbContext.Modeling.Queryable<ViewOrders>().Where(x => x.ViewId == id).OrderBy(x => x.OrderNumber).ToList();

            ViewDto dto = new ViewDto();
            dto.Id = view.Id;
            dto.Name = view.Name;
            dto.Description = view.Description;
            dto.AppId = view.AppId;
            dto.DatabaseId = view.DatabaseId;
            dto.GroupFilter = view.GroupFilter;
            dto.Relations = viewRelations;
            dto.Filters = viewFilters;
            dto.Columns = viewColumns;
            dto.Orders = viewOrders;
            return dto;
        }

        
        
        
        
        
        public List<ViewColumns> GetViewColumns(Guid id)
        {
            return _dbContext.Modeling.Queryable<ViewColumns>().Where(x => x.ViewId == id).OrderBy(x => x.OrderNumber).ToList();
        }

        
        
        
        
        public void SaveView(ViewDto dto)
        {
            dto.Name = "View" + dto.Name;
            bool isEdit = dto.Id != Guid.Empty;
            View view = isEdit ? _dbContext.Modeling.Queryable<View>().InSingle(dto.Id) : new View();
            if (!isEdit)
            {
                if (_dbContext.Modeling.Queryable<View>().Any(x => x.Name == dto.Name))
                {
                    throw new Exception($"视图\"{dto.Name}\"已经存在！");
                }
            }

            view.Id = isEdit ? view.Id : Guid.NewGuid();
            view.Name = dto.Name;
            view.Description = dto.Description;
            view.AppId = dto.AppId;
            view.DatabaseId = dto.DatabaseId;
            view.GroupFilter = dto.GroupFilter;
            view.IsCustom = true;
            if (isEdit)
            {
                _dbContext.Modeling.Updateable(view).ExecuteCommand();
            }
            else
            {
                view.State = 1;
                view.CreateDate = DateTime.Now;
                _dbContext.Modeling.Insertable(view).ExecuteCommand();
            }

            if (isEdit)
            {
                _dbContext.Modeling.Deleteable<ViewRelations>(x => x.ViewId == view.Id).ExecuteCommand();
                _dbContext.Modeling.Deleteable<ViewFilters>(x => x.ViewId == view.Id).ExecuteCommand();
                _dbContext.Modeling.Deleteable<ViewOrders>(x => x.ViewId == view.Id).ExecuteCommand();
            }

            if (dto.Relations.Count > 0)
            {
                dto.Relations.ForEach(x =>
                {
                    x.Id = Guid.NewGuid();
                    x.ViewId = view.Id;
                });
                _dbContext.Modeling.Insertable(dto.Relations).ExecuteCommand();
            }

            if (dto.Filters.Count > 0)
            {
                dto.Filters.ForEach(x =>
                {
                    x.Id = Guid.NewGuid();
                    x.ViewId = view.Id;
                });
                _dbContext.Modeling.Insertable(dto.Filters).ExecuteCommand();
            }

            if (dto.Columns.Count > 0)
            {
                List<Guid> updateIdList = new List<Guid>();
                var dbColumns = _dbContext.Modeling.Queryable<ViewColumns>().Where(x => x.ViewId == view.Id).OrderBy(x => x.OrderNumber).ToList();
                dto.Columns.ForEach(x =>
                {
                    string type = "text";
                    if (x.BusinessObjectColumnId.HasValue)
                    {
                        var column = _dbContext.Modeling.Queryable<BusinessObjectColumn>().InSingle(x.BusinessObjectColumnId);
                        if (column != null)
                        {
                            type = column.DisplayType;
                        }
                    }

                    x.ViewId = view.Id;
                    x.DisplayType = type;
                    if (x.Id == Guid.Empty)
                    {
                        x.Id = Guid.NewGuid();
                        _dbContext.Modeling.Insertable(x).ExecuteCommand();
                    }
                    else
                    {
                        if (dbColumns.Any(m => m.Id == x.Id))
                        {
                            updateIdList.Add(x.Id);
                            _dbContext.Modeling.Updateable(x).ExecuteCommand();
                        }
                    }
                });

                var deleteColumns = dbColumns.Where(x => !updateIdList.Contains(x.Id)).ToList();
                if (deleteColumns != null && deleteColumns.Count > 0)
                {
                    _dbContext.Modeling.Deleteable(deleteColumns).ExecuteCommand();
                }
            }

            if (dto.Orders.Count > 0)
            {
                dto.Orders.ForEach(x =>
                {
                    x.Id = Guid.NewGuid();
                    x.ViewId = view.Id;
                });
                _dbContext.Modeling.Insertable(dto.Orders).ExecuteCommand();
            }

            _dynamicSqlService.InitView(_dbType, dto);

            _structureCacheService.SetRedis(new StructureCache.Dtos.StructureCacheSetDto()
            {
                BusinessObjectId = view.Id,
                BusinessObjectType = 3
            });
        }

        
        
        
        
        public void DeleteView(Guid id)
        {
            _dbContext.Modeling.Updateable<View>()
                .SetColumns(w => new View()
                {
                    State = 0,
                })
                .Where(w => w.Id == id).ExecuteCommand();
        }

        
        
        
        
        public void PublishView(Guid id)
        {
            _dbContext.Modeling.Updateable<View>()
                .SetColumns(w => new View()
                {
                    State = 2,
                })
                .Where(w => w.Id == id).ExecuteCommand();
        }

        
        
        
        
        
        public PageResult<ViewForCompositeDto> GetListForComposite(ViewQueryForCompositeDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<View, DataBase, ApplicationEntity>((v, db, ae) => new JoinQueryInfos(JoinType.Inner, v.DatabaseId == db.Id, JoinType.Left, v.AppId == ae.Id))
                .Where((v, db) => v.State == 2)
                .WhereIF(!string.IsNullOrEmpty(dto.Name), (v, db, ae) => v.Name.Contains(dto.Name))
                .WhereIF(!string.IsNullOrEmpty(dto.DataBaseName), (v, db, ae) => db.Name.Contains(dto.DataBaseName))
                .WhereIF(!string.IsNullOrEmpty(dto.DataBaseType), (v, db, ae) => db.Type == dto.DataBaseType)
                .WhereIF(dto.DataBaseId.HasValue, (v, db, ae) => db.Id == dto.DataBaseId)
                .WhereIF(dto.ApplicationId.HasValue, (v, db, ae) => SqlFunc.IsNullOrEmpty(v.AppId) || v.AppId == dto.ApplicationId)
                .OrderBy((v, db, ae) => v.CreateDate, OrderByType.Desc)
                .Select((v, db, ae) => new ViewForCompositeDto
                {
                    Id = v.Id,
                    DataBaseId = v.DatabaseId,
                    Name = v.Name,
                    CreateDate = v.CreateDate,
                    Description = v.Description,
                    DataBaseName = db.Name,
                    DataBaseDescription = db.Description,
                    DataBaseType = db.Type,
                    IsCustom = false,
                    IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                    State = 2,
                    IsTree = false,
                    IsLogicalDelete = false,
                    ApplicationId = SqlFunc.ToString(v.AppId),
                    ApplicationName = ae.Name
                });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            if (dto.HasColumns.HasValue && dto.HasColumns.Value)
            {
                items.ForEach(f =>
                {
                    f.Columns = _dbContext.Modeling.Queryable<ViewColumns>().Where(a => a.ViewId == f.Id)
                    .OrderBy(a => a.OrderNumber)
                    .Select(a => new ViewForCompositeColumnDto
                    {
                        Id = a.Id,
                        Name = a.BusinessObjectColumnAlias,
                        Description = a.BusinessObjectColumnDescription,
                        DisplayType = a.DisplayType,
                        IsPrimaryKey = a.IsPrimaryKey == 1,
                        IsSystemColumn = false,
                    }).ToList();
                });
            }

            return new PageResult<ViewForCompositeDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        public JArray GetDictionarCascaderyTree()
        {
            var list = _dbContext.Modeling.Queryable<Dictionary>().Where(x => x.Status == 1).OrderBy(x => x.OrderNum).ToList();
            JArray jResult = new JArray();
            foreach (var item in list.Where(x => string.IsNullOrEmpty(x.TypeCode)))
            {
                JObject jRootItem = new JObject();
                jRootItem["value"] = item.Code;
                jRootItem["label"] = item.Name;
                JArray jChildren = new JArray();
                foreach (var subItem in list.Where(x => x.TypeCode == item.Code))
                {
                    JObject jItem = new JObject();
                    jItem["value"] = subItem.Code;
                    jItem["label"] = subItem.Name;

                    JArray jLastChildren = new JArray();

                    JObject jLastItem1 = new JObject();
                    jLastItem1["value"] = "Code";
                    jLastItem1["label"] = "编码";
                    jLastChildren.Add(jLastItem1);

                    JObject jLastItem2 = new JObject();
                    jLastItem2["value"] = "Name";
                    jLastItem2["label"] = "名称";
                    jLastChildren.Add(jLastItem2);

                    JObject jLastItem3 = new JObject();
                    jLastItem3["value"] = "Value";
                    jLastItem3["label"] = "值";
                    jLastChildren.Add(jLastItem3);
                    jItem["children"] = jLastChildren;

                    jChildren.Add(jItem);
                }

                jRootItem["children"] = jChildren;
                jResult.Add(jRootItem);
            }

            return jResult;
        }
    }
}
