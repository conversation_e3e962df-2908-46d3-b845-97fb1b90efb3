using Medusa.Service.Modeling.Application.Authority.Dtos;
using Medusa.Service.Modeling.Core.Entity;

namespace Medusa.Service.Modeling.Application.Authority.Profile
{
    
    
    
    public class AuthorityProfile : AutoMapper.Profile
    {
        
        
        
        public AuthorityProfile()
        {
            CreateMap<BusinessSystem, BusinessSysDto>();
            CreateMap<BusinessSysDto, BusinessSystem>();
        }
    }
}
