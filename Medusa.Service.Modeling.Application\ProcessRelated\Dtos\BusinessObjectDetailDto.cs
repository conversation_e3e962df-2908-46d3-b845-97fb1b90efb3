using System;
using System.Collections.Generic;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class BusinessObjectDetailDto
    {
        
        
        
        public Guid? Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Code { get; set; }

        
        
        
        public int? Status { get; set; }

        
        
        
        public string Version { get; set; }

        
        
        
        public Guid? BusinessTypeId { get; set; }

        
        
        
        public string BusinessTypeName { get; set; }

        
        
        
        public string OutsideBusinessObjectInfo { get; set; }

        
        
        
        public bool? IsOutsideBusinessObject { get; set; }

        
        
        
        public List<BusinessObjectFieldDto> Fields { get; set; }
    }
}
