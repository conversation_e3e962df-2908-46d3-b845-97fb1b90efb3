using System;
using System.Collections.Generic;
using System.Text;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 样品送样
    /// </summary>
    [EntityTable("crmsamplerecords")]
    public class CRMSampleRecords
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 内部对接人用户名
        /// </summary>
        public string Contracts { get; set; }

        /// <summary>
        /// 已满足Spec
        /// </summary>
        public string SatisfySpec { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 是否进行下一步跟进
        /// </summary>
        public string IsContinue { get; set; }

        /// <summary>
        /// 对标状态编码
        /// </summary>
        public string Standard { get; set; }

        /// <summary>
        /// 项目类别编码
        /// </summary>
        public string ProjectCategory { get; set; }

        /// <summary>
        /// 客户测试预计结束时间
        /// </summary>
        public string TestEndTime { get; set; }

        /// <summary>
        /// 重要程度
        /// </summary>
        public string ImportanceName { get; set; }

        /// <summary>
        /// 样品需求人
        /// </summary>
        public string SampleDemanderName { get; set; }

        /// <summary>
        /// 对标状态
        /// </summary>
        public string StandardName { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 送样目的
        /// </summary>
        public string Purpose { get; set; }

        /// <summary>
        /// 快递单号
        /// </summary>
        public string ExpressNumber { get; set; }

        /// <summary>
        /// 客户Spec测定方法
        /// </summary>
        public string SpecMethod { get; set; }

        /// <summary>
        /// 送样状态
        /// </summary>
        public string StatusName { get; set; }

        /// <summary>
        /// 产品大类编码
        /// </summary>
        public string ProductMainType { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 样品需求人用户名
        /// </summary>
        public string SampleDemander { get; set; }

        /// <summary>
        /// 项目类别
        /// </summary>
        public string ProjectCategoryName { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 客户测试进箱时间
        /// </summary>
        public string TestTime { get; set; }

        /// <summary>
        /// 研发担当用户名
        /// </summary>
        public string DevUserId { get; set; }

        /// <summary>
        /// 送样状态编码
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 研发担当
        /// </summary>
        public string DevUserName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 送样担当对象
        /// </summary>
        public string SampleUserObject { get; set; }

        /// <summary>
        /// 样品名称
        /// </summary>
        public string SampleName { get; set; }

        /// <summary>
        /// 未满足Spec
        /// </summary>
        public string DisSatisfySpec { get; set; }

        /// <summary>
        /// 送货方式编码
        /// </summary>
        public string Delivery { get; set; }

        /// <summary>
        /// 内部对接人
        /// </summary>
        public string ContractsName { get; set; }

        /// <summary>
        /// 销售线索Id
        /// </summary>
        public string SalesClueId { get; set; }

        /// <summary>
        /// 样品单编号
        /// </summary>
        public string SampleNo { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 销售线索
        /// </summary>
        public string SalesClueName { get; set; }

        /// <summary>
        /// 收件人
        /// </summary>
        public string Receiver { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; }

        /// <summary>
        /// 送样日期
        /// </summary>
        public string SendDate { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string Specifications { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 客户要求Spec
        /// </summary>
        public string CustomerSpec { get; set; }

        /// <summary>
        /// 送样结果
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 内部对接人对象
        /// </summary>
        public string ContractsObject { get; set; }

        /// <summary>
        /// 产品大类
        /// </summary>
        public string ProductMainTypeName { get; set; }

        /// <summary>
        /// 重要程度编码
        /// </summary>
        public string Importance { get; set; }

        /// <summary>
        /// 样品需求人
        /// </summary>
        public string SampleDemanderObject { get; set; }

        /// <summary>
        /// 送样担当用户名
        /// </summary>
        public string SampleUserId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 研发担当对象
        /// </summary>
        public string DevObject { get; set; }

        /// <summary>
        /// 送货方式
        /// </summary>
        public string DeliveryName { get; set; }

        /// <summary>
        /// 送样担当
        /// </summary>
        public string SampleUserName { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 是否进行下一步跟进编号
        /// </summary>
        public string IsContinueCode { get; set; }

        /// <summary>
        /// 事业部标识
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否推送
        /// </summary>
        public int? IsSend { get; set; }
    }
}
