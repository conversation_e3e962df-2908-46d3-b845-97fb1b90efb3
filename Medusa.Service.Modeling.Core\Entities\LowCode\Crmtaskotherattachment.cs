using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// 其他任务附件
    /// </summary>
    [SugarTable("crmtaskotherattachment")]
    public class Crmtaskotherattachment
    {
        /// <summary>
        /// 附件ID
        /// </summary>
        [SugarColumn(ColumnName = "attachmentId", IsPrimaryKey = true)]
        public Guid AttachmentId { get; set; }

        /// <summary>
        /// 任务主键ID
        /// </summary>
        [SugarColumn(ColumnName = "taskId")]
        public Guid TaskId { get; set; }

        /// <summary>
        /// 附件名称
        /// </summary>
        [SugarColumn(ColumnName = "attachmentName")]
        public string AttachmentName { get; set; }

        /// <summary>
        /// 附件格式
        /// </summary>
        [SugarColumn(ColumnName = "attachmentFormat")]
        public string AttachmentFormat { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        [SugarColumn(ColumnName = "fileSize")]
        public string FileSize { get; set; }

        /// <summary>
        /// 附件地址
        /// </summary>
        [SugarColumn(ColumnName = "attachmentUrl")]
        public string AttachmentUrl { get; set; }

        /// <summary>
        /// 是否删除 1已删除,0未删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [SugarColumn(ColumnName = "createdUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "createdDate")]
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// 修改用户ID
        /// </summary>
        [SugarColumn(ColumnName = "modifiedUserId")]
        public string ModifiedUserId { get; set; }
    }
}
