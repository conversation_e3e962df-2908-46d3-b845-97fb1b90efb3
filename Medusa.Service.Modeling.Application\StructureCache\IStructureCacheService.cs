using Medusa.Service.Modeling.Application.StructureCache.Dtos;

namespace Medusa.Service.Modeling.Application.StructureCache
{
    
    
    
    public interface IStructureCacheService : IServiceBase
    {
        
        
        
        
        void SetRedis(StructureCacheSetDto dto);

        
        
        
        
        void SetAllRedis(StructureCacheSetDto dto);

        
        
        
        
        
        string GetRedis(StructureCacheSetDto dto);
    }
}
