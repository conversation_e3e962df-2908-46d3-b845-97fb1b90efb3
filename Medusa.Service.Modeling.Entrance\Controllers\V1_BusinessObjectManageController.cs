using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.BusinessObjectManage;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// BusinessObjects controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "BusinessObjectManage.v1")]
    public class V1_BusinessObjectManageController : ProductControllerBase
    {
        #region //  服务注入
        readonly IBusinessObjectManageService _businessObjectManageService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_BusinessObjectManageController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="businessObjectManageService">businessObjectManageService</param>
        public V1_BusinessObjectManageController(IBusinessObjectManageService businessObjectManageService)
        {
            _businessObjectManageService = businessObjectManageService;
        }
        #endregion

        /// <summary>
        /// 获取业务对象信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("business-objects")]
        public PageResult<ObjectDto> GetBusinessObjects([FromQuery] BusinessObjectQueryDto dto)
        {
            return _businessObjectManageService.GetBusinessObjects(dto);
        }

        /// <summary>
        /// 获取业务对象信息
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpGet("business-objects/{id}")]
        public ObjectDto GetBusinessObject([FromRoute] Guid id)
        {
            return _businessObjectManageService.GetBusinessObject(id);
        }

        /// <summary>
        /// 新增/修改业务对象信息
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("business-objects/save")]
        [ModelFieldCheck]
        public void SaveBusinessObject([FromBody] ObjectDto dto)
        {
            _businessObjectManageService.SaveBusinessObject(dto);
        }

        /// <summary>
        /// 业务对象删除
        /// </summary>
        /// <param name="id">id</param>
        [HttpPost("business-objects/{id}/delete")]
        public void DeleteBusinessObject([FromRoute] Guid id)
        {
            _businessObjectManageService.DeleteBusinessObject(id);
        }

        /// <summary>
        /// 业务对象发布
        /// </summary>
        /// <param name="id">id</param>
        [HttpPost("business-objects/{id}/publish")]
        public void PublishBusinessObject([FromRoute] Guid id)
        {
            _businessObjectManageService.PublishBusinessObject(id);
        }

        /// <summary>
        /// 获取业务对象表字段树
        /// </summary>
        /// <param name="id">业务对象ID</param>
        /// <returns>数据列表</returns>
        [HttpGet("business-objects/{id}/query")]
        public BOColumnDataDto GetBusinessObjectDataTree([FromRoute] Guid id)
        {
            return _businessObjectManageService.GetBusinessObjectTreeList(id);
        }

        /// <summary>
        /// 业务对象数据查询
        /// </summary>
        /// <param name="dto">业务对象数据查询dto</param>
        /// <returns>数据列表</returns>
        [HttpPost("business-objects/GetBusinessObjectDataList")]
        public DataTable GetBusinessObjectDataList([FromBody] BOColumnDataDto dto)
        {
            DataTable dt = _businessObjectManageService.GetBusinessObjectDataList(dto);
            return dt;
        }

        /// <summary>
        /// 更新业务对象数据
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("business-objects/UpdateBusinessObjectData")]
        public void UpdateBusinessObjectData([FromBody]List<BOColumnValueDto> dto)
        {
            _businessObjectManageService.UpdateBusinessObjectData(dto);
        }

        /// <summary>
        /// 获取业务对象树信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("business-objects/tree")]

        public PageResult<ExpandoObject> GetBusinessObjectTree([FromBody] BusinessObjectTreeQuery dto)
        {
           return _businessObjectManageService.GetBusinessObjectTree(dto);
        }

        /// <summary>
        /// 获取业务对象数据
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("business-objects/{id}/data")]
        public PageResult<ExpandoObject> GetBusinessObjectData([FromRoute]Guid id, [FromQuery] BusinessObjectCommonQueryDto dto)
        {
            return _businessObjectManageService.GetBusinessObjectData(id, dto);
        }

        /// <summary>
        /// 获取业务对象数据数量
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("business-objects/{id}/data-count")]
        public int GetBusinessObjectDataCount([FromRoute] Guid id, [FromQuery] BusinessObjectCommonQueryDto dto)
        {
            return _businessObjectManageService.GetBusinessObjectDataCount(id, dto);
        }

        /// <summary>
        /// 校验重复业务对象数据
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("business-objects/{id}/check-repeat")]
        public bool CheckRepeatBusinessObjectData([FromRoute] Guid id, [FromQuery] BusinessObjectCommonCheckDto dto)
        {
            return _businessObjectManageService.CheckRepeatBusinessObjectData(id, dto);
        }
    }
}
