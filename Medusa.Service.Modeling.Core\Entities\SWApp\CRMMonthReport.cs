using System;
using System.Collections.Generic;
using System.Text;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 日报
    /// </summary>
    [EntityTable("crmmonthlyreport")]
    public class CRMMonthReport
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 汇报周期-开始
        /// </summary>
        public string StartTime { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 汇报周期-结束
        /// </summary>
        public string EndTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 创建用户姓名
        /// </summary>
        public string CreateName { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否推送
        /// </summary>
        public int? IsSend { get; set; }
    }
}
