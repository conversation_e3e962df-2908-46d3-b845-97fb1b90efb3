using System.Collections.Generic;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using MT.Enterprise.Core.Middlewares.UserState;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Medusa.Service.Modeling.Entrance.Development
{
    /// <summary>
    /// 头信息 User 状态
    /// </summary>
    public class CustomHeaderFilter : IOperationFilter
    {
        /// <summary>
        /// Apply
        /// </summary>
        /// <param name="operation">operation</param>
        /// <param name="context">上下文</param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation.Parameters == null)
            {
                operation.Parameters = new List<OpenApiParameter>();
            }

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = UserConstants.HeaderUser,
                In = ParameterLocation.Header,
                Description = "当前用户 json&base64 字符串\n默认值：{\"id\":\"e8575564-17e8-4d9a-8ab3-e055c366dc62\",\"name\":\"管理员\",\"account\":\"admin\",\"language\":\"zh\"}",
                Schema = new OpenApiSchema { Type = "string", Default = new OpenApiString("eyJpZCI6ImU4NTc1NTY0LTE3ZTgtNGQ5YS04YWIzLWUwNTVjMzY2ZGM2MiIsIm5hbWUiOiLnrqHnkIblkZgiLCJhY2NvdW50IjoiYWRtaW4iLCJsYW5ndWFnZSI6InpoIn0=") }
            });
        }
    }
}
