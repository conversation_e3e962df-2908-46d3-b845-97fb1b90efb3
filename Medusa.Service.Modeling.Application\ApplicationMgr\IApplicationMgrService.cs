using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.ApplicationMgr.Dtos;

namespace Medusa.Service.Modeling.Application.ApplicationMgr
{
    
    
    
    public interface IApplicationMgrService : IServiceBase
    {
        
        
        
        
        
        List<ApplicationDto> GetApplications(ApplicationQueryDto dto);

        
        
        
        
        
        ApplicationDto GetApplication(Guid id);

        
        
        
        
        void DeleteApplication(Guid id);

        
        
        
        
        void AddApplication(ApplicationDto dto);

        
        
        
        
        
        void UpdateApplication(Guid id, ApplicationDto dto);
    }
}
