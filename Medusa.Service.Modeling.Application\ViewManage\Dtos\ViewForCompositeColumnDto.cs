using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ViewManage.Dtos
{
    
    
    
    public class ViewForCompositeColumnDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public bool? IsPrimaryKey { get; set; }

        
        
        
        public string DisplayType { get; set; }

        
        
        
        public bool IsSystemColumn { get; set; }
    }
}
