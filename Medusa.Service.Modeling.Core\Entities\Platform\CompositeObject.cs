using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 组合对象
    /// </summary>
    [EntityTable("CompositeObjects")]
    public partial class CompositeObject
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 组合对象名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 数据库Id
        /// </summary>
        public Guid DataBaseId { get; set; }

        /// <summary>
        /// 业务对象ID
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool? IsDelete { get; set; }

        /// <summary>
        /// ApplicationId
        /// </summary>
        public string ApplicationId { get; set; }

        /// <summary>
        /// ApplicationName
        /// </summary>
        public string ApplicationName { get; set; }
    }
}
