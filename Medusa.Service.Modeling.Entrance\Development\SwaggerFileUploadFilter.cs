using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Medusa.Service.Modeling.Entrance.Development
{
    /// <summary>
    /// 为文档添加上传控件
    /// </summary>
    public class SwaggerFileUploadFilter : IOperationFilter
    {
        /// <summary>
        /// Apply the specified operation and context.
        /// </summary>
        /// <param name="operation">Operation.</param>
        /// <param name="context">Context.</param>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            context.ApiDescription.TryGetMethodInfo(out MethodInfo methodInfo);

            if (methodInfo == null)
            {
                return;
            }

            var isUploadFile = methodInfo.GetCustomAttributes(false).OfType<SwaggerFileUploadAttribute>().Any();

            if (isUploadFile)
            {
                operation.RequestBody = new OpenApiRequestBody
                {
                    Content =
                    {
                        ["multipart/form-data"] = new OpenApiMediaType
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties =
                                {
                                    ["files"] = new OpenApiSchema
                                    {
                                        Type = "array",
                                        Description = "上传的文件",
                                        Items = new OpenApiSchema
                                        {
                                            Type = "string",
                                            Format = "binary"
                                        }
                                    }
                                },
                                Required = new HashSet<string>
                                {
                                    "files"
                                }
                            }
                        }
                    }
                };
            }
        }
    }
}
