using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.Authority.Dtos;

namespace Medusa.Service.Modeling.Application.Authority
{
    
    
    
    public interface IAuthorityService : IServiceBase
    {
        
        
        
        
        
        PageResult<BusinessSysDto> GetBusinessSystems(BusinessSysQueryDto dto);

        
        
        
        
        void SaveBusinessSystems(BusinessSysDto dto);

        
        
        
        
        void DeleteBusinessSystems(Guid id);

        
        
        
        
        
        List<BusinessObjectItemDto> GetBusinessObjectAuthority(Guid id);

        
        
        
        
        
        void SaveBusinessObjectAuthority(Guid id, List<BusinessObjectItemDto> dto);

        
        
        
        
        
        List<BusinessObjectItemDto> GetBusinessObjects(BusinessObjectQueryDto dto);
    }
}
