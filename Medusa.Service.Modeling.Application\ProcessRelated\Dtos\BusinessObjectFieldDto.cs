using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class BusinessObjectFieldDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Code { get; set; }

        
        
        
        public string Type { get; set; }

        
        
        
        public string DefaultValue { get; set; }

        
        
        
        public int? Order { get; set; }

        
        
        
        public string OutsideBusinessObjectInfo { get; set; }

        
        
        
        public bool? IsOutsideBusinessObject { get; set; }

        
        
        
        public List<BusinessObjectFieldDto> Children { get; set; }
    }
}
