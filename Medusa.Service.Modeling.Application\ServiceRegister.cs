using Medusa.Service.Modeling.Application.AppConfig;
using Medusa.Service.Modeling.Application.ApplicationMgr;
using Medusa.Service.Modeling.Application.Authority;
using Medusa.Service.Modeling.Application.BusinessObjectManage;
using Medusa.Service.Modeling.Application.Common;
using Medusa.Service.Modeling.Application.CompositeObjectManage;
using Medusa.Service.Modeling.Application.CRMReport;
using Medusa.Service.Modeling.Application.CRMTasks;
using Medusa.Service.Modeling.Application.DataBaseManage;
using Medusa.Service.Modeling.Application.DictionaryMgr;
using Medusa.Service.Modeling.Application.DynamicSql;
using Medusa.Service.Modeling.Application.HomePage;
using Medusa.Service.Modeling.Application.ModuleMgr;
using Medusa.Service.Modeling.Application.OperationLog;
using Medusa.Service.Modeling.Application.PageModelingManage;
using Medusa.Service.Modeling.Application.PageView;
using Medusa.Service.Modeling.Application.ProcessRelated;
using Medusa.Service.Modeling.Application.ProductRegistration;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Application.Users;
using Medusa.Service.Modeling.Application.ViewManage;
using Medusa.Service.Modeling.Core;
using Microsoft.Extensions.DependencyInjection;
using MMedusa.Service.Modeling.Application.EOPMessage;
using MMedusa.Service.Modeling.Application.TrainUser;

namespace Medusa.Service.Modeling.Application
{
    
    
    
    public static class ServiceRegister
    {
        
        
        
        
        public static void AddApplicationServices(this IServiceCollection services)
        {
            services.AddSingleton<MyDbContext>();
            services.AddSingleton<IAppConfigService, AppConfigService>();
            services.AddSingleton<IProductRegistrationService, ProductRegistrationService>();
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IDataBaseManageService, DataBaseManageService>();
            services.AddSingleton<IPageModelingManageService, PageModelingManageService>();
            services.AddSingleton<IModuleMgrService, ModuleMgrService>();
            services.AddSingleton<IAuthorityService, AuthorityService>();
            services.AddSingleton<IProcessRelatedService, ProcessRelatedService>();
            services.AddSingleton<IBusinessObjectManageService, BusinessObjectManageService>();
            services.AddSingleton<ICompositeObjectManageService, CompositeObjectManageService>();
            services.AddSingleton<IDynamicSqlService, DynamicSqlService>();
            services.AddSingleton<IApplicationMgrService, ApplicationMgrService>();
            services.AddSingleton<IViewManageService, ViewManageService>();
            services.AddSingleton<IDictionaryMgrService, DictionaryMgrService>();
            services.AddSingleton<IStructureCacheService, StructureCacheService>();
            services.AddSingleton<IPageViewService, PageViewService>();
            services.AddSingleton<IOperationLogService, OperationLogService>();

            
            services.AddSingleton<ICRMReportService, CRMReportService>();

            
            services.AddSingleton<IEOPMessageService, EOPMessageService>();

            
            services.AddSingleton<ITrainUserService, TrainUserService>();
            services.AddSingleton<ICommonService, CommonService>();

            
            services.AddSingleton<IHomePageService, HomePageService>();

            
            services.AddSingleton<ITasksService, TasksService>();
            services.AddSingleton<ICrmTasksService, CrmTasksService>();
        }
    }
}
