using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class OutsideBusinessObjectColumnStructureDto
    {
        
        
        
        public string Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public int Order { get; set; }

        
        
        
        public string DisplayType { get; set; }

        
        
        
        public string ColumnType { get; set; }

        
        
        
        public int? Length { get; set; }

        
        
        
        public int? Decimal { get; set; }
    }
}
