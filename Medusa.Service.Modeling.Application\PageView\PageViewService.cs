using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using Medusa.Service.Modeling.Application.PageView.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using NPOI.SS.Formula.Functions;
using SqlSugar;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Medusa.Service.Modeling.Application.PageView
{
    
    
    
    public class PageViewService : ServiceBase, IPageViewService
    {
        #region 
        readonly MyDbContext _dbContext;

        
        
        
        
        
        public PageViewService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
        }

        #endregion

        
        
        
        
        
        public List<ViewObjectResultDto> GetObject(ViewObjectQueryDto dto)
        {
            var result = new List<ViewObjectResultDto>();
            var types = !string.IsNullOrEmpty(dto.ObjectType) ? dto.ObjectType : "1;2;3";
            types.Split(";").ToList().ForEach(f =>
            {
                switch (f)
                {
                    case "1":
                        result.AddRange(_dbContext.Modeling.Queryable<BusinessObject>()
                        .Where(a => !a.IsDelete.Value && a.State == 1)
                        .WhereIF(dto.ApplicationId.HasValue && dto.ApplicationId.Value != Guid.Empty, a => SqlFunc.IsNullOrEmpty(a.ApplicationId) || a.ApplicationId.Contains(SqlFunc.ToString(dto.ApplicationId)))
                        .Select(a => new ViewObjectResultDto
                        {
                            ObjectId = a.Id,
                            ObjectName = a.Name,
                            ObjectDescription = a.Description,
                            ObjectType = "1"
                        }).ToList());
                        break;
                    case "2":
                        result.AddRange(_dbContext.Modeling.Queryable<CompositeObject>()
                        .Where(a => !a.IsDelete.Value)
                        .WhereIF(dto.ApplicationId.HasValue && dto.ApplicationId.Value != Guid.Empty, a => SqlFunc.IsNullOrEmpty(a.ApplicationId) || a.ApplicationId.Contains(SqlFunc.ToString(dto.ApplicationId)))
                        .Select(a => new ViewObjectResultDto
                        {
                            ObjectId = a.Id,
                            ObjectName = a.Name,
                            ObjectDescription = a.Name,
                            ObjectType = "2"
                        }).ToList());
                        break;
                    case "3":
                        result.AddRange(_dbContext.Modeling.Queryable<View>()
                       .Where(a => a.State == 2)
                       .WhereIF(dto.ApplicationId.HasValue && dto.ApplicationId.Value != Guid.Empty, a => SqlFunc.IsNullOrEmpty(a.AppId) || a.AppId == dto.ApplicationId)
                       .Select(a => new ViewObjectResultDto
                       {
                           ObjectId = a.Id,
                           ObjectName = a.Name,
                           ObjectDescription = a.Description,
                           ObjectType = "3"
                       }).ToList());
                        break;
                }
            });

            return result;
        }

        
        
        
        
        
        
        public List<ViewObjectResultDto> GetObjectField(Guid id, string objectType)
        {
            var result = new List<ViewObjectResultDto>();
            switch (objectType)
            {
                case "1":
                    var businessObject = _dbContext.Modeling.Queryable<BusinessObject>().InSingle(id);
                    var businessObjectFields = _dbContext.Modeling.Queryable<BusinessObjectColumn>()
                        .Where(w => w.BusinessObjectId == id && w.IsEnable.Value)
                        .OrderBy(w => new { w.Order, w.Id })
                        .Select(w => new ViewObjectFieldDto
                        {
                            FieldId = w.Id,
                            FieldName = w.Name,
                            FieldDescription = w.Description
                        }).ToList();
                    result.Add(new ViewObjectResultDto
                    {
                        Fields = businessObjectFields,
                        ObjectType = objectType,
                        ObjectId = businessObject.Id,
                        ObjectName = businessObject.Name,
                        ObjectDescription = businessObject.Description,
                        IsMain = true
                    });
                    break;
                case "2":
                    var compositeObject = _dbContext.Modeling.Queryable<CompositeObject, BusinessObject>((co, bo) => co.BusinessObjectId == bo.Id)
                        .Where((co, bo) => co.Id == id && !bo.IsDelete.Value)
                        .Select((co, bo) => new ViewObjectResultDto
                        {
                            ObjectType = objectType,
                            ObjectId = bo.Id,
                            ObjectName = bo.Name,
                            ObjectDescription = bo.Description,
                            IsMain = true
                        })
                        .First();
                    var compositeRelationObject = _dbContext.Modeling.Queryable<CompositeObjectRelation, BusinessObject>((co, bo) => co.BusinessObjectId == bo.Id)
                        .Where((co, bo) => co.CompositeObjectId == id && !bo.IsDelete.Value && co.ObjectType == "businessObject")
                         .Select((co, bo) => new ViewObjectResultDto
                         {
                             ObjectType = objectType,
                             ObjectId = bo.Id,
                             ObjectName = bo.Name,
                             ObjectDescription = bo.Description,
                             ParentObjectId = co.BusinessObjectId,
                             RelationFieldId = co.BusinessObjectColumnId,
                             ParentRelationFieldId = co.ParentBusinessObjectColumnId,
                             JoinType = co.JoinType,
                             JoinRelation = co.JoinRelation,
                             ExtraCondition = co.ExtraCondition
                         }).ToList();
                    var compositeRelationView = _dbContext.Modeling.Queryable<CompositeObjectRelation, View>((co, bo) => co.BusinessObjectId == bo.Id)
                       .Where((co, bo) => co.CompositeObjectId == id && co.ObjectType == "view")
                        .Select((co, bo) => new ViewObjectResultDto
                        {
                            ObjectType = objectType,
                            ObjectId = bo.Id,
                            ObjectName = bo.Name,
                            ObjectDescription = bo.Description,
                            ParentObjectId = co.ParentId,
                            RelationFieldId = co.BusinessObjectColumnId,
                            ParentRelationFieldId = co.ParentBusinessObjectColumnId,
                            JoinType = co.JoinType,
                            JoinRelation = co.JoinRelation,
                            ExtraCondition = co.ExtraCondition
                        }).ToList();
                    var mainObjectFields = _dbContext.Modeling.Queryable<BusinessObjectColumn>()
                        .Where(w => w.BusinessObjectId == compositeObject.ObjectId && w.IsEnable.Value)
                        .OrderBy(w => new { w.Order, w.Id })
                        .Select(w => new ViewObjectFieldDto
                        {
                            FieldId = w.Id,
                            FieldName = w.Name,
                            FieldDescription = w.Description
                        }).ToList();
                    compositeObject.Fields = mainObjectFields;
                    result.Add(compositeObject);
                    compositeRelationObject.ForEach(f =>
                    {
                        var temp = _dbContext.Modeling.Queryable<BusinessObjectColumn>()
                        .Where(w => w.BusinessObjectId == f.ObjectId && w.IsEnable.Value)
                        .OrderBy(w => new { w.Order, w.Id })
                        .Select(w => new ViewObjectFieldDto
                        {
                            FieldId = w.Id,
                            FieldName = w.Name,
                            FieldDescription = w.Description
                        }).ToList();
                        f.Fields = temp;
                        result.Add(f);
                    });
                    compositeRelationView.ForEach(f =>
                    {
                        var temp = _dbContext.Modeling.Queryable<ViewColumns>()
                        .Where(w => w.ViewId == f.ObjectId)
                        .OrderBy(w => new { w.OrderNumber, w.Id })
                        .Select(w => new ViewObjectFieldDto
                        {
                            FieldId = w.Id,
                            FieldName = w.BusinessObjectColumnAlias,
                            FieldDescription = w.BusinessObjectColumnDescription
                        }).ToList();
                        f.Fields = temp;
                        result.Add(f);
                    });
                    break;
                case "3":
                    var viewObject = _dbContext.Modeling.Queryable<View>().InSingle(id);
                    var viewObjectFields = _dbContext.Modeling.Queryable<ViewColumns>()
                        .Where(w => w.ViewId == id && w.ColumnType == "ObjectColumn")
                        .OrderBy(w => new { w.OrderNumber, w.Id })
                        .Select(w => new ViewObjectFieldDto
                        {
                            FieldId = w.Id,
                            FieldName = w.BusinessObjectColumnAlias,
                            FieldDescription = w.BusinessObjectColumnDescription
                        }).ToList();
                    result.Add(new ViewObjectResultDto
                    {
                        Fields = viewObjectFields,
                        ObjectType = objectType,
                        ObjectId = viewObject.Id,
                        ObjectName = viewObject.Name,
                        ObjectDescription = viewObject.Description,
                        IsMain = true
                    });
                    break;
            }

            return result;
        }
    }
}
