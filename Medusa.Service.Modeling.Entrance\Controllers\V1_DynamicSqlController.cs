using System;
using System.Text;
using System.Web;
using Medusa.Service.Modeling.Application.DynamicSql;
using Medusa.Service.Modeling.Application.DynamicSql.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// DynamicSql controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "DynamicSql.v1")]
    public class V1_DynamicSqlController : ProductControllerBase
    {
        #region //  服务注入
        readonly IDynamicSqlService _dynamicSqlService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_DynamicSqlController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="dynamicSqlService">dynamicSqlService</param>
        public V1_DynamicSqlController(IDynamicSqlService dynamicSqlService)
        {
            _dynamicSqlService = dynamicSqlService;
        }
        #endregion

        /// <summary>
        /// Query
        /// </summary>
        /// <param name="jObject">jObject</param>
        /// <param name="databaseId">databaseId</param>
        /// <returns>returns</returns>
        [HttpPost("query/{databaseId}")]
        public JObject Query([FromBody] JObject jObject, Guid databaseId)
        {
            return _dynamicSqlService.Query(jObject, databaseId);
        }

        /// <summary>
        ///  Update
        /// </summary>
        /// <param name="jObject">jObject</param>
        /// <param name="databaseId">databaseId</param>
        [HttpPost("update/{databaseId}")]
        public void Update([FromBody] JObject jObject, Guid databaseId)
        {
            _dynamicSqlService.Update(jObject, databaseId);
        }

        /// <summary>
        /// Export
        /// </summary>
        /// <param name="jObject">jObject</param>
        /// <param name="databaseId">databaseId</param>
        [HttpPost("export/{databaseId}")]
        public void Export([FromBody] JObject jObject, Guid databaseId)
        {
            var data = _dynamicSqlService.Export(jObject, databaseId);
            var name = HttpUtility.UrlEncode($"{data.GetSheetName(0)}.xlsx", Encoding.UTF8);
            Response.Clear();
            Response.ContentType = "application/octet-stream";
            Response.Headers.Add("Content-Disposition", $"attachment; filename={name}");
            data.Write(Response.Body);
            data.Close();
        }

        /// <summary>
        /// 获取函数列结果
        /// </summary>
        /// <param name="paras">paras</param>
        /// <returns>结果</returns>
        [HttpPost("get-function-column-result")]
        public ViewFunctionDto GetFunctionColumnResult([FromBody] ViewFunctionDto paras)
        {
            return _dynamicSqlService.GetFunctionColumnResult(paras);
        }

        /// <summary>
        /// Query
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("query")]
        public JObject Query([FromBody] DynamicQueryDto dto)
        {
            return _dynamicSqlService.Query(dto);
        }
    }
}
