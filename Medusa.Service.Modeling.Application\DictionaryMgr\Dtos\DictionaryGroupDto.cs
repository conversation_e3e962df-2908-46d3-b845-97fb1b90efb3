using System.Collections.Generic;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Core.Entity;

namespace Medusa.Service.Modeling.Application.DictionaryMgr.Dtos
{
    
    
    
    public class DictionaryGroupDto
    {
        
        
        
        public List<Dictionary> Dics { get; set; }

        
        
        
        public List<ObjectStructureDto> Structure { get; set; }
    }
}
