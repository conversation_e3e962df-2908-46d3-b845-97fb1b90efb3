using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 售后客诉台账表
    /// </summary>
    [SugarTable("crmcustomercomplaintamount")]
    public class Crmcustomercomplaintamount
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 临时责任人Id
        /// </summary>
        [SugarColumn(ColumnName = "TemporaryUserId")]
        public string TemporaryUserId { get; set; }

        /// <summary>
        /// 问题分类编码
        /// </summary>
        [SugarColumn(ColumnName = "QuestionCode")]
        public string QuestionCode { get; set; }

        /// <summary>
        /// 客服人员对象
        /// </summary>
        [SugarColumn(ColumnName = "ServiceUserObject")]
        public string ServiceUserObject { get; set; }

        /// <summary>
        /// 线别
        /// </summary>
        [SugarColumn(ColumnName = "LineName")]
        public string LineName { get; set; }

        /// <summary>
        /// 客服人员
        /// </summary>
        [SugarColumn(ColumnName = "ServiceUserName")]
        public string ServiceUserName { get; set; }

        /// <summary>
        /// 长期责任人对象
        /// </summary>
        [SugarColumn(ColumnName = "LongTermUserObject")]
        public string LongTermUserObject { get; set; }

        /// <summary>
        /// 事业部/产品线编码
        /// </summary>
        [SugarColumn(ColumnName = "AttributionDept")]
        public string AttributionDept { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        [SugarColumn(ColumnName = "ProductModel")]
        public string ProductModel { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "Remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 长期责任人
        /// </summary>
        [SugarColumn(ColumnName = "LongTermUserName")]
        public string LongTermUserName { get; set; }

        /// <summary>
        /// 是否首次问题编码
        /// </summary>
        [SugarColumn(ColumnName = "IsFirst")]
        public string IsFirst { get; set; }

        /// <summary>
        /// 责任人对象
        /// </summary>
        [SugarColumn(ColumnName = "ResponUserObject")]
        public string ResponUserObject { get; set; }

        /// <summary>
        /// 对策
        /// </summary>
        [SugarColumn(ColumnName = "Solution")]
        public string Solution { get; set; }

        /// <summary>
        /// 责任部门
        /// </summary>
        [SugarColumn(ColumnName = "ResponDeptName")]
        public string ResponDeptName { get; set; }

        /// <summary>
        /// 责任人用户名
        /// </summary>
        [SugarColumn(ColumnName = "ResponUserId")]
        public string ResponUserId { get; set; }

        /// <summary>
        /// 不良数量
        /// </summary>
        [SugarColumn(ColumnName = "FaultyQuantity")]
        public string FaultyQuantity { get; set; }

        /// <summary>
        /// 处理状态
        /// </summary>
        [SugarColumn(ColumnName = "ProcessingStatusName")]
        public string ProcessingStatusName { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "CreateDate")]
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "ModifyDate")]
        public DateTime ModifyDate { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        [SugarColumn(ColumnName = "CustomerId")]
        public string CustomerId { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        [SugarColumn(ColumnName = "ProductName")]
        public string ProductName { get; set; }

        /// <summary>
        /// 是否量产
        /// </summary>
        [SugarColumn(ColumnName = "IsProductionName")]
        public string IsProductionName { get; set; }

        /// <summary>
        /// 发生工序
        /// </summary>
        [SugarColumn(ColumnName = "AttributionProcedureName")]
        public string AttributionProcedureName { get; set; }

        /// <summary>
        /// 长期责任人ID
        /// </summary>
        [SugarColumn(ColumnName = "LongTermUserId")]
        public string LongTermUserId { get; set; }

        /// <summary>
        /// 补货数量
        /// </summary>
        [SugarColumn(ColumnName = "ReplenishmentQuantity")]
        public decimal? ReplenishmentQuantity { get; set; }

        /// <summary>
        /// 年月
        /// </summary>
        [SugarColumn(ColumnName = "Month")]
        public string Month { get; set; }

        /// <summary>
        /// 产品规格
        /// </summary>
        [SugarColumn(ColumnName = "ProductSpecifications")]
        public string ProductSpecifications { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        [SugarColumn(ColumnName = "CreateUserId")]
        public string CreateUserId { get; set; }

        /// <summary>
        /// 其他
        /// </summary>
        [SugarColumn(ColumnName = "Other")]
        public string Other { get; set; }

        /// <summary>
        /// 制造工厂
        /// </summary>
        [SugarColumn(ColumnName = "AttributionFactoryName")]
        public string AttributionFactoryName { get; set; }

        /// <summary>
        /// 是否成立编码
        /// </summary>
        [SugarColumn(ColumnName = "IsEstablish")]
        public string IsEstablish { get; set; }

        /// <summary>
        /// 客户诉求
        /// </summary>
        [SugarColumn(ColumnName = "CustomerDemand")]
        public string CustomerDemand { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [SugarColumn(ColumnName = "BatchNo")]
        public string BatchNo { get; set; }

        /// <summary>
        /// 原因分类编码
        /// </summary>
        [SugarColumn(ColumnName = "ReasonClassification")]
        public string ReasonClassification { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [SugarColumn(ColumnName = "TemporaryCompeleteTime")]
        public string TemporaryCompeleteTime { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        [SugarColumn(ColumnName = "ProductId")]
        public string ProductId { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        [SugarColumn(ColumnName = "ModifyUserId")]
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 不良比例
        /// </summary>
        [SugarColumn(ColumnName = "FaultyRate")]
        public decimal? FaultyRate { get; set; }

        /// <summary>
        /// 客诉等级编码
        /// </summary>
        [SugarColumn(ColumnName = "ComplaintLevel")]
        public string ComplaintLevel { get; set; }

        /// <summary>
        /// 临时责任人
        /// </summary>
        [SugarColumn(ColumnName = "TemporaryUserName")]
        public string TemporaryUserName { get; set; }

        /// <summary>
        /// 发现工序
        /// </summary>
        [SugarColumn(ColumnName = "ProcedureName")]
        public string ProcedureName { get; set; }

        /// <summary>
        /// 客诉编号
        /// </summary>
        [SugarColumn(ColumnName = "No")]
        public string No { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [SugarColumn(ColumnName = "CustomerName")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客服人员ID
        /// </summary>
        [SugarColumn(ColumnName = "ServiceUserId")]
        public string ServiceUserId { get; set; }

        /// <summary>
        /// 问题分类
        /// </summary>
        [SugarColumn(ColumnName = "QuestionName")]
        public string QuestionName { get; set; }

        /// <summary>
        /// 发生工序编码
        /// </summary>
        [SugarColumn(ColumnName = "AttributionProcedure")]
        public string AttributionProcedure { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [SugarColumn(ColumnName = "UnitName")]
        public string UnitName { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        [SugarColumn(ColumnName = "CreateUserOrgPathId")]
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 是否成立
        /// </summary>
        [SugarColumn(ColumnName = "IsEstablishName")]
        public string IsEstablishName { get; set; }

        /// <summary>
        /// 措施
        /// </summary>
        [SugarColumn(ColumnName = "Measures")]
        public string Measures { get; set; }

        /// <summary>
        /// 单位编码
        /// </summary>
        [SugarColumn(ColumnName = "Unit")]
        public string Unit { get; set; }

        /// <summary>
        /// 客户工厂
        /// </summary>
        [SugarColumn(ColumnName = "CustomerFactory")]
        public string CustomerFactory { get; set; }

        /// <summary>
        /// 客诉等级
        /// </summary>
        [SugarColumn(ColumnName = "ComplaintLevelName")]
        public string ComplaintLevelName { get; set; }

        /// <summary>
        /// 是否首次问题
        /// </summary>
        [SugarColumn(ColumnName = "IsFirstName")]
        public string IsFirstName { get; set; }

        /// <summary>
        /// 班别
        /// </summary>
        [SugarColumn(ColumnName = "ShiftCategoryName")]
        public string ShiftCategoryName { get; set; }

        /// <summary>
        /// 班别编码
        /// </summary>
        [SugarColumn(ColumnName = "ShiftCategory")]
        public string ShiftCategory { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [SugarColumn(ColumnName = "LongTermCompeleteTime")]
        public string LongTermCompeleteTime { get; set; }

        /// <summary>
        /// 责任人
        /// </summary>
        [SugarColumn(ColumnName = "ResponUserName")]
        public string ResponUserName { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        [SugarColumn(ColumnName = "ParentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// 是否量产编码
        /// </summary>
        [SugarColumn(ColumnName = "IsProduction")]
        public string IsProduction { get; set; }

        /// <summary>
        /// 原因分类
        /// </summary>
        [SugarColumn(ColumnName = "ReasonClassificationName")]
        public string ReasonClassificationName { get; set; }

        /// <summary>
        /// 问题
        /// </summary>
        [SugarColumn(ColumnName = "Question")]
        public string Question { get; set; }

        /// <summary>
        /// 处理状态编码
        /// </summary>
        [SugarColumn(ColumnName = "ProcessingStatus")]
        public string ProcessingStatus { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public byte? IsDelete { get; set; }

        /// <summary>
        /// 差旅费
        /// </summary>
        [SugarColumn(ColumnName = "BusinessFee")]
        public decimal? BusinessFee { get; set; }

        /// <summary>
        /// 反馈日期
        /// </summary>
        [SugarColumn(ColumnName = "FeedbackDate")]
        public string FeedbackDate { get; set; }

        /// <summary>
        /// 责任部门对象
        /// </summary>
        [SugarColumn(ColumnName = "ResponDeptObject")]
        public string ResponDeptObject { get; set; }

        /// <summary>
        /// 事业部/产品线
        /// </summary>
        [SugarColumn(ColumnName = "AttributionDeptName")]
        public string AttributionDeptName { get; set; }

        /// <summary>
        /// 发生原因
        /// </summary>
        [SugarColumn(ColumnName = "ReasonDesc")]
        public string ReasonDesc { get; set; }

        /// <summary>
        /// 临时责任人对象
        /// </summary>
        [SugarColumn(ColumnName = "TemporaryUserObject")]
        public string TemporaryUserObject { get; set; }

        /// <summary>
        /// 制造工厂编码
        /// </summary>
        [SugarColumn(ColumnName = "AttributionFactory")]
        public string AttributionFactory { get; set; }

        /// <summary>
        /// 发现工序编码
        /// </summary>
        [SugarColumn(ColumnName = "Procedure")]
        public string Procedure { get; set; }

        /// <summary>
        /// 责任部门编码
        /// </summary>
        [SugarColumn(ColumnName = "ResponDept")]
        public string ResponDept { get; set; }

        /// <summary>
        /// 临时责任人登录账号
        /// </summary>
        [SugarColumn(ColumnName = "TemporaryUserLoginId")]
        public string TemporaryUserLoginId { get; set; }

        /// <summary>
        /// 长期责任人登录账号
        /// </summary>
        [SugarColumn(ColumnName = "LongTermUserLoginId")]
        public string LongTermUserLoginId { get; set; }

        /// <summary>
        /// 销售担当对象
        /// </summary>
        [SugarColumn(ColumnName = "SaleManObject")]
        public string SaleManObject { get; set; }

        /// <summary>
        /// 销售担当ID
        /// </summary>
        [SugarColumn(ColumnName = "SaleManID")]
        public string SaleManID { get; set; }

        /// <summary>
        /// 销售担当
        /// </summary>
        [SugarColumn(ColumnName = "SaleMan")]
        public string SaleMan { get; set; }

        /// <summary>
        /// 客诉来源ID
        /// </summary>
        [SugarColumn(ColumnName = "ResourceID")]
        public string ResourceID { get; set; }

        /// <summary>
        /// 客诉来源
        /// </summary>
        [SugarColumn(ColumnName = "Resource")]
        public string Resource { get; set; }

        /// <summary>
        /// 有无索赔单ID
        /// </summary>
        [SugarColumn(ColumnName = "HasSPDID")]
        public string HasSPDID { get; set; }

        /// <summary>
        /// 有无索赔单
        /// </summary>
        [SugarColumn(ColumnName = "HasSPD")]
        public string HasSPD { get; set; }

        /// <summary>
        /// 赛伍产品处理方式ID
        /// </summary>
        [SugarColumn(ColumnName = "SWProductID")]
        public string SWProductID { get; set; }

        /// <summary>
        /// 赛伍产品处理方式
        /// </summary>
        [SugarColumn(ColumnName = "SWProduct")]
        public string SWProduct { get; set; }

        /// <summary>
        /// 客户产品处理方式ID
        /// </summary>
        [SugarColumn(ColumnName = "CustomerProductID")]
        public string CustomerProductID { get; set; }

        /// <summary>
        /// 客户产品处理方式
        /// </summary>
        [SugarColumn(ColumnName = "CustomerProduct")]
        public string CustomerProduct { get; set; }

        /// <summary>
        /// 售后方案
        /// </summary>
        [SugarColumn(ColumnName = "AfterSalePlan")]
        public string AfterSalePlan { get; set; }

        /// <summary>
        /// 客诉成本
        /// </summary>
        [SugarColumn(ColumnName = "TotalAmount")]
        public string TotalAmount { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [SugarColumn(ColumnName = "CustomerAddress")]
        public string CustomerAddress { get; set; }

        /// <summary>
        /// 客户联系人
        /// </summary>
        [SugarColumn(ColumnName = "CustomerContactor")]
        public string CustomerContactor { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [SugarColumn(ColumnName = "ContactPhone")]
        public string ContactPhone { get; set; }

        /// <summary>
        /// 品质部负责人
        /// </summary>
        [SugarColumn(ColumnName = "PZBFZR")]
        public string Pzbfzr { get; set; }

        /// <summary>
        /// 品质部负责人编码
        /// </summary>
        [SugarColumn(ColumnName = "PZBFZRCode")]
        public string PZBFZRCode { get; set; }

        /// <summary>
        /// 品质部负责人对象
        /// </summary>
        [SugarColumn(ColumnName = "PZBFZRObject")]
        public string PZBFZRObject { get; set; }

        /// <summary>
        /// 财务部负责人对象
        /// </summary>
        [SugarColumn(ColumnName = "CWBFZRObject")]
        public string CWBFZRObject { get; set; }

        /// <summary>
        /// 财务部负责人编码
        /// </summary>
        [SugarColumn(ColumnName = "CWBFZRCode")]
        public string CWBFZRCode { get; set; }

        /// <summary>
        /// 财务部负责人
        /// </summary>
        [SugarColumn(ColumnName = "CWBFZR")]
        public string Cwbfzr { get; set; }

        /// <summary>
        /// 涉及产品线Code
        /// </summary>
        [SugarColumn(ColumnName = "SJCPX")]
        public string Sjcpx { get; set; }

        /// <summary>
        /// 涉及产品线
        /// </summary>
        [SugarColumn(ColumnName = "SJCPXName")]
        public string SJCPXName { get; set; }
    }
}
