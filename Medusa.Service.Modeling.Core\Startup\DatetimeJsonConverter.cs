using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using MT.Enterprise.Utils;
using MT.Enterprise.Utils.Extensions;

namespace Medusa.Service.Modeling.Core
{
    /// <summary>
    /// DatetimeJsonConverter
    /// </summary>
    public class DatetimeJsonConverter : JsonConverter<DateTime>
    {
        /// <summary>
        /// 重写
        /// </summary>
        /// <param name="reader">reader</param>
        /// <param name="typeToConvert">typeToConvert</param>
        /// <param name="options">options</param>
        /// <returns>Datetime</returns>
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            return reader.GetString().ToOurDateTime();
        }

        /// <summary>
        /// 重写
        /// </summary>
        /// <param name="writer">writer</param>
        /// <param name="value">value</param>
        /// <param name="options">options</param>
        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString(Clock.GetFormat(Clock.ClockType.DateTime)));
        }
    }
}
