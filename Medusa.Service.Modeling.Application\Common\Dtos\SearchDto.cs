using System.Collections.Generic;

namespace Medusa.Service.Modeling.Application.Common.Dtos
{
    
    
    
    public class SearchDto
    {
        
        
        
        public string ObjectType { get; set; } = "table";

        
        
        
        public string ObjectDataBaseCode { get; set; } = "Default";

        
        
        
        public string Name { get; set; }

        
        
        
        public bool IsAll { get; set; } = false;

        
        
        
        public int Page { get; set; } = 1;

        
        
        
        public int PageSize { get; set; } = 10;

        
        
        
        public List<OrderByDto> OrderBy { get; set; }

        
        
        
        public List<WhereDto> Where { get; set; }
    }
}
