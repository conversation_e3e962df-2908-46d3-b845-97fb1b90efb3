using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// 满意度调查
    /// </summary>
    [SugarTable("crmsaleclue")]
    public class CrmSaleClue
    {
        /// <summary>
        /// 主键
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 市场担当
        /// </summary>
        public string MarketUserId { get; set; }

        /// <summary>
        /// 销售担当
        /// </summary>
        public string SalesUserId { get; set; }

        /// <summary>
        /// 线索名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string CreateDate { get; set; }
    }
}
