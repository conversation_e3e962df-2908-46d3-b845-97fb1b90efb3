using System.Threading.Tasks;
using Medusa.Service.Modeling.Application.OperationLog.Dtos;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Application.OperationLog
{
    
    
    
    public interface IOperationLogService : IServiceBase
    {
        
        
        
        
        
        Task WriteLog(WriteLogDto dto);

        
        
        
        
        
        Task<PageResult<LogResultDto>> GetLog(LogQueryDto dto);
    }
}
