using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Text;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.Cache;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Utils.Extensions;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.BusinessObjectManage
{
    
    
    
    public class BusinessObjectManageService : ServiceBase, IBusinessObjectManageService
    {
        #region 
        readonly MyDbContext _dbContext;
        private readonly IStructureCacheService _structureCacheService;

        
        
        
        
        
        public BusinessObjectManageService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _structureCacheService = serviceProvider.GetService<IStructureCacheService>();
        }

        #endregion

        
        
        
        
        
        public PageResult<ObjectDto> GetBusinessObjects(BusinessObjectQueryDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((dt, db) => new JoinQueryInfos(JoinType.Inner, dt.DataBaseId == db.Id))
                .Where((dt, db) => !dt.IsDelete.Value)
                .WhereIF(dto.State.HasValue, (dt, db) => dt.State == dto.State)
                .WhereIF(!string.IsNullOrEmpty(dto.Name), (dt, db) => dt.Name.Contains(dto.Name))
                .WhereIF(!string.IsNullOrEmpty(dto.DataBaseName), (dt, db) => db.Name.Contains(dto.DataBaseName))
                .WhereIF(!string.IsNullOrEmpty(dto.DataBaseType), (dt, db) => db.Type == dto.DataBaseType)
                .WhereIF(dto.DataBaseId.HasValue, (dt, db) => db.Id == dto.DataBaseId)
                .WhereIF(dto.IsTree.HasValue, (dt, db) => dt.IsTree == dto.IsTree)
                .WhereIF(!string.IsNullOrEmpty(dto.ApplicationId), (dt, db) => SqlFunc.IsNullOrEmpty(dt.ApplicationId) || dt.ApplicationId.Contains(dto.ApplicationId))
                .WhereIF(!string.IsNullOrEmpty(dto.Description), (dt, db) => dt.Description.Contains(dto.Description))
                .WhereIF(dto.Id.HasValue, (dt, db) => dt.Id == dto.Id)
                .OrderBy((dt, db) => dt.CreateDate, OrderByType.Desc)
                .Select((dt, db) => new ObjectDto
                {
                    Id = dt.Id,
                    DataBaseId = dt.DataBaseId,
                    Name = dt.Name,
                    CreateDate = dt.CreateDate,
                    Description = dt.Description,
                    DataBaseName = db.Name,
                    DataBaseDescription = db.Description,
                    DataBaseType = db.Type,
                    IsCustom = dt.IsCustom,
                    IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                    State = dt.State,
                    IsTree = dt.IsTree,
                    IsLogicalDelete = dt.IsLogicalDelete,
                    ApplicationId = dt.ApplicationId,
                    ApplicationName = dt.ApplicationName
                });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            if (dto.HasColumns.HasValue && dto.HasColumns.Value)
            {
                items.ForEach(f =>
                {
                    f.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == f.Id)
                    .WhereIF(dto.IsEnable.HasValue, a => a.IsEnable == dto.IsEnable)
                    .OrderBy(a => a.Order).ToList().MapTo<List<ObjectColumnDto>>();
                });
            }

            return new PageResult<ObjectDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        
        public ObjectDto GetBusinessObject(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((dt, db) => new JoinQueryInfos(JoinType.Inner, dt.DataBaseId == db.Id))
                 .Where((dt, db) => !dt.IsDelete.Value && dt.Id == id)
                  .Select((dt, db) => new ObjectDto
                  {
                      Id = dt.Id,
                      DataBaseId = dt.DataBaseId,
                      Name = dt.Name,
                      CreateDate = dt.CreateDate,
                      Description = dt.Description,
                      DataBaseName = db.Name,
                      DataBaseDescription = db.Description,
                      DataBaseType = db.Type,
                      IsCustom = dt.IsCustom,
                      IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                      State = dt.State,
                      IsTree = dt.IsTree,
                      IsLogicalDelete = dt.IsLogicalDelete,
                      ApplicationId = dt.ApplicationId,
                      ApplicationName = dt.ApplicationName
                  }).First();
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            item.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == id).OrderBy(a => a.Order).ToList().MapTo<List<ObjectColumnDto>>();
            return item;
        }

        
        
        
        
        public void SaveBusinessObject(ObjectDto dto)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(dto.DataBaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            if (dto.Id == Guid.Empty)
            {
                dto.Name = dto.Name.Replace("_", string.Empty).Trim();
                var businessObjects = _dbContext.Modeling.Queryable<BusinessObject>().Where(d => d.DataBaseId == dto.DataBaseId && d.Name == dto.Name).ToList();
                if (client.DbMaintenance.IsAnyTable(dto.Name, false) || businessObjects.Count > 0)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.exist"));
                }

                var businessObject = dto.MapTo<BusinessObject>();
                businessObject.Name = businessObject.Name;
                businessObject.Id = Guid.NewGuid();
                businessObject.IsCustom = true;
                businessObject.IsDelete = false;
                businessObject.State = 0;
                DateTime now = DateTime.Now;
                businessObject.CreateDate = now;
                var columns = dto.Columns.MapTo<List<BusinessObjectColumn>>();
                int order = 1;
                columns.ForEach(f =>
                {
                    f.Name = f.Name.Replace("_", string.Empty).Trim();
                    f.Id = Guid.NewGuid();
                    f.Type = DbTypeHelper.NetTypeFormat(dataBase.Type, f);
                    f.BusinessObjectId = businessObject.Id;
                    f.CreateDate = now;
                    f.Order = order;
                    f.Length = DbTypeHelper.GetLength(dataBase.Type, f.DisplayType, f.Length);
                    order += 1;
                });
                _dbContext.Modeling.Insertable(businessObject).ExecuteCommand();
                if (columns != null && columns.Count > 0)
                {
                    _dbContext.Modeling.Insertable(columns).ExecuteCommand();
                }
            }
            else
            {
                var businessObject = _dbContext.Modeling.Queryable<BusinessObject>().InSingle(dto.Id);
                if (businessObject == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
                }

                dto.Name = dto.Name.Replace("_", string.Empty).Trim();
                if (businessObject.Name != dto.Name)
                {
                    client.DbMaintenance.RenameTable(businessObject.Name, dto.Name);
                }

                businessObject.Name = dto.Name;
                businessObject.Description = dto.Description;
                businessObject.IsTree = dto.IsTree;
                businessObject.IsLogicalDelete = dto.IsLogicalDelete;
                businessObject.ApplicationId = dto.ApplicationId;
                businessObject.ApplicationName = dto.ApplicationName;
                var insert = new List<BusinessObjectColumn>();
                var update = new List<BusinessObjectColumn>();
                var delete = new List<Guid>();
                var columns = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == dto.Id).OrderBy(a => a.Order).ToList();
                var inColumns = dto.Columns.Where(f => f.IsNew).ToList().MapTo<List<BusinessObjectColumn>>();
                var upColumns = dto.Columns.Where(f => !f.IsNew).ToList().MapTo<List<BusinessObjectColumn>>();
                int order = columns[columns.Count - 1].Order + 1;
                inColumns.ForEach(f =>
                {
                    f.Name = f.Name.Replace("_", string.Empty).Trim();
                    f.Id = Guid.NewGuid();
                    f.Type = DbTypeHelper.NetTypeFormat(dataBase.Type, f);
                    f.BusinessObjectId = businessObject.Id;
                    f.CreateDate = DateTime.Now;
                    f.Order = order;
                    f.Length = DbTypeHelper.GetLength(dataBase.Type, f.DisplayType, f.Length);
                    f.DefaultValue = f.DefaultValue;
                    order += 1;
                    insert.Add(f);
                });

                columns.ForEach(f =>
                {
                    var column = upColumns.Find(a => a.Id == f.Id);
                    if (column != null)
                    {
                        column.Name = column.Name.Replace("_", string.Empty).Trim();
                        f.OldName = f.Name;
                        f.Name = column.Name;
                        f.Description = column.Description;
                        f.IsIdentity = column.IsIdentity;
                        f.IsPrimaryKey = column.IsPrimaryKey;
                        f.IsNullable = column.IsNullable;
                        f.Type = DbTypeHelper.NetTypeFormat(dataBase.Type, f);
                        f.DisplayType = f.DisplayType;
                        f.Decimal = column.Decimal;
                        f.IsEnable = column.IsEnable;
                        f.IsSystemColumn = column.IsSystemColumn;
                        f.Order = f.Order;
                        f.Length = DbTypeHelper.GetLength(dataBase.Type, column.DisplayType, column.Length);
                        f.DefaultValue = column.DefaultValue;
                        update.Add(f);
                    }
                    else
                    {
                        delete.Add(f.Id);
                    }
                });

                _dbContext.Modeling.Updateable(businessObject).ExecuteCommand();

                if (businessObject.State == 0)
                {
                    if (delete != null && delete.Count > 0)
                    {
                        _dbContext.Modeling.Deleteable<BusinessObjectColumn>(delete).ExecuteCommand();
                    }
                }

                if (insert != null && insert.Count > 0)
                {
                    _dbContext.Modeling.Insertable(insert).ExecuteCommand();
                }

                if (update != null && update.Count > 0)
                {
                    _dbContext.Modeling.Updateable(update).ExecuteCommand();
                }

                if (businessObject.State == 1)
                {
                    var initTableColumns = insert;
                    var dbColumns = client.DbMaintenance.GetColumnInfosByTableName(businessObject.Name, false);
                    dbColumns.ForEach(f =>
                    {
                        var column = update.Find(a => a.Name == f.DbColumnName);
                        initTableColumns.Add(new BusinessObjectColumn
                        {
                            Decimal = column != null && column.Decimal != f.DecimalDigits ? column.Decimal : f.DecimalDigits,
                            IsNullable = f.IsNullable,
                            IsPrimaryKey = f.IsPrimarykey,
                            Length = column != null && column.Length != f.Length ? column.Length : f.Length,
                            Name = f.DbColumnName,
                            Description = column != null ? column.Description : f.ColumnDescription,
                            Type = column.DisplayType == "number" && column.Type != f.DataType ? column.Type : f.DataType,
                            IsIdentity = f.IsIdentity,
                            DefaultValue = f.DefaultValue
                        });
                    });
                    client.CodeFirst.InitTables(CommonHelper.CreateDataTableClass(businessObject, initTableColumns));
                    _structureCacheService.SetRedis(new StructureCache.Dtos.StructureCacheSetDto()
                    {
                        BusinessObjectId = dto.Id,
                        BusinessObjectType = 1
                    });
                }
            }
        }

        
        
        
        
        public void DeleteBusinessObject(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<BusinessObject>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            item.IsDelete = true;
            _dbContext.Modeling.Updateable(item).ExecuteCommand();
            _dbContext.Modeling.DbMaintenance.DropTable(item.Name);
        }

        
        
        
        
        public void PublishBusinessObject(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<BusinessObject>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(item.DataBaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            item.State = 1;
            _dbContext.Modeling.Updateable(item).ExecuteCommand();
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            var initTableColumns = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(w => w.BusinessObjectId == item.Id).ToList();
            client.CodeFirst.InitTables(CommonHelper.CreateDataTableClass(item, initTableColumns));
            _structureCacheService.SetRedis(new StructureCache.Dtos.StructureCacheSetDto()
            {
                BusinessObjectId = item.Id,
                BusinessObjectType = 1
            });
        }

        
        
        
        
        
        public BOColumnDataDto GetBusinessObjectTreeList(Guid id)
        {
            BOColumnDataDto columnsInfo = new BOColumnDataDto();
            var item = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((dt, db) => new JoinQueryInfos(JoinType.Inner, dt.DataBaseId == db.Id))
                 .Where((dt, db) => !dt.IsDelete.Value && dt.Id == id)
                  .Select((dt, db) => new ObjectDto
                  {
                      Id = dt.Id,
                      DataBaseId = dt.DataBaseId,
                      Name = dt.Name,
                      CreateDate = dt.CreateDate,
                      Description = dt.Description,
                      DataBaseName = db.Name,
                      DataBaseDescription = db.Description,
                      DataBaseType = db.Type,
                      IsCustom = dt.IsCustom,
                      IsOutSideBusinessBase = db.IsOutSideBusinessBase,
                      State = dt.State,
                      IsTree = dt.IsTree,
                      IsLogicalDelete = dt.IsLogicalDelete
                  }).First();
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            List<BusinessObjectColumn> colList = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == id).OrderBy(a => a.Order).ToList();
            StringBuilder sb = new StringBuilder();
            List<BOColumnTreeDto> cTreeList = new List<BOColumnTreeDto>();
            foreach (BusinessObjectColumn col in colList)
            {
                BOColumnTreeDto bo = new BOColumnTreeDto();
                bo.Level = "2";
                bo.Title = string.IsNullOrEmpty(col.Description) ? col.Name : col.Description;
                bo.Key = col.Name;
                cTreeList.Add(bo);
            }

            List<BOColumnTreeDto> list = new List<BOColumnTreeDto>();
            BOColumnTreeDto pTree = new BOColumnTreeDto();
            pTree.Level = "1";
            pTree.Title = string.IsNullOrEmpty(item.Description) ? item.Name : item.Description;
            pTree.Key = item.Name;
            pTree.Children = cTreeList;
            list.Add(pTree);
            columnsInfo.DataBaseName = item.DataBaseName;
            columnsInfo.ObjectName = item.Name;
            columnsInfo.ObjectColumns = list;
            return columnsInfo;
        }

        
        
        
        
        
        public DataTable GetBusinessObjectDataList(BOColumnDataDto dto)
        {
            string cols = "Id,";
            foreach (BOColumnTreeDto col in dto.ObjectColumns)
            {
                if (col.Key.ToLower() != "id")
                {
                    cols += col.Key + ",";
                }
            }

            DataTable dt = _dbContext.Modeling.Queryable(dto.ObjectName, "Cust" + dto.ObjectName).Select(cols.TrimEnd(',')).ToDataTable();
            return dt;
        }

        
        
        
        
        public void UpdateBusinessObjectData(List<BOColumnValueDto> dto)
        {
            string cols = string.Empty;
            List<string> sList = new List<string>();
            List<SugarParameter> pList = new List<SugarParameter>();
            string id = string.Empty;
            foreach (BOColumnValueDto col in dto)
            {
                if (col.Key == "id")
                {
                    id = col.Value;
                }
                else
                {
                    cols += string.Format("{0}=@{0}", col.Key) + ",";
                    pList.Add(new SugarParameter("@" + col.Key, col.Value));
                }
            }

            string sql = string.Format(@"update {0} set {1} where id='{2}'", dto[0].Table, cols.TrimEnd(','), id);
            _dbContext.Modeling.Ado.ExecuteCommand(sql, pList);
        }

        
        
        
        
        
        public PageResult<ExpandoObject> GetBusinessObjectTree(BusinessObjectTreeQuery dto)
        {
            var item = _dbContext.Modeling.Queryable<DataBase>().InSingle(dto.DataBaseId);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var result = new PageResult<ExpandoObject>();
            SqlSugarClient client = CustomDbClient.CustomDB(item);
            if (client != null)
            {
                if (dto.ControlType == "mmt-tree" || dto.ControlType == "mmt-tree-select")
                {
                    var columns = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == SqlFunc.ToGuid(dto.BusinessObjectId)).ToList();
                    var select = new List<string>();
                    columns.ForEach(f =>
                    {
                        select.Add("Cust1." + f.Name + " as Cust_" + f.Name);
                    });
                    List<SugarParameter> pList = new List<SugarParameter>();
                    if (!string.IsNullOrEmpty(dto.ParentId))
                    {
                        pList.Add(new SugarParameter("@PID", dto.ParentId));
                    }

                    var orderBy = new List<OrderByModel>();
                    if (dto.FilterConditions != null && dto.FilterConditions.orderBy != null)
                    {
                        foreach (var oby in dto.FilterConditions.orderBy)
                        {
                            orderBy.Add(new OrderByModel
                            {
                                FieldName = $"Cust_{oby.field}",
                                OrderByType = oby.order == "asc" ? OrderByType.Asc : OrderByType.Desc
                            });
                        }
                    }

                    var conditonModels = new List<IConditionalModel>();
                    var ddt = new List<KeyValuePair<WhereType, ConditionalModel>>();
                    if (dto.FilterConditions != null && dto.FilterConditions.where != null)
                    {
                        foreach (var ow in dto.FilterConditions.where)
                        {
                            var cModel = new ConditionalModel()
                            {
                                FieldName = $"Cust1.{ow.field}",
                                ConditionalType = CommonHelper.GetOperatorFormat(ow),
                                FieldValue = ow.fieldValue
                            };
                            ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(dto.FilterConditions.whereRelation == "and" ? WhereType.And : WhereType.Or, cModel));
                        }

                        if (ddt.Count > 0)
                        {
                            conditonModels.Add(new ConditionalCollections { ConditionalList = ddt });
                        }
                    }
                    else
                    {
                        var cModel = new ConditionalModel()
                        {
                            FieldName = "Cust1.IsDelete",
                            ConditionalType = ConditionalType.Equal,
                            FieldValue = "0"
                        };
                        conditonModels.Add(cModel);
                    }

                    if (dto.IsAll.HasValue && dto.IsAll.Value)
                    {
                        if (!string.IsNullOrEmpty(dto.SearchStr))
                        {
                            var cModel = new ConditionalModel()
                            {
                                FieldName = $"Cust1.{dto.SearchField}",
                                ConditionalType = ConditionalType.Like,
                                FieldValue = dto.SearchStr
                            };
                            conditonModels.Add(cModel);
                        }

                        var items = client.Queryable(dto.BusinessObjectName, "Cust1")
                         .Select(string.Join(",", select))
                         .Where(conditonModels)
                         .OrderBy(orderBy)
                         .ToList();
                        result.Total = items.Count;
                        result.Items = items;
                        return result;
                    }

                    if (!string.IsNullOrEmpty(dto.SearchStr))
                    {
                        var cModel = new ConditionalModel()
                        {
                            FieldName = $"Cust1.{dto.SearchField}",
                            ConditionalType = ConditionalType.Like,
                            FieldValue = dto.SearchStr
                        };
                        conditonModels.Add(cModel);
                        var items = client.Queryable(dto.BusinessObjectName, "Cust1")
                          .Select(string.Join(",", select))
                          .Where(conditonModels)
                          .OrderBy(orderBy)
                          .ToList();
                        result.Total = items.Count;
                        result.Items = items;
                    }
                    else
                    {
                        var parentKey = !string.IsNullOrEmpty(dto.ParentField) ? dto.ParentField : "ID";
                        var childrenKey = !string.IsNullOrEmpty(dto.ChildrenField) ? dto.ChildrenField : "ParentId";
                        var itemQuery1 = client.Queryable(dto.BusinessObjectName, "Cust1")
                      .Select(string.Join(",", select))
                       .Where(conditonModels)
                      .Where(string.IsNullOrEmpty(dto.ParentId) ? $"(Cust1.{childrenKey} is null or Cust1.{childrenKey} = '')" : $"Cust1.{childrenKey} = @PID", pList);

                        var itemQuery2 = client.Queryable(dto.BusinessObjectName, "Cust1")
                            .AddJoinInfo(dto.BusinessObjectName, "Cust2", $"Cust1.{childrenKey} = Cust2.{parentKey}", JoinType.Inner)
                           .Select(string.Join(",", select))
                           .Where(conditonModels)
                           .Where(string.IsNullOrEmpty(dto.ParentId) ? $"(Cust2.{childrenKey} is null  or Cust1.{childrenKey} = '')" : $"Cust2.{childrenKey} = @PID", pList);
                        var items = client.Union<ExpandoObject>(itemQuery1, itemQuery2).OrderBy(orderBy).ToList();
                        result.Total = items.Count;
                        result.Items = items;
                    }
                }
            }

            return result;
        }

        
        
        
        
        
        
        public PageResult<ExpandoObject> GetBusinessObjectData(Guid id, BusinessObjectCommonQueryDto dto)
        {
            var businessObject = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((b, d) => b.DataBaseId == d.Id)
                .Where((b, d) => b.Id == id)
                .Select((b, d) => new
                {
                    bName = b.Name,
                    bIsDelete = b.IsDelete,
                    bState = b.State,
                    d
                })
              .First();
            if (businessObject == null || businessObject.bIsDelete.Value || businessObject.bState.Value == 0 || businessObject.d == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            SqlSugarClient client = CustomDbClient.CustomDB(businessObject.d);
            var querable = client.Queryable(businessObject.bName, "Cust" + businessObject.bName);
            var ddt = new List<KeyValuePair<WhereType, ConditionalModel>>();
            var isOr = dto.IsOr.HasValue && dto.IsOr.Value ? true : false;
            var parameterCon = new List<SugarParameter>();
            if (dto.IsDelete.HasValue)
            {
                ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                {
                    FieldName = $"Cust{businessObject.bName}.IsDelete",
                    ConditionalType = ConditionalType.Equal,
                    FieldValue = dto.IsDelete.Value ? "1" : "0"
                }));
            }

            if (dto.ModifyDateBegin.HasValue)
            {
                ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                {
                    FieldName = $"Cust{businessObject.bName}.ModifyDate",
                    ConditionalType = ConditionalType.GreaterThanOrEqual,
                    FieldValue = dto.ModifyDateBegin.Value.ToString("yyyy-MM-dd")
                }));
            }

            if (dto.ModifyDateEnd.HasValue)
            {
                ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                {
                    FieldName = $"Cust{businessObject.bName}.ModifyDate",
                    ConditionalType = ConditionalType.LessThanOrEqual,
                    FieldValue = dto.ModifyDateEnd.Value.ToString("yyyy-MM-dd")
                }));
            }

            
            if (!string.IsNullOrEmpty(dto.Wheres))
            {
                var wheres = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ConditionalModel>>(dto.Wheres);
                wheres.ForEach(f =>
                {
                    ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                    {
                        FieldName = $"Cust{businessObject.bName}.{f.FieldName}",
                        ConditionalType = f.ConditionalType,
                        FieldValue = f.FieldValue
                    }));
                });
            }

            var conditonModels = new List<IConditionalModel>();
            conditonModels.Add(new ConditionalCollections()
            {
                ConditionalList = ddt
            });
            querable.Where(conditonModels);

            var sort = OrderByType.Asc;
            if (dto.SorterOrder.HasValue && dto.SorterOrder.Value == PageSort.Descend)
            {
                sort = OrderByType.Desc;
            }

            var order = new List<string>();
            if (!string.IsNullOrEmpty(dto.SorterField))
            {
                var sp = dto.SorterField.Split(",").ToList();
                sp.ForEach(f =>
                {
                    order.Add($"Cust{businessObject.bName}.{f} {sort}");
                });
            }
            else
            {
                order.Add($"Cust{businessObject.bName}.CreateDate {sort}");
            }

            querable.OrderBy(string.Join(",", order));

            var result = new PageResult<ExpandoObject>();
            if (dto.IsAll)
            {
                result.Items = querable.ToList();
                result.Total = result.Items.Count;
            }
            else
            {
                var total = 0;
                result.Items = querable.ToPageList(dto.PageIndex, dto.PageSize, ref total);
                result.Total = total;
            }

            return result;
        }

        
        
        
        
        
        
        public int GetBusinessObjectDataCount(Guid id, BusinessObjectCommonQueryDto dto)
        {
            var businessObject = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((b, d) => b.DataBaseId == d.Id)
                 .Where((b, d) => b.Id == id)
                 .Select((b, d) => new
                 {
                     bName = b.Name,
                     bIsDelete = b.IsDelete,
                     bState = b.State,
                     d
                 })
               .First();
            if (businessObject == null || businessObject.bIsDelete.Value || businessObject.bState.Value == 0 || businessObject.d == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            SqlSugarClient client = CustomDbClient.CustomDB(businessObject.d);
            var querable = client.Queryable(businessObject.bName, "Cust" + businessObject.bName);
            var ddt = new List<KeyValuePair<WhereType, ConditionalModel>>();
            var isOr = dto.IsOr.HasValue && dto.IsOr.Value ? true : false;
            var parameterCon = new List<SugarParameter>();
            if (dto.IsDelete.HasValue)
            {
                ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                {
                    FieldName = $"Cust{businessObject.bName}.IsDelete",
                    ConditionalType = ConditionalType.Equal,
                    FieldValue = dto.IsDelete.Value ? "1" : "0"
                }));
            }

            if (dto.ModifyDateBegin.HasValue)
            {
                ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                {
                    FieldName = $"Cust{businessObject.bName}.ModifyDate",
                    ConditionalType = ConditionalType.GreaterThanOrEqual,
                    FieldValue = dto.ModifyDateBegin.Value.ToString("yyyy-MM-dd")
                }));
            }

            if (dto.ModifyDateEnd.HasValue)
            {
                ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                {
                    FieldName = $"Cust{businessObject.bName}.ModifyDate",
                    ConditionalType = ConditionalType.LessThanOrEqual,
                    FieldValue = dto.ModifyDateEnd.Value.ToString("yyyy-MM-dd")
                }));
            }

            
            if (!string.IsNullOrEmpty(dto.Wheres))
            {
                var wheres = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ConditionalModel>>(dto.Wheres);
                wheres.ForEach(f =>
                {
                    ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                    {
                        FieldName = $"Cust{businessObject.bName}.{f.FieldName}",
                        ConditionalType = f.ConditionalType,
                        FieldValue = f.FieldValue
                    }));
                });
            }

            var conditonModels = new List<IConditionalModel>();
            conditonModels.Add(new ConditionalCollections()
            {
                ConditionalList = ddt
            });
            querable.Where(conditonModels);

            var sort = OrderByType.Asc;
            if (dto.SorterOrder.HasValue && dto.SorterOrder.Value == PageSort.Descend)
            {
                sort = OrderByType.Desc;
            }

            var order = new List<string>();
            if (!string.IsNullOrEmpty(dto.SorterField))
            {
                var sp = dto.SorterField.Split(",").ToList();
                sp.ForEach(f =>
                {
                    order.Add($"Cust{businessObject.bName}.{f} {sort}");
                });
            }
            else
            {
                order.Add($"Cust{businessObject.bName}.CreateDate {sort}");
            }

            querable.OrderBy(string.Join(",", order));

            return querable.ToList().Count;
        }

        
        
        
        
        
        
        public bool CheckRepeatBusinessObjectData(Guid id, BusinessObjectCommonCheckDto dto)
        {
            var businessObject = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((b, d) => b.DataBaseId == d.Id)
                .Where((b, d) => b.Id == id)
                .Select((b, d) => new
                {
                    bName = b.Name,
                    bIsDelete = b.IsDelete,
                    bState = b.State,
                    d
                })
              .First();
            if (businessObject == null || businessObject.bIsDelete.Value || businessObject.bState.Value == 0 || businessObject.d == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
            }

            var column = _dbContext.Modeling.Queryable<BusinessObjectColumn>().Where(a => a.BusinessObjectId == id && a.Name == dto.CheckFieldName).First();
            if (column == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObjectColumn.notfound"));
            }

            SqlSugarClient client = CustomDbClient.CustomDB(businessObject.d);
            var querable = client.Queryable(businessObject.bName, "Cust" + businessObject.bName);
            var ddt = new List<KeyValuePair<WhereType, ConditionalModel>>();
            var isOr = dto.IsOr.HasValue && dto.IsOr.Value ? true : false;
            var parameterCon = new List<SugarParameter>();
            ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
            {
                FieldName = $"Cust{businessObject.bName}.{column.Name}",
                ConditionalType = ConditionalType.Equal,
                FieldValue = dto.CheckFieldValue
            }));
            if (dto.IsDelete.HasValue)
            {
                ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                {
                    FieldName = $"Cust{businessObject.bName}.IsDelete",
                    ConditionalType = ConditionalType.Equal,
                    FieldValue = dto.IsDelete.Value ? "1" : "0"
                }));
            }

            
            if (!string.IsNullOrEmpty(dto.Wheres))
            {
                var wheres = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ConditionalModel>>(dto.Wheres);
                wheres.ForEach(f =>
                {
                    ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(isOr ? WhereType.Or : WhereType.And, new ConditionalModel
                    {
                        FieldName = $"Cust{businessObject.bName}.{f.FieldName}",
                        ConditionalType = f.ConditionalType,
                        FieldValue = f.FieldValue
                    }));
                });
            }

            var conditonModels = new List<IConditionalModel>();
            conditonModels.Add(new ConditionalCollections()
            {
                ConditionalList = ddt
            });
            querable.Where(conditonModels);

            if (querable.ToList().Count > 0)
            {
                return true;
            }

            return false;
        }
    }
}