using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// PageModelings controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "PageModelingManage.v1")]
    public class V1_PageModelingManageController : ProductControllerBase
    {
        #region //  服务注入
        readonly IPageModelingManageService _pageModelingManageService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_PageModelingManageController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="pageModelingManageService">pageModelingManageService</param>
        public V1_PageModelingManageController(IPageModelingManageService pageModelingManageService)
        {
            _pageModelingManageService = pageModelingManageService;
        }
        #endregion

        #region 建模

        /// <summary>
        /// 获取建模信息
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpGet("page-modelings/{id}")]
        public PageModelingDto GetPageModeling([FromRoute]Guid id)
        {
            return _pageModelingManageService.GetPageModeling(id);
        }

        /// <summary>
        /// 获取建模信息
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="versionId">versionId</param>
        /// <returns>returns</returns>
        [HttpGet("page-modelings/{id}/preview")]
        public PageModelingDto GetPageModelingPreview([FromRoute] Guid id, [FromQuery(Name = "version-id")] Guid versionId)
        {
            return _pageModelingManageService.GetPageModelingPreview(id, versionId);
        }

        /// <summary>
        /// 获取建模信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("page-modelings")]
        public PageResult<PageModelingDto> GetPageModelings([FromQuery]PageModelingQueryDto dto)
        {
            return _pageModelingManageService.GetPageModelings(dto);
        }

        /// <summary>
        /// 保存建模信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("page-modelings/save")]
        [ModelFieldCheck]
        public Guid SavePageModeling([FromBody]PageModelingDto dto)
        {
            return _pageModelingManageService.SavePageModeling(dto);
        }

        /// <summary>
        /// 更新建模设计信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("page-modelings/desgin/update")]
        public Guid UpdatePageModelingDesgin([FromBody]PageModelingDto dto)
        {
            return _pageModelingManageService.UpdatePageModelingDesgin(dto);
        }

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("page-modelings/import")]
        public void Import([FromForm] ExportImportTemplateDto dto)
        {
            _pageModelingManageService.Import(dto);
        }

        /// <summary>
        /// 删除建模信息
        /// </summary>
        /// <param name="id">id</param>
        [HttpDelete("page-modelings/{id}")]
        public void DeletePageModelings([FromRoute] Guid id)
        {
            _pageModelingManageService.DeletePageModelings(id);
        }

        /// <summary>
        /// 获取业务对象
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("page-modelings/business-objects")]
        public List<ObjectGroupDto> GetBusinessObjects(ObjectQueryDto dto)
        {
            return _pageModelingManageService.GetBusinessObjects(dto);
        }

        /// <summary>
        /// 获取业务对象字段
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("page-modelings/business-object-fields")]
        public List<ObjectStructureDto> GetBusinessObjectFields(ObjectColumnQueryDto dto)
        {
            return _pageModelingManageService.GetBusinessObjectFields(dto);
        }

        /// <summary>
        /// 复制建模信息
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("page-modelings/copy")]
        [ModelFieldCheck]
        public void CopyPageModeling([FromBody] PageModelingDto dto)
        {
            _pageModelingManageService.CopyPageModeling(dto);
        }

        /// <summary>
        /// 导出导入模板
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("page-modelings/export-import-template")]
        public void ExportImportTemplate([FromBody] ExportImportTemplateDto dto)
        {
            var data = _pageModelingManageService.ExportImportTemplate(dto);
            var name = HttpUtility.UrlEncode($"{data.GetSheetName(0)}.xlsx", Encoding.UTF8);
            Response.Clear();
            Response.ContentType = "application/octet-stream";
            Response.Headers.Add("Content-Disposition", $"attachment; filename={name}");
            data.Write(Response.Body);
            data.Close();
        }

        /// <summary>
        /// 下架建模信息
        /// </summary>
        /// <param name="id">id</param>
        [HttpPost("page-modelings/{id}/undercarriage")]
        public void Undercarriage([FromRoute] Guid id)
        {
            _pageModelingManageService.Undercarriage(id);
        }

        /// <summary>
        /// 发布建模信息
        /// </summary>
        /// <param name="id">id</param>
        [HttpPost("page-modelings/{id}/publish")]
        public void Publish([FromRoute] Guid id)
        {
            _pageModelingManageService.Publish(id);
        }
        #endregion

        /// <summary>
        /// genclass
        /// </summary>
        /// <param name="tableName">tableName</param>
        /// <param name="namespaceKey">namespaceKey</param>
        /// <param name="db">db</param>
        /// <returns>returns</returns>
        [HttpPost("page-modelings/genclass")]
        public (string, string, string, string) GenClass([FromQuery]string tableName, [FromQuery]string namespaceKey, [FromBody]DataBase db)
        {
            return _pageModelingManageService.GenClass(tableName, namespaceKey, db);
        }

        /// <summary>
        /// 获取建模版本列表
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("page-modelings/{id}/versions")]
        public PageResult<PageModelingVersionDto> GetPageModelingVersions([FromRoute] Guid id, [FromQuery] PageQueryDtoBase dto)
        {
            return _pageModelingManageService.GetPageModelingVersions(id, dto);
        }

        /// <summary>
        /// 创建新版本
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("page-modelings/create-version")]
        public void CreateVersion([FromBody] PageModelingVersionDto dto)
        {
            _pageModelingManageService.CreateVersion(dto);
        }

        /// <summary>
        /// 发布版本
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("page-modelings/public-version")]
        public void PublicVersion([FromBody] PageModelingVersionDto dto)
        {
            _pageModelingManageService.PublicVersion(dto);
        }

        /// <summary>
        /// 应用版本
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("page-modelings/use-version")]
        public void UseVersion([FromBody] PageModelingVersionDto dto)
        {
            _pageModelingManageService.UseVersion(dto);
        }

        /// <summary>
        /// 更新建模信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("page-modelings/update")]
        public Guid UpdatePageModeling([FromBody] PageModelingDto dto)
        {
            return _pageModelingManageService.UpdatePageModeling(dto);
        }

        /// <summary>
        /// 获取建模版本列表
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpPost("page-modelings/sync/versions")]
        public List<PageModelingDto> GetPageModelingVersions([FromBody] List<string> id)
        {
            return _pageModelingManageService.GetPageModelingVersions(id);
        }

        /// <summary>
        /// 获取建模版本列表
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpPost("page-modelings/sync/objects")]
        public PageModelingObjectResultDto GetPageModelingObjects([FromBody] List<PageModelingDto> dto)
        {
            return _pageModelingManageService.GetPageModelingObjects(dto);
        }

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>JObject</returns>
        [HttpPost("page-modelings/sync")]
        public JObject Sync([FromBody] PageModelingSyncDto dto)
        {
            return _pageModelingManageService.Sync(dto);
        }
    }
}
