using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 销售线索主表
    /// </summary>
    [EntityTable("crmsaleclue")]
    public class CRMSaleClue
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// 创建用户组织路径
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 销售线索名称
        /// </summary>
        public string SaleStageName { get; set; }

        /// <summary>
        /// 销售线索编号
        /// </summary>
        public string No { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 市场担当用户名
        /// </summary>
        public string MarketUserId { get; set; }

        /// <summary>
        /// 三新类型名称
        /// </summary>
        public string SXTYpeName { get; set; }

        /// <summary>
        /// 销售担当名称
        /// </summary>
        public string SalesName { get; set; }

        /// <summary>
        /// 修改用户
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 销售阶段
        /// </summary>
        public string SaleStage { get; set; }

        /// <summary>
        /// 客户阶段
        /// </summary>
        public string CustomerStage { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public string ModifyDate { get; set; }

        /// <summary>
        /// 线索名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        /// 三新类型编码
        /// </summary>
        public string SXType { get; set; }

        /// <summary>
        /// 市场担当名称
        /// </summary>
        public string MarketName { get; set; }

        /// <summary>
        ///  市场担当对象
        /// </summary>
        public string MarketObject { get; set; }

        /// <summary>
        /// 客户阶段
        /// </summary>
        public string CustomerStageName { get; set; }

        /// <summary>
        /// 销售担当
        /// </summary>
        public string SalesObject { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string CreateDate { get; set; }

        /// <summary>
        /// 销售担当
        /// </summary>
        public string SalesUserId { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 标识SET/PV/3C
        /// </summary>
        public string Flag { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 是否推送
        /// </summary>
        public int? IsSend { get; set; }
    }
}
