using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos
{
    
    
    
    public class BusinessObjectTreeQuery
    {
        
        
        
        public Guid? DataBaseId { get; set; }

        
        
        
        public string BusinessObjectId { get; set; }

        
        
        
        public string BusinessObjectName { get; set; }

        
        
        
        public string ParentId { get; set; }

        
        
        
        public string SearchStr { get; set; }

        
        
        
        public string SearchField { get; set; }

        
        
        
        public string DefaultDataId { get; set; }

        
        
        
        public string ControlType { get; set; }

        
        
        
        public string ParentField { get; set; }

        
        
        
        public string ChildrenField { get; set; }

        
        
        
        public dynamic FilterConditions { get; set; }

        
        
        
        public bool? IsAll { get; set; }
    }
}
