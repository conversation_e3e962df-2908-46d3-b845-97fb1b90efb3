IF NOT EXISTS(SELECT * FROM sysdatabases WHERE NAME = N'BPM_Platform') 
CREATE DATABASE [BPM_Platform]
GO
USE [BPM_Platform]
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[NotificationRules]') AND type IN ('U'))  DROP TABLE [dbo].[NotificationRules]CREATE TABLE [NotificationRules] ([NotificationRuleId] bigint NOT NULL IDENTITY(1,1),[Code] nvarchar(50) NOT NULL,[Name] nvarchar(50) NOT NULL,[Type] int NOT NULL,[DelayTime] datetime NULL,[Cron] nvarchar(50) NOT NULL,[FailCounts] int NOT NULL,[IsValid] bit NOT NULL,[CreateTime] datetime NOT NULL DEFAULT (getdate()),PRIMARY KEY ([NotificationRuleId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'消息规则',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'消息规则',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'NotificationRuleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'NotificationRuleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'NotificationRuleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'Code')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Code' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Code' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'Type')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'类型（0立即发送，1到点发送，2循环发送）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Type' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'类型（0立即发送，1到点发送，2循环发送）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Type' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'DelayTime')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'延迟的时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'DelayTime' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'延迟的时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'DelayTime' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'Cron')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'循环发送设置表达式',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Cron' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'循环发送设置表达式',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'Cron' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'FailCounts')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'失败次数,循环消息不处理',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'FailCounts' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'失败次数,循环消息不处理',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'FailCounts' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'IsValid')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'IsValid' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'IsValid' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationRules', 'COLUMN', N'CreateTime')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'CreateTime' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationRules',  @level2type = 'COLUMN', @level2name = N'CreateTime' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Attachments]') AND type IN ('U'))  DROP TABLE [dbo].[Attachments]CREATE TABLE [Attachments] ([AttachmentId] uniqueidentifier NOT NULL,[IsDelete] int NULL DEFAULT ((0)),[Size] int NULL,[FileName] nvarchar(50) NULL,[State] int NULL DEFAULT ((0)),[UUName] nvarchar(200) NULL,[CreateTime] datetime NULL,[Creator] uniqueidentifier NULL,[CreatorName] nvarchar(50) NULL,[SystemType] nvarchar(50) NULL,[FilePath] nvarchar(200) NULL,PRIMARY KEY ([AttachmentId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'附件',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'附件',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'AttachmentId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'AttachmentId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'AttachmentId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'IsDelete')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'0未删除1删除',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'IsDelete' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'0未删除1删除',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'IsDelete' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'Size')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'文件大小',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'Size' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'文件大小',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'Size' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'FileName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'文件展示名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'FileName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'文件展示名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'FileName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'State')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'1激活0未激活',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'State' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'1激活0未激活',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'State' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'UUName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'UUname',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'UUName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'UUname',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'UUName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'CreateTime')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'CreateTime' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'CreateTime' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'Creator')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'Creator' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'Creator' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'CreatorName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'CreatorName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'CreatorName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'SystemType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'上传系统来源',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'SystemType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'上传系统来源',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'SystemType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Attachments', 'COLUMN', N'FilePath')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'文件路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'FilePath' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'文件路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Attachments',  @level2type = 'COLUMN', @level2name = N'FilePath' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Menus]') AND type IN ('U'))  DROP TABLE [dbo].[Menus]CREATE TABLE [Menus] ([MenuId] uniqueidentifier NOT NULL,[ParentId] uniqueidentifier NULL,[AppCode] varchar(10) NOT NULL,[PageType] char(1) NULL,[OrderNo] int NULL,[DataRights] varchar(500) NULL,[OpenType] char(1) NULL DEFAULT ('S'),[InUse] bit NULL,[MODULERIGHTS] varchar(100) NULL,[MODIFYCOLUMNLASTOFINDEX] int NULL,[DELETECOLUMNLASTOFINDEX] int NULL,[F1] nvarchar(1000) NULL,[F2] nvarchar(1000) NULL,[F3] varchar(50) NULL,PRIMARY KEY ([MenuId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Menus', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'菜单',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'菜单',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Menus', 'COLUMN', N'MenuId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'MenuId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'MenuId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Menus', 'COLUMN', N'ParentId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'父Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'ParentId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'父Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'ParentId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Menus', 'COLUMN', N'AppCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'应用Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'AppCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'应用Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'AppCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Menus', 'COLUMN', N'PageType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'页面类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'PageType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'页面类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'PageType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Menus', 'COLUMN', N'OrderNo')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'排序',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'OrderNo' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'排序',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'OrderNo' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Menus', 'COLUMN', N'InUse')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'使用状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'InUse' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'使用状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Menus',  @level2type = 'COLUMN', @level2name = N'InUse' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[MenuDetails]') AND type IN ('U'))  DROP TABLE [dbo].[MenuDetails]CREATE TABLE [MenuDetails] ([MenuDetailId] uniqueidentifier NOT NULL,[MenuName] nvarchar(100) NULL,[MenuEnglishName] varchar(50) NULL,[LanguageCode] varchar(10) NULL,[MenuId] uniqueidentifier NOT NULL,[FileURL] varchar(200) NULL,[FunctionURL] varchar(100) NULL,[Hidden] bit NULL,[IconURL1] varchar(255) NULL,[IconURL2] varchar(255) NULL,[IconURL3] varchar(255) NULL,PRIMARY KEY ([MenuDetailId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'菜单明细',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'菜单明细',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', 'COLUMN', N'MenuDetailId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuDetailId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuDetailId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', 'COLUMN', N'MenuName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'模块名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'模块名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', 'COLUMN', N'MenuEnglishName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'模块英文名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuEnglishName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'模块英文名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuEnglishName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', 'COLUMN', N'LanguageCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'语言编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'LanguageCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'语言编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'LanguageCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', 'COLUMN', N'MenuId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'菜单Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'菜单Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'MenuId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', 'COLUMN', N'FileURL')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'文件路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'FileURL' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'文件路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'FileURL' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuDetails', 'COLUMN', N'Hidden')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'隐藏菜单',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'Hidden' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'隐藏菜单',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuDetails',  @level2type = 'COLUMN', @level2name = N'Hidden' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Dictionaries]') AND type IN ('U'))  DROP TABLE [dbo].[Dictionaries]
CREATE TABLE [Dictionaries] ([DictionaryId] uniqueidentifier NOT NULL,[Code] nvarchar(50) NULL,[Name] nvarchar(100) NULL,[Value] varchar(500) NULL,[OrderNum] int NULL,[Status] int NULL,[Remark] ntext NULL,[UpperId] uniqueidentifier NULL,[TypeCode] varchar(50) NULL,[TypeName] nvarchar(20) NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([DictionaryId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'数据字典',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'数据字典',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'DictionaryId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'DictionaryId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'DictionaryId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'Code')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'字典编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Code' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'字典编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Code' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'字典名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'字典名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'Value')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'字典值',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Value' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'字典值',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Value' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'OrderNum')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'排序',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'OrderNum' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'排序',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'OrderNum' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'Remark')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Remark' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'Remark' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'UpperId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'父Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'UpperId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'父Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'UpperId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'TypeCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'分组编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'TypeCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'分组编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'TypeCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'TypeName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'分组名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'TypeName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'分组名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'TypeName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dictionaries', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dictionaries',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[JobTitles]') AND type IN ('U'))  DROP TABLE [dbo].[JobTitles]
CREATE TABLE [JobTitles] ([JobTitleId] uniqueidentifier NOT NULL,[JobNo] varchar(20) NOT NULL,[Name] nchar(50) NOT NULL,[JobNameEn] varchar(100) NULL,[JobLevel] int NOT NULL,[JobCode] nvarchar(100) NULL,[JobCategory] varchar(50) NOT NULL,[Description] varchar(500) NULL,[Status] int NULL,[F1] nvarchar(200) NULL,[F2] nvarchar(200) NULL,[F3] nvarchar(200) NULL,PRIMARY KEY ([JobTitleId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'职位',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'职位',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'JobTitleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobTitleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobTitleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'JobNo')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'职位编号',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobNo' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'职位编号',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobNo' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'JobNameEn')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'英文名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobNameEn' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'英文名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobNameEn' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'JobLevel')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'职位级别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobLevel' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'职位级别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobLevel' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'JobCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'职位编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'职位编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'JobCategory')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'职位类别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobCategory' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'职位类别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'JobCategory' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'Description')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'Description' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'Description' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'JobTitles', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'JobTitles',  @level2type = 'COLUMN', @level2name = N'Status' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Organizations]') AND type IN ('U'))  DROP TABLE [dbo].[Organizations]
CREATE TABLE [Organizations] ([OrganizationId] int IDENTITY(1,1) NOT NULL,[Name] nvarchar(200) NOT NULL,[Name2] nvarchar(200) NULL,[DeptCode] nvarchar(50) NOT NULL,[CompanyId] int NOT NULL,[UpperId] int NULL,[DeptManager] uniqueidentifier NULL,[DeptTelephone] varchar(50) NULL,[Level] int NULL,[Status] int NOT NULL,[Remark] ntext NULL,[CityId] int NULL,[IndependentRoll] bit NULL DEFAULT ((0)),[SortCode] int NULL,[ProcessLevel] int NULL,[ProcessType] int NULL,[FullPathText] nvarchar(500) NULL,[FullPathCode] varchar(1000) NULL,[F1] nvarchar(1000) NULL,[F2] nvarchar(1000) NULL,[F3] nvarchar(1000) NULL,PRIMARY KEY ([OrganizationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'OrganizationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'OrganizationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'OrganizationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'Name2')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Name2' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Name2' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'DeptCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'部门编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'DeptCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'部门编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'DeptCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'CompanyId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'公司Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'CompanyId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'公司Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'CompanyId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'UpperId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'上级Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'UpperId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'上级Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'UpperId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'DeptManager')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'负责人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'DeptManager' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'负责人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'DeptManager' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'DeptTelephone')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'电话',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'DeptTelephone' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'电话',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'DeptTelephone' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'Level')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'层级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Level' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'层级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Level' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'Remark')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Remark' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'Remark' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'CityId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'城市Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'CityId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'城市Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'CityId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'ProcessLevel')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'流程组织级别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'ProcessLevel' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'流程组织级别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'ProcessLevel' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'ProcessType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'流程组织类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'ProcessType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'流程组织类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'ProcessType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'FullPathText')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'FullPathText' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'FullPathText' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Organizations', 'COLUMN', N'FullPathCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'全路径Code',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'FullPathCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'全路径Code',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Organizations',  @level2type = 'COLUMN', @level2name = N'FullPathCode' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Positions]') AND type IN ('U'))  DROP TABLE [dbo].[Positions]
CREATE TABLE [Positions] ([PositionId] uniqueidentifier NOT NULL,[OrganizationId] int NOT NULL,[JobTitleId] uniqueidentifier NOT NULL,[OfficeId] int NULL,[PositionCode] nvarchar(100) NULL,[Name] varchar(100) NULL,[Description] nvarchar(200) NULL,[UpperId] uniqueidentifier NULL,[IsActive] bit NOT NULL,[CompanyId] int NULL,[Type] int NULL,[F1] nvarchar(200) NULL,[F2] nvarchar(200) NULL,[F3] nvarchar(200) NULL,[F4] nvarchar(200) NULL,[F5] nvarchar(200) NULL,PRIMARY KEY ([PositionId], [OrganizationId], [JobTitleId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'岗位',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'岗位',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'PositionId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'PositionId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'PositionId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'OrganizationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'OrganizationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'OrganizationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'JobTitleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'职位Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'JobTitleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'职位Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'JobTitleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'PositionCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'岗位编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'PositionCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'岗位编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'PositionCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'Description')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'Description' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'Description' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'UpperId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'父级Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'UpperId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'父级Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'UpperId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'IsActive')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'IsActive' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'IsActive' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'CompanyId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'公司ID(岗位挂公司情况)',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'CompanyId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'公司ID(岗位挂公司情况)',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'CompanyId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Positions', 'COLUMN', N'Type')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'null或0代表普通岗位，1代表分管领导',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'Type' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'null或0代表普通岗位，1代表分管领导',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Positions',  @level2type = 'COLUMN', @level2name = N'Type' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type IN ('U'))  DROP TABLE [dbo].[Users]
CREATE TABLE [Users] ([UserId] uniqueidentifier NOT NULL,[UserLoginId] varchar(50) NULL,[UserName] nvarchar(200) NULL,[UserAlias] varchar(50) NULL,[Password] varchar(100) NOT NULL,[UserType] char(1) NOT NULL,[SourceType] char(1) NULL,[Source] nvarchar(20) NULL,[FirstName] varchar(100) NULL,[MiddleName] varchar(100) NULL,[LastName] varchar(100) NULL,[PinyinFirstWord] varchar(20) NULL,[Gender] char(1) NULL,[Email] varchar(300) NULL,[Emailbake] varchar(300) NULL,[BirthDay] datetime NULL,[Status] int NULL,[MobilePhone] varchar(50) NULL,[Extension] varchar(20) NULL,[Company] varchar(100) NULL,[EmployeeType1] nvarchar(50) NULL,[CostCenterDesc] nvarchar(200) NULL,[Remark] nvarchar(500) NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[MidifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,[LastestLoginDate] datetime NULL,[F1] nvarchar(1000) NULL,[F2] nvarchar(1000) NULL,[F3] nvarchar(1000) NULL,[F4] nvarchar(1000) NULL,[F5] nvarchar(1000) NULL,[F6] nvarchar(1000) NULL,[SubCompanyId] int NULL,[PasswordLockTime] datetime NULL,[NoLoginLockTime] datetime NULL,[ApproveDate] datetime NULL,[LockStatus] int NULL,[InstanceId] varchar(50) NULL,[WorkNumber] varchar(50) NULL,[IsStoreRequest] int NULL,[SortCode] int NULL,[UpperUserId] nvarchar(200) NULL,[Grade] nvarchar(200) NULL,[JobTitle] nvarchar(200) NULL,[CompanyId] int NULL,[OrganizationId] int NULL,[FullPathText] nvarchar(500) NULL,[FullPathCode] varchar(1000) NULL,[CompanyName] nvarchar(100) NULL,[OrganizationName] nvarchar(100) NULL,PRIMARY KEY ([UserId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'人员',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'人员',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'UserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'UserLoginId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'登录账号',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserLoginId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'登录账号',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserLoginId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'UserName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'用户名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'用户名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'UserAlias')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'用户别名',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserAlias' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'用户别名',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserAlias' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Password')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'登录密码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Password' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'登录密码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Password' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'UserType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'用户类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'用户类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'UserType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'SourceType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'数据源类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'SourceType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'数据源类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'SourceType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Source')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'数据源',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Source' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'数据源',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Source' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'FirstName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'FIrst名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'FirstName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'FIrst名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'FirstName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'MiddleName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'Middle名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'MiddleName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'Middle名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'MiddleName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'LastName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'Last名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'LastName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'Last名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'LastName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'PinyinFirstWord')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'名称拼音首字母',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'PinyinFirstWord' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'名称拼音首字母',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'PinyinFirstWord' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Gender')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'性别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Gender' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'性别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Gender' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Email')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'邮件',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Email' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'邮件',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Email' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'BirthDay')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'生日',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'BirthDay' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'生日',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'BirthDay' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'MobilePhone')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'电话',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'MobilePhone' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'电话',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'MobilePhone' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Company')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'公司',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Company' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'公司',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Company' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Remark')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Remark' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Remark' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'MidifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'MidifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'MidifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'LastestLoginDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'最新登录时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'LastestLoginDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'最新登录时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'LastestLoginDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'SubCompanyId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'上级公司Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'SubCompanyId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'上级公司Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'SubCompanyId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'WorkNumber')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'工号',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'WorkNumber' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'工号',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'WorkNumber' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'Grade')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'等级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Grade' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'等级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'Grade' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'JobTitle')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'职位',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'JobTitle' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'职位',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'JobTitle' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'CompanyId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'公司Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CompanyId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'公司Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CompanyId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'OrganizationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'OrganizationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'OrganizationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'FullPathText')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织名称全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'FullPathText' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织名称全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'FullPathText' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'FullPathCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'FullPathCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'FullPathCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'CompanyName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'公司名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CompanyName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'公司名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'CompanyName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Users', 'COLUMN', N'OrganizationName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'OrganizationName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Users',  @level2type = 'COLUMN', @level2name = N'OrganizationName' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[UserPositionRelations]') AND type IN ('U'))  DROP TABLE [dbo].[UserPositionRelations]
CREATE TABLE [UserPositionRelations] ([UserPositionRelationId] uniqueidentifier NOT NULL,[PositionId] uniqueidentifier NOT NULL,[UserId] uniqueidentifier NOT NULL,[PrimaryPosition] bit NOT NULL,[StartDate] datetime NOT NULL,[EndDate] datetime NOT NULL,[IsActive] bit NOT NULL,[F1] nvarchar(200) NULL,[F2] nvarchar(200) NULL,[F3] nvarchar(200) NULL)  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'岗位人员关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'岗位人员关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', 'COLUMN', N'UserPositionRelationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'UserPositionRelationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'UserPositionRelationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', 'COLUMN', N'PositionId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'岗位Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'PositionId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'岗位Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'PositionId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', 'COLUMN', N'UserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'用户Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'UserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'用户Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'UserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', 'COLUMN', N'PrimaryPosition')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'是否主岗',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'PrimaryPosition' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'是否主岗',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'PrimaryPosition' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', 'COLUMN', N'StartDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'开始日期',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'StartDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'开始日期',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'StartDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', 'COLUMN', N'EndDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'结束日期',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'EndDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'结束日期',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'EndDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'UserPositionRelations', 'COLUMN', N'IsActive')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'IsActive' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'UserPositionRelations',  @level2type = 'COLUMN', @level2name = N'IsActive' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[ConfigSettings]') AND type IN ('U'))  DROP TABLE [dbo].[ConfigSettings]
CREATE TABLE [ConfigSettings] ([Key] nvarchar(50) NOT NULL,[Value] nvarchar(210) NULL,[Group] nvarchar(50) NULL,[Creator] uniqueidentifier NULL,[CreatorName] nvarchar(50) NULL,[CreatorTime] datetime NULL,PRIMARY KEY ([Key]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ConfigSettings', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'系统配置',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'系统配置',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ConfigSettings', 'COLUMN', N'Key')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'配置Key',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Key' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'配置Key',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Key' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ConfigSettings', 'COLUMN', N'Value')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'配置值',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Value' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'配置值',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Value' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ConfigSettings', 'COLUMN', N'Group')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'分组',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Group' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'分组',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Group' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ConfigSettings', 'COLUMN', N'Creator')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Creator' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'Creator' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ConfigSettings', 'COLUMN', N'CreatorName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'CreatorName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'CreatorName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ConfigSettings', 'COLUMN', N'CreatorTime')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'CreatorTime' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ConfigSettings',  @level2type = 'COLUMN', @level2name = N'CreatorTime' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[MenuActionRelations]') AND type IN ('U'))  DROP TABLE [dbo].[MenuActionRelations]
CREATE TABLE [MenuActionRelations] ([MenuActionRelationId] int NOT NULL IDENTITY(1,1),[Code] varchar(10) NOT NULL,[Name] nvarchar(50) NULL,[MenuId] uniqueidentifier NOT NULL,PRIMARY KEY ([MenuActionRelationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuActionRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'菜单功能',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'菜单功能',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuActionRelations', 'COLUMN', N'MenuActionRelationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'关联ID',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'MenuActionRelationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'关联ID',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'MenuActionRelationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuActionRelations', 'COLUMN', N'Code')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'功能编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'Code' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'功能编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'Code' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuActionRelations', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'功能名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'功能名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MenuActionRelations', 'COLUMN', N'MenuId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'菜单Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'MenuId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'菜单Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MenuActionRelations',  @level2type = 'COLUMN', @level2name = N'MenuId' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[NotificationTempletes]') AND type IN ('U'))  DROP TABLE [dbo].[NotificationTempletes]
CREATE TABLE [NotificationTempletes] ([Code] nvarchar(50) NOT NULL,[Name] nvarchar(50) NOT NULL,[Content] nvarchar(max) NOT NULL,[Type] int NOT NULL,[IsValid] bit NOT NULL,[Subject] nvarchar(100) NOT NULL,PRIMARY KEY ([Code]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationTempletes', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'消息模板',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'消息模板',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationTempletes', 'COLUMN', N'Code')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'消息编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Code' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'消息编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Code' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationTempletes', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'消息名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'消息名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationTempletes', 'COLUMN', N'Content')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'内容',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Content' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'内容',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Content' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationTempletes', 'COLUMN', N'Type')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'消息类型（0邮件，1短信）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Type' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'消息类型（0邮件，1短信）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Type' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationTempletes', 'COLUMN', N'IsValid')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'IsValid' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'是否有效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'IsValid' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'NotificationTempletes', 'COLUMN', N'Subject')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主题',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Subject' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主题',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'NotificationTempletes',  @level2type = 'COLUMN', @level2name = N'Subject' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Companies]') AND type IN ('U'))  DROP TABLE [dbo].[Companies]
CREATE TABLE [Companies] ([CompanyId] int IDENTITY(1,1) NOT NULL,[CompanyCode] varchar(50) NOT NULL,[ComapnyName] nvarchar(100) NULL,[CompanyNameEn] varchar(100) NULL,[City] int NULL,[BusinessType] varchar(20) NULL,[Address] nvarchar(100) NULL,[ContactUser1] varchar(100) NULL,[ContactUser2] varchar(100) NULL,[PhoneNumber1] varchar(20) NULL,[PhoneNumber2] varchar(20) NULL,[Fax] varchar(20) NULL,[Status] int NULL,[Remark] varchar(1000) NULL,[CompanyManager] uniqueidentifier NULL,[CompanyType] int NULL,[Domain] varchar(100) NULL,[UpperId] int NULL,[Level] int NULL,[SortCode] int NULL,[ShowInMDS] char(1) NULL,[ProcessLevel] int NULL,[ProcessType] int NULL,[FullPathText] nvarchar(500) NULL,[FullPathCode] varchar(1000) NULL,[F1] nvarchar(1000) NULL,[F2] nvarchar(1000) NULL,[F3] nvarchar(10) NULL,[F4] nvarchar(10) NULL,[F5] nvarchar(1000) NULL,PRIMARY KEY ([CompanyId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'公司',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'公司',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'CompanyId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'CompanyCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'ComapnyName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'ComapnyName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'ComapnyName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'CompanyNameEn')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织英文名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyNameEn' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织英文名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyNameEn' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'City')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'城市Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'City' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'城市Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'City' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'BusinessType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'业务类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'BusinessType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'业务类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'BusinessType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'Remark')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'Remark' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'备注',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'Remark' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'CompanyManager')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'负责人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyManager' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'负责人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'CompanyManager' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'UpperId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'父级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'UpperId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'父级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'UpperId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'Level')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'层级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'Level' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'层级',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'Level' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'ProcessLevel')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'流程组织级别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'ProcessLevel' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'流程组织级别',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'ProcessLevel' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'ProcessType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'流程组织类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'ProcessType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'流程组织类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'ProcessType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'FullPathText')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织名称全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'FullPathText' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织名称全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'FullPathText' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Companies', 'COLUMN', N'FullPathCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'FullPathCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Companies',  @level2type = 'COLUMN', @level2name = N'FullPathCode' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[BusinessIdentityAccessGroupDetails]') AND type IN ('U'))  DROP TABLE [dbo].[BusinessIdentityAccessGroupDetails]
CREATE TABLE [BusinessIdentityAccessGroupDetails] ([BusinessIdentityAccessGroupDetailId] uniqueidentifier NOT NULL,[BusinessIdentityAccessGroupId] uniqueidentifier NOT NULL,[PermissionType] int NULL,[FullPathCode] varchar(500) NULL,[FullPathText] nvarchar(500) NULL,[CreateUserID] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserID] uniqueidentifier NULL,[ModifyDate] datetime NULL,[F1] nvarchar(50) NULL,[F2] nvarchar(50) NULL,[F3] nvarchar(50) NULL,[F4] nvarchar(50) NULL,[F5] nvarchar(50) NULL,[F6] nvarchar(50) NULL,PRIMARY KEY ([BusinessIdentityAccessGroupDetailId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'BusinessIdentityAccessGroupDetails', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'行安全性明细',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'BusinessIdentityAccessGroupDetails'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'行安全性明细',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'BusinessIdentityAccessGroupDetails'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[BusinessIdentityAccessGroups]') AND type IN ('U'))  DROP TABLE [dbo].[BusinessIdentityAccessGroups]
CREATE TABLE [BusinessIdentityAccessGroups] ([BusinessIdentityAccessGroupId] uniqueidentifier NOT NULL,[GroupName] nvarchar(50) NULL,[AccessType] int NULL,[Description] nvarchar(max) NULL,[Status] int NULL,[CreateUserID] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserID] uniqueidentifier NULL,[ModifyDate] datetime NULL,[F1] nvarchar(50) NULL,[F2] nvarchar(50) NULL,[F3] nvarchar(50) NULL,[F4] nvarchar(50) NULL,[F5] nvarchar(50) NULL,[F6] nvarchar(50) NULL,PRIMARY KEY ([BusinessIdentityAccessGroupId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'BusinessIdentityAccessGroups', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'行安全性组',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'BusinessIdentityAccessGroups'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'行安全性组',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'BusinessIdentityAccessGroups'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[BusinessIdentityAccessGroupUsers]') AND type IN ('U'))  DROP TABLE [dbo].[BusinessIdentityAccessGroupUsers]
CREATE TABLE [BusinessIdentityAccessGroupUsers] ([BusinessIdentityAccessGroupUsersId] uniqueidentifier NOT NULL,[BusinessIdentityAccessGroupId] uniqueidentifier NOT NULL,[UserID] uniqueidentifier NOT NULL,[CreateUserID] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserID] uniqueidentifier NULL,[ModifyDate] datetime NULL,[F1] nvarchar(50) NULL,[F2] nvarchar(50) NULL,[F3] nvarchar(50) NULL,[F4] nvarchar(50) NULL,[F5] nvarchar(50) NULL,[F6] nvarchar(50) NULL,PRIMARY KEY ([BusinessIdentityAccessGroupUsersId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'BusinessIdentityAccessGroupUsers', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'行安全性人员',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'BusinessIdentityAccessGroupUsers'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'行安全性人员',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'BusinessIdentityAccessGroupUsers'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[CommonRoles]') AND type IN ('U'))  DROP TABLE [dbo].[CommonRoles]
CREATE TABLE [CommonRoles] ([RoleId] uniqueidentifier NOT NULL,[RoleCode] varchar(50) NULL,[RoleType] varchar(10) NULL,[RoleName] nvarchar(255) NULL,[Description] nvarchar(max) NULL,[Status] int NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([RoleId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标准角色',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标准角色',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'RoleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'RoleCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'RoleType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色类型（S系统超级管理员）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色类型（S系统超级管理员）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'RoleName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'RoleName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'Description')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'Description' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'Description' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'1=有效；0=无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'1=有效；0=无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoles', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoles',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[CommonRoleUserRelations]') AND type IN ('U'))  DROP TABLE [dbo].[CommonRoleUserRelations]
CREATE TABLE [CommonRoleUserRelations] ([RoleUserRelationId] uniqueidentifier NOT NULL,[RoleId] uniqueidentifier NULL,[OrgCode] varchar(255) NULL,[UserId] uniqueidentifier NULL,[UserName] nvarchar(255) NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([RoleUserRelationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标准角色人员',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标准角色人员',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'RoleUserRelationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'RoleUserRelationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'RoleUserRelationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'RoleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'OrgCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织Code',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'OrgCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织Code',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'OrgCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'UserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'人员Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'UserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'人员Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'UserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'UserName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'人员名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'UserName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'人员名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'UserName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleUserRelations', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleUserRelations',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[CommonRoleDataRights]') AND type IN ('U'))  DROP TABLE [dbo].[CommonRoleDataRights]
CREATE TABLE [CommonRoleDataRights] ([RoleDataRightId] uniqueidentifier NOT NULL,[RoleId] uniqueidentifier NULL,[Type] int NULL,[DataRights] varchar(max) NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([RoleDataRightId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标准角色数据权限',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标准角色数据权限',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'RoleDataRightId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'RoleDataRightId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'RoleDataRightId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'RoleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'RoleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'RoleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'Type')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'类型（0数据范围，1数据行安全性，2自定义）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'Type' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'类型（0数据范围，1数据行安全性，2自定义）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'Type' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'DataRights')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'数据权限（自定义值 | 数据范围）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'DataRights' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'数据权限（自定义值 | 数据范围）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'DataRights' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRights', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRights',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[CommonRoleDataRightDetails]') AND type IN ('U'))  DROP TABLE [dbo].[CommonRoleDataRightDetails]
CREATE TABLE [CommonRoleDataRightDetails] ([RoleDataRightDetailId] uniqueidentifier NOT NULL,[RoleId] uniqueidentifier NULL,[FullPathCode] varchar(max) NULL,[FullPathText] nvarchar(max) NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([RoleDataRightDetailId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标准角色数据权限明细',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标准角色数据权限明细',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'RoleDataRightDetailId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'RoleDataRightDetailId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'RoleDataRightDetailId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'RoleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'RoleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'RoleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'FullPathCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'FullPathCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'FullPathCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'FullPathText')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'组织名称全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'FullPathText' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'组织名称全路径',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'FullPathText' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleDataRightDetails', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleDataRightDetails',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[CommonRoleMenuRelations]') AND type IN ('U'))  DROP TABLE [dbo].[CommonRoleMenuRelations]
CREATE TABLE [CommonRoleMenuRelations] ([RoleMenuRelationId] uniqueidentifier NOT NULL,[RoleId] uniqueidentifier NULL,[MenuId] uniqueidentifier NULL,[DataRights] varchar(100) NULL,PRIMARY KEY ([RoleMenuRelationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleMenuRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色菜单关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色菜单关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleMenuRelations', 'COLUMN', N'RoleMenuRelationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'RoleMenuRelationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'RoleMenuRelationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleMenuRelations', 'COLUMN', N'RoleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleMenuRelations', 'COLUMN', N'MenuId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'菜单Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'MenuId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'菜单Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'MenuId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleMenuRelations', 'COLUMN', N'DataRights')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'数据权限',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'DataRights' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'数据权限',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuRelations',  @level2type = 'COLUMN', @level2name = N'DataRights' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[CommonRoleMenuActionRelations]') AND type IN ('U'))  DROP TABLE [dbo].[CommonRoleMenuActionRelations]
CREATE TABLE [CommonRoleMenuActionRelations] ([RoleMenuActionRelationId] uniqueidentifier NOT NULL,[RoleId] uniqueidentifier NULL,[MenuId] uniqueidentifier NULL,[ActionCode] varchar(50) NULL,PRIMARY KEY ([RoleMenuActionRelationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'CommonRoleMenuActionRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色菜单功能关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuActionRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色菜单功能关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'CommonRoleMenuActionRelations'
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[TagCommonRoleRelations]') AND type IN ('U'))  DROP TABLE [dbo].[TagCommonRoleRelations]
CREATE TABLE [TagCommonRoleRelations] ([TagCommonRelationId] uniqueidentifier NOT NULL,[TagId] uniqueidentifier NULL,[RoleId] uniqueidentifier NULL,PRIMARY KEY ([TagCommonRelationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TagCommonRoleRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标准角色标签',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标准角色标签',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TagCommonRoleRelations', 'COLUMN', N'TagCommonRelationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations',  @level2type = 'COLUMN', @level2name = N'TagCommonRelationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations',  @level2type = 'COLUMN', @level2name = N'TagCommonRelationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TagCommonRoleRelations', 'COLUMN', N'TagId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标签Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations',  @level2type = 'COLUMN', @level2name = N'TagId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标签Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations',  @level2type = 'COLUMN', @level2name = N'TagId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TagCommonRoleRelations', 'COLUMN', N'RoleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TagCommonRoleRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Matrices]') AND type IN ('U'))  DROP TABLE [dbo].[Matrices]
CREATE TABLE [Matrices] ([MatrixId] uniqueidentifier NOT NULL,[Code] varchar(100) NULL,[Name] nvarchar(255) NULL,[Description] nvarchar(max) NULL,[TableName] varchar(50) NULL,[Status] int NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,[Type] int NULL,PRIMARY KEY ([MatrixId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'MatrixId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'MatrixId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'MatrixId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'Code')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Code' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Code' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'Description')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Description' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Description' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'TableName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'表名',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'TableName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'表名',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'TableName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态（-1草稿，0无效，1已发布）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态（-1草稿，0无效，1已发布）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Matrices', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Matrices',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[MatrixDimensionRelations]') AND type IN ('U'))  DROP TABLE [dbo].[MatrixDimensionRelations]
CREATE TABLE [MatrixDimensionRelations] ([MatrixDimensionRelationId] uniqueidentifier NOT NULL,[MatrixId] uniqueidentifier NULL,[DimensionId] uniqueidentifier NULL,PRIMARY KEY ([MatrixDimensionRelationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixDimensionRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵维度关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵维度关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixDimensionRelations', 'COLUMN', N'MatrixDimensionRelationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations',  @level2type = 'COLUMN', @level2name = N'MatrixDimensionRelationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations',  @level2type = 'COLUMN', @level2name = N'MatrixDimensionRelationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixDimensionRelations', 'COLUMN', N'MatrixId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations',  @level2type = 'COLUMN', @level2name = N'MatrixId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations',  @level2type = 'COLUMN', @level2name = N'MatrixId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixDimensionRelations', 'COLUMN', N'DimensionId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'维度Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations',  @level2type = 'COLUMN', @level2name = N'DimensionId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'维度Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixDimensionRelations',  @level2type = 'COLUMN', @level2name = N'DimensionId' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[MatrixRoleRelations]') AND type IN ('U'))  DROP TABLE [dbo].[MatrixRoleRelations]
CREATE TABLE [MatrixRoleRelations] ([MatrixRoleRelationId] uniqueidentifier NOT NULL,[MatrixId] uniqueidentifier NULL,[RoleId] uniqueidentifier NULL,PRIMARY KEY ([MatrixRoleRelationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixRoleRelations', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵角色关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵角色关系',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixRoleRelations', 'COLUMN', N'MatrixRoleRelationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations',  @level2type = 'COLUMN', @level2name = N'MatrixRoleRelationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations',  @level2type = 'COLUMN', @level2name = N'MatrixRoleRelationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixRoleRelations', 'COLUMN', N'MatrixId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations',  @level2type = 'COLUMN', @level2name = N'MatrixId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations',  @level2type = 'COLUMN', @level2name = N'MatrixId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'MatrixRoleRelations', 'COLUMN', N'RoleId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'角色Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'MatrixRoleRelations',  @level2type = 'COLUMN', @level2name = N'RoleId' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Dimensions]') AND type IN ('U'))  DROP TABLE [dbo].[Dimensions]
CREATE TABLE [Dimensions] ([DimensionId] uniqueidentifier NOT NULL,[Code] varchar(50) NULL,[Name] nvarchar(255) NULL,[Description] nvarchar(max) NULL,[Type] int NULL,[Status] int NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([DimensionId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'矩阵维度',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'矩阵维度',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'DimensionId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'DimensionId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'DimensionId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'Code')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'维度编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Code' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'维度编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Code' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'Name')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'文档名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Name' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'文档名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Name' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'Description')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Description' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Description' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'Type')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'数据来源',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Type' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'数据来源',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Type' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Dimensions', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Dimensions',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Tags]') AND type IN ('U'))  DROP TABLE [dbo].[Tags]
CREATE TABLE [Tags] ([TagId] uniqueidentifier NOT NULL,[ParentId] uniqueidentifier NULL,[TagCode] varchar(50) NULL,[TagName] nvarchar(255) NULL,[Type] varchar(50) NULL,[TypeName] nvarchar(255) NULL,[Description] nvarchar(max) NULL,[OrderIndex] int NOT NULL,[Status] int NOT NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([TagId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标签',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标签',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'TagId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TagId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TagId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'ParentId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标签的父节点',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'ParentId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标签的父节点',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'ParentId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'TagCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标签的编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TagCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标签的编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TagCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'TagName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标签的名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TagName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标签的名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TagName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'Type')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标签分类：组织等级、业态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'Type' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标签分类：组织等级、业态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'Type' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'TypeName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'标签分类：组织等级、业态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TypeName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'标签分类：组织等级、业态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'TypeName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'1=有效；0=无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'1=有效；0=无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Tags', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Tags',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Applications]') AND type IN ('U'))  DROP TABLE [dbo].[Applications]
CREATE TABLE [Applications] ([ApplicationId] uniqueidentifier NOT NULL,[AppCode] varchar(50) NULL,[AppName] nvarchar(50) NULL,[AppType] varchar(50) NULL,[Url] varchar(255) NULL,[Description] nvarchar(255) NULL,[OrderNo] int NULL,[Status] int NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([ApplicationId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'应用',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'应用',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'ApplicationId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'ApplicationId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'ApplicationId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'AppCode')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'应用编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'AppCode' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'应用编码',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'AppCode' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'AppName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'应用名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'AppName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'应用名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'AppName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'AppType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'应用类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'AppType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'应用类型',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'AppType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'Url')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'链接（接入外部系统服务地址）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'Url' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'链接（接入外部系统服务地址）',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'Url' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'Description')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'Description' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'Description' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'OrderNo')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'排序',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'OrderNo' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'排序',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'OrderNo' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'状态',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'Status' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'CreateUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'CreateUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'CreateUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'ModifyUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'ModifyUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Applications', 'COLUMN', N'ModifyDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'ModifyDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'修改时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Applications',  @level2type = 'COLUMN', @level2name = N'ModifyDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[TripartiteSystems]') AND type IN ('U'))  DROP TABLE [dbo].[TripartiteSystems]
CREATE TABLE [TripartiteSystems] ([TripartiteSystemId] uniqueidentifier NOT NULL,[SystemCode] varchar(100) NOT NULL,[Name] nvarchar(100) NOT NULL,[DomainType] varchar(50) NULL,[Domain] varchar(max) NOT NULL,[SecretKey] varchar(255) NULL,[RequestMethod] varchar(255) NULL,[CreateResultURL] varchar(max) NULL,[CreateResultParams] varchar(max) NULL,[AuditURL] varchar(max) NULL,[AuditParams] varchar(max) NULL,[ApproveCloseURL] varchar(max) NULL,[ApproveCloseParams] varchar(max) NULL,[Status] int NULL,[Description] nvarchar(max) NULL,[CreateUserId] uniqueidentifier NULL,[CreateDate] datetime NULL,[ModifyUserId] uniqueidentifier NULL,[ModifyDate] datetime NULL,PRIMARY KEY ([TripartiteSystemId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TripartiteSystems', 'COLUMN', N'RequestMethod')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'get;post',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'RequestMethod' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'get;post',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'RequestMethod' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TripartiteSystems', 'COLUMN', N'CreateResultURL')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'BPM流程发起成功',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'CreateResultURL' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'BPM流程发起成功',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'CreateResultURL' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TripartiteSystems', 'COLUMN', N'AuditURL')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'BPM审批过程中（审批同意、退回发起人、发起人自己撤回），审批记录知会到业务系统',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'AuditURL' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'BPM审批过程中（审批同意、退回发起人、发起人自己撤回），审批记录知会到业务系统',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'AuditURL' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TripartiteSystems', 'COLUMN', N'ApproveCloseURL')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'BPM流程审批结束',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'ApproveCloseURL' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'BPM流程审批结束',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'ApproveCloseURL' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'TripartiteSystems', 'COLUMN', N'Status')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'1=有效；-1=无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'Status' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'1=有效；-1=无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'TripartiteSystems',  @level2type = 'COLUMN', @level2name = N'Status' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[TagOrganizationRelations]') AND type IN ('U'))  DROP TABLE [dbo].[TagOrganizationRelations]
CREATE TABLE [TagOrganizationRelations] ([TagOrganizationRelationId] uniqueidentifier NOT NULL,[TagId] uniqueidentifier NULL,[OrgCode] varchar(36) NULL,PRIMARY KEY ([TagOrganizationRelationId]) )  
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Sequences]') AND type IN ('U'))  DROP TABLE [dbo].[Sequences]
CREATE TABLE [Sequences] ([SequenceId] uniqueidentifier NOT NULL,[FormType] varchar(36) NULL,[FormPrefix] varchar(50) NULL,[FormNo] int NULL,[SequenceType] int NULL,[SequenceLength] int NULL,[Description] nvarchar(200) NULL,[LastUpdateDate] datetime NULL,PRIMARY KEY ([SequenceId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'SequenceId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'SequenceId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'SequenceId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'FormType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'表单类型，如供应商(supplier)',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'FormType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'表单类型，如供应商(supplier)',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'FormType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'FormPrefix')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'单据前缀：如LT',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'FormPrefix' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'单据前缀：如LT',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'FormPrefix' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'FormNo')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'表单流水号：如999',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'FormNo' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'表单流水号：如999',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'FormNo' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'SequenceType')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'序列号类型：0：无更；1：日更；2：月更；3：年更',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'SequenceType' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'序列号类型：0：无更；1：日更；2：月更；3：年更',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'SequenceType' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'SequenceLength')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'序列号长度：如6位，不足时自动补齐',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'SequenceLength' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'序列号长度：如6位，不足时自动补齐',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'SequenceLength' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'Description')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'Description' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'描述',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'Description' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Sequences', 'COLUMN', N'LastUpdateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'最后一笔序列号生成时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'LastUpdateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'最后一笔序列号生成时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Sequences',  @level2type = 'COLUMN', @level2name = N'LastUpdateDate' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[Drafts]') AND type IN ('U'))  DROP TABLE [dbo].[Drafts]
CREATE TABLE [Drafts] ([DraftId] int NOT NULL IDENTITY(1,1),[ProcessId] uniqueidentifier NOT NULL,[Topic] nvarchar(100) NULL,[Summary] nvarchar(50) NULL,[Data] nvarchar(MAX) NULL,[UserId] uniqueidentifier NULL,[UserName] nvarchar(50) NULL,[HaveAttachments] bit NULL,[OwnerUserId] uniqueidentifier NULL,[OwnerUserName] nvarchar(50) NULL,[AddTime] datetime NULL,[UpdateTime] datetime NULL,PRIMARY KEY ([DraftId]) ) 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', NULL, NULL)) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'流程实例草稿',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts'ELSE  EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'流程实例草稿',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts'
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'DraftId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'DraftId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'DraftId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'ProcessId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'流程id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'ProcessId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'流程id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'ProcessId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'Topic')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主题',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'Topic' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主题',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'Topic' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'Summary')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'摘要',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'Summary' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'摘要',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'Summary' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'Data')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'草稿的Json数据',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'Data' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'草稿的Json数据',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'Data' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'UserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'发起人id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'UserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'发起人id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'UserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'UserName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'发起人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'UserName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'发起人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'UserName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'HaveAttachments')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'是否有附件',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'HaveAttachments' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'是否有附件',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'HaveAttachments' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'OwnerUserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'填写人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'OwnerUserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'填写人',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'OwnerUserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'OwnerUserName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'填写人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'OwnerUserName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'填写人名称',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'OwnerUserName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'AddTime')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'添加时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'AddTime' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'添加时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'AddTime' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'Drafts', 'COLUMN', N'UpdateTime')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'更新时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'UpdateTime' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'更新时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'Drafts',  @level2type = 'COLUMN', @level2name = N'UpdateTime' 
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[ApprovalComments]') AND type IN ('U'))  DROP TABLE [dbo].[ApprovalComments]
CREATE TABLE [ApprovalComments] ([ApprovalCommentId] uniqueidentifier NOT NULL,[Title] nvarchar(50) NULL,[Content] nvarchar(255) NULL,[UserId] uniqueidentifier NULL,[UserName] nvarchar(50) NULL,[CreateDate] datetime NULL,[OrderIndex] int NULL,PRIMARY KEY ([ApprovalCommentId]) )  
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ApprovalComments', 'COLUMN', N'ApprovalCommentId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'ApprovalCommentId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'主键',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'ApprovalCommentId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ApprovalComments', 'COLUMN', N'Content')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'内容',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'Content' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'内容',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'Content' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ApprovalComments', 'COLUMN', N'UserId')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'所属用户Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'UserId' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'所属用户Id',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'UserId' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ApprovalComments', 'COLUMN', N'UserName')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'所属用户姓名',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'UserName' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'所属用户姓名',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'UserName' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ApprovalComments', 'COLUMN', N'CreateDate')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'CreateDate' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'创建时间',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'CreateDate' 
IF ((SELECT COUNT(*) from fn_listextendedproperty('MS_Description', 'SCHEMA', N'', 'TABLE', N'ApprovalComments', 'COLUMN', N'OrderIndex')) > 0) EXEC sp_updateextendedproperty @name = N'MS_Description', @value = N'是否无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'OrderIndex' ELSE EXEC  sp_addextendedproperty @name = N'MS_Description', @value = N'是否无效',  @level0type = 'SCHEMA', @level0name = N'dbo',  @level1type = 'TABLE', @level1name = N'ApprovalComments',  @level2type = 'COLUMN', @level2name = N'OrderIndex' 

/* 初始化数据 UserId ********-17E8-4D9A-8AB3-E055C366DC62 */
INSERT INTO [dbo].[Users] ([UserId], [UserLoginId], [UserName], [UserAlias], [Password], [UserType], [SourceType], [Source], [FirstName], [MiddleName], [LastName], [PinyinFirstWord], [Gender], [Email], [Emailbake], [BirthDay], [Status], [MobilePhone], [Extension], [Company], [EmployeeType1], [CostCenterDesc], [Remark], [CreateUserId], [CreateDate], [MidifyUserId], [ModifyDate], [LastestLoginDate], [F1], [F2], [F3], [F4], [F5], [F6], [SubCompanyId], [PasswordLockTime], [NoLoginLockTime], [ApproveDate], [LockStatus], [InstanceId], [WorkNumber], [IsStoreRequest], [SortCode], [UpperUserId], [Grade], [JobTitle], [CompanyId], [OrganizationId], [FullPathText], [FullPathCode], [CompanyName], [OrganizationName]) VALUES ('********-17E8-4D9A-8AB3-E055C366DC62', 'admin', N'管理员', 'admin', 'fCIvspJ9goryL1khNOiTJIBjfA0=', 'E', 'B', N'', '', '', '', '', 'M', '<EMAIL>', '<EMAIL>', NULL, 1, '18506258811', '', NULL, N'E', N'', N'', '********-17E8-4D9A-8AB3-E055C366DC62', NULL, '********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, N'', N'', N'', N'', N'', N'', 950, NULL, NULL, NULL, 0, '', '2345', NULL, NULL, NULL, NULL, N'部门经理', 1, 1, N'测试公司_测试部门', '51BDBAD5-88E6-4474-BA70-D88EB00B2434_D9B5A723-A0B3-4473-95BE-971C9A6C3C47', N'测试公司', N'测试部门');
INSERT INTO [dbo].[Users] ([UserId], [UserLoginId], [UserName], [UserAlias], [Password], [UserType], [SourceType], [Source], [FirstName], [MiddleName], [LastName], [PinyinFirstWord], [Gender], [Email], [Emailbake], [BirthDay], [Status], [MobilePhone], [Extension], [Company], [EmployeeType1], [CostCenterDesc], [Remark], [CreateUserId], [CreateDate], [MidifyUserId], [ModifyDate], [LastestLoginDate], [F1], [F2], [F3], [F4], [F5], [F6], [SubCompanyId], [PasswordLockTime], [NoLoginLockTime], [ApproveDate], [LockStatus], [InstanceId], [WorkNumber], [IsStoreRequest], [SortCode], [UpperUserId], [Grade], [JobTitle], [CompanyId], [OrganizationId], [FullPathText], [FullPathCode], [CompanyName], [OrganizationName]) VALUES ('78C35188-FCED-4E06-899F-00B84B499D8A', 'eric.cheng', N'程国强', NULL, 'fCIvspJ9goryL1khNOiTJIBjfA0=', 'E', NULL, NULL, NULL, NULL, NULL, NULL, 'M', '<EMAIL>', NULL, NULL, 1, '18506258811', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'SZ1005', NULL, NULL, NULL, NULL, N'部门经理', NULL, NULL, N'测试公司_测试部门', '51BDBAD5-88E6-4474-BA70-D88EB00B2434_D9B5A723-A0B3-4473-95BE-971C9A6C3C47', N'测试公司', N'测试部门');
INSERT INTO [dbo].[CommonRoles] ([RoleId], [RoleCode], [RoleType], [RoleName], [Description], [Status], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('DF5E0733-0C24-4C77-9C69-6DDBDC0A6765', 'superadmin', '1', N'管理员', N'管理员', 1, '********-17E8-4D9A-8AB3-E055C366DC62', '2020-02-11 21:59:14.613', '********-17E8-4D9A-8AB3-E055C366DC62', '2020-02-11 23:10:24.180');
INSERT INTO [dbo].[CommonRoleUserRelations] ([RoleUserRelationId], [RoleId], [OrgCode], [UserId], [UserName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('C0BC407C-E91B-427A-8605-F7125FBCEDFD', 'DF5E0733-0C24-4C77-9C69-6DDBDC0A6765', NULL, '********-17E8-4D9A-8AB3-E055C366DC62', NULL, '********-17E8-4D9A-8AB3-E055C366DC62', '2020-05-11 09:26:06.527', NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('7E7E7017-878D-4B69-9823-03398A257AC8', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('2F02B81C-3B37-4F76-A3F8-03AC5D81799B', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '0', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('DC0C1DE4-3D2A-4BBF-97A2-066E59EF9F6F', 'EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', 'M', 3, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('E1D11C8A-E65A-4C32-9746-092975234FE6', '3AF7A7CB-038A-43AC-8355-F816D9842D38', '', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('90550F12-0E7E-42BE-B337-0D16AD53B4F8', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 6, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('36F88F01-31B8-4298-83E7-199132D3AD54', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('074B78B5-FFF9-4DBD-BC54-1A36D11886F0', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 7, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('E74E3EB7-81FE-42A2-B1A9-2438933DFD65', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('76A659F8-953F-4772-AAA2-24494AF3CC3D', 'C68424D6-C305-439A-A95A-3308163F8249', 'SearHR', 'M', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('D69022EA-19C7-462F-B734-24547264E2E0', '76A659F8-953F-4772-AAA2-24494AF3CC3D', 'SearHR', 'M', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('9FA103D2-7D34-4D24-BD28-293707C6D2A3', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('06CC11DD-B167-4617-A236-2E62AA76EA3D', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('8EC42A85-FEBD-4E31-B31F-3162C893D80F', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 11, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('BC733AC8-CFB2-4A52-B14F-325B9245D3EB', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('8169D475-C314-4608-999A-380CB12DBD52', 'C68424D6-C305-439A-A95A-3308163F8249', 'SearHR', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('39C79F2C-23E0-476D-AFAD-3926825D75E9', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 3, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('EDE658D2-9BB8-4DEE-977A-3DF9B809459C', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('A662CC1C-B11E-4F60-93DA-3E233C812017', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('716909B6-5C2F-4D99-902A-418A01DDA3A4', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('EA346FA6-A3E8-40DB-AD80-44C307BEEB93', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('0BAC816E-1F7E-40F4-A52D-45AD1BD95520', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('B9600A6E-7BCE-4E21-8847-463F3DE5630E', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 6, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('8CD91884-99C1-4C8A-804C-47706DF4D5E0', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('D576B728-C6F9-4B31-974B-48845FEB7A08', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('598250C1-9660-4F37-8A80-498E53274D50', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('07FA5C19-E6F4-417D-AF44-49A23ADC047E', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('BA9A7501-4C5E-4479-9420-4A105EF32A1B', '8169D475-C314-4608-999A-380CB12DBD52', 'HR', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('554A8F8D-9A26-46DC-8D55-4E204D153E3C', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('AA4F489E-AC04-4DF8-81BD-4F55C16AB08A', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('E3EF14D5-DBAE-4511-8F0D-530E034BD484', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('AFF2C0FC-D19B-48B5-8CA8-54A881472A74', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 0, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('352999C3-F6CA-43FF-9C0D-5CCF331CB45A', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('D56E4281-C515-4279-B0D0-64051C2CC160', '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('9A36430A-29D6-4A55-8614-68DEC1B5F77C', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('DBE944EB-46F8-4B1C-B0C0-6C0FB29D9F45', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('99C0DFA2-B13A-4AAB-BA1A-6C780D4100C6', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('6101692B-2AC2-40E7-B89C-6D8B0D0DA72F', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 8, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('32D2F95C-F32E-477F-9465-714007679B74', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('8D9720E2-A342-4C1A-A785-794F6EC8E398', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('97A75EBF-CAD7-4BE8-8494-7D0D3DBF527B', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 12, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('FB7E6186-407E-440B-AE56-7D6D416F9242', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('7940BD39-5868-4944-8CDB-7D8E305DE8AC', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('7D084C4C-7701-49DC-A260-7ED72FE50038', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('07C03644-FF61-49E4-B3AB-82F89B344F13', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('35F102C9-C959-468B-8C0F-83BCA83E78AE', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('FAB88C3A-BBDA-4F63-8A1B-8AA064CD1563', '3DB82C0E-4DB3-4042-8866-1FE07CF1B24C', 'platform', 'M', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', 'M', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('DD2530DD-DCCD-4962-A722-9912E99466B7', '8169D475-C314-4608-999A-380CB12DBD52', 'HR', 'M', 0, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('3F500497-8864-4DF9-B63D-991D74464830', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', -1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('CEF77C99-C116-4892-AA04-993BFB42B67D', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('D7659DFA-6A1B-41D5-91F3-995D9D181365', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 7, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('9C9AF639-BED1-46FC-A6E8-9B88BCB6E5B3', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('63C4302E-BDC6-443B-AC72-9DC602CA3452', 'DD2530DD-DCCD-4962-A722-9912E99466B7', 'HR', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('092CDAD4-B6A6-4FD3-9BFE-9F89B798164C', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('DCCA2676-8912-41F8-BC0F-A03FB9480256', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('23938B57-BA3B-426B-888A-A857CAF9785C', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 6, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('82626B0F-D436-4799-ABF5-AE968383EA85', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('BEAA4AD1-31FC-4BEB-8C03-B42D6084A97C', '0AAB450E-C442-4719-BAC0-F10409110918', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('3D1820CF-B900-49A2-983B-BA46CD064EE5', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('2973735E-AE52-4D99-9C73-BA6452E7652D', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', 'M', 2, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('A56EC975-F98C-4271-A77A-BF6CF73F1BD8', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 1, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('177E7D55-1C10-4266-9466-BFA652E5D84F', '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', 'platform', 'P', 3, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('C9D81832-BA20-42E9-B85C-C057A71E07FC', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 12, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('50126FFF-813F-40BC-B872-C39886999FD5', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 3, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('20E76C1D-9EE1-4D60-ABA9-C62B20D0E47C', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 11, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('598E1FF9-C622-4FBF-9DE8-C701D1BFA37F', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('32F564B4-8E4D-4FA4-BDCA-C813670ABBBE', 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('EC748F3D-339B-406C-BC44-C8CEC80039F9', '90550F12-0E7E-42BE-B337-0D16AD53B4F8', 'platform', 'P', NULL, NULL, NULL, '0', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('F8C98398-FA5F-49A0-A364-C9FE7E2F948C', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('232FE2FC-E919-48FC-A83B-CC7D88D3143B', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('9C5CFF6C-B264-4A2B-B442-D321C97E3A6A', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('001A1C91-5FC4-4682-83F0-D6B5336E5E4B', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 10, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('442070C9-43DE-46DD-9830-DC9C7E778F8F', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('E418D5F9-1327-48E9-88AB-DF66595D7BAF', '49E23799-3147-43B7-B5D9-ED179E659275', 'platform', 'P', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('806B13FB-B241-41C0-8A16-E16728B29118', '3AF7A7CB-038A-43AC-8355-F816D9842D38', 'platform', 'P', 13, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('975DABBB-E2AA-47D9-8B93-E30C17049281', '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', 'platform', 'P', 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 8, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('F06EBB4D-A288-4289-B4EC-E6BB9DBDC1E2', '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', 'platform', 'P', 6, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('664E9B28-ED2B-4B16-958F-E950EC102536', 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', 'HR', 'P', 0, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('E0BE4248-A0D1-4C8F-A185-E95774198907', '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', 'platform', 'P', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('49E23799-3147-43B7-B5D9-ED179E659275', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 5, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('901874E1-3867-4B06-832E-ED772758E9C4', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('0AAB450E-C442-4719-BAC0-F10409110918', 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', 'platform', 'M', 1, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('89269868-9E00-43F8-8493-F18A450C49E9', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 3, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('D05AFCCA-7425-43A2-AF9A-F3384E9206F2', 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', 'platform', 'P', 0, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('6DAAA5D9-D3AD-4456-88F4-F3A2631A8833', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 3, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('020A9025-C7F7-46EF-8030-F40F14DB6D72', '3D1820CF-B900-49A2-983B-BA46CD064EE5', 'platform', 'P', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('435851EA-72D0-4FB9-B391-F74907772790', '9A36430A-29D6-4A55-8614-68DEC1B5F77C', 'platform', 'P', 2, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('3AF7A7CB-038A-43AC-8355-F816D9842D38', '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', 'platform', 'M', 2, '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '1');
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2', 'FB7E6186-407E-440B-AE56-7D6D416F9242', 'platform', 'P', 4, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', 'DC0C1DE4-3D2A-4BBF-97A2-066E59EF9F6F', 'platform', 'M', NULL, NULL, NULL, '1', NULL, NULL, 1, NULL, NULL, NULL);
INSERT INTO [dbo].[menus] ([MenuId], [ParentId], [AppCode], [PageType], [OrderNo], [DataRights], [OpenType], [InUse], [MODULERIGHTS], [MODIFYCOLUMNLASTOFINDEX], [DELETECOLUMNLASTOFINDEX], [F1], [F2], [F3]) VALUES ('0D8341FA-F696-4C94-A85C-FF2030D214B5', '32D2F95C-F32E-477F-9465-714007679B74', 'platform', 'P', 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('C712CE5A-2C64-4AE2-8EE9-00C5A651022C', N'流程测试', 'Process Test', NULL, 'AE33CC11-96D2-43C1-9B07-FCD04E8B5CE2', NULL, 'process-test', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('24D10151-1FC6-4ED4-9B4B-00FCF9BD90D8', N'流程编辑', 'edit', NULL, '2973735E-AE52-4D99-9C73-BA6452E7652D', '/bpm/process-management/process-list/edit/edit.module.ts', 'process-list/edit', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('0A042517-7977-47BA-98B2-02968DA61684', N'日志列表', 'Logs', NULL, 'EC748F3D-339B-406C-BC44-C8CEC80039F9', '/platform/log-management/log/log.module.ts', 'log', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('89FCE1A1-F9A0-44D3-8371-04382726D0E6', N'用户详情', 'User Detail', NULL, '554A8F8D-9A26-46DC-8D55-4E204D153E3C', '/platform/user-management/user/query/query.module.ts', 'user/query', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('065FCFB9-A080-417E-AFD9-064E8394E5A9', N'日志中心', 'Log Management', NULL, '90550F12-0E7E-42BE-B337-0D16AD53B4F8', NULL, 'log-management', '0', 'file', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('D657FF15-E2E8-4020-9ACE-08775B61CD5E', N'测试1', 'test1', NULL, '76A659F8-953F-4772-AAA2-24494AF3CC3D', NULL, 'test1', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('8D2904E5-D613-4B72-BA4F-0896EC7BF131', N'表单列表', 'Form List', NULL, '07FA5C19-E6F4-417D-AF44-49A23ADC047E', '/bpm/form-management/form/form.module.ts', 'form', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('D390DA33-B64E-44C6-A1AE-0B46E0ED0A6B', N'人员中心', 'Home', NULL, 'BA9A7501-4C5E-4479-9420-4A105EF32A1B', NULL, 'home', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('C861175E-B72C-4A84-8592-1111525BC4BD', N'标准角色', 'Role Management', NULL, '3AF7A7CB-038A-43AC-8355-F816D9842D38', NULL, 'common-roles', '0', 'user', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('3FB3B7AE-94BD-43D0-940D-12ED5FBB7471', N'角色库', 'Role', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A', '/platform/user-management/role/index/index.module.ts', 'role', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('68EB8BAF-800D-451B-A0A0-13C066422F51', N'前台菜单管理', 'Front Menu Message', NULL, 'B239A85C-5A96-47D8-A8F1-0EFE0010BFE0', '/platform/system-management/front-menu/front-menu.module.ts', 'front-menu', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('1A5CFE11-E39F-4B96-BC66-1AC35977F820', N'测试模型', 'Test Model', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8', '/automator/process-test-model/index/index.module.ts', 'test-model', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('F1D78727-533A-4986-A073-1E5B62E9DD84', N'业务事项流程图', 'Process List', NULL, 'E1B3660A-EE2E-42A8-B320-C86B8FBFF4D4', '/bpm/process-management/process-list/index/index.module.ts', 'process-list', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('11BAD300-DB1F-4C0C-A953-1E963DE2E751', N'测试任务', 'Test Task', NULL, '32F564B4-8E4D-4FA4-BDCA-C813670ABBBE', '/automator/process-test-result/index/index.module.ts', 'test-result', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('B30B428A-12F6-43DA-A725-20952D6296B7', N'部门管理', 'Department', NULL, '435851EA-72D0-4FB9-B391-F74907772790', '/platform/organization-management/department/department.module.ts', 'department', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('F6A428AA-148F-4C93-A61F-21F81FF7D112', N'外部业务对象（待实现）', 'External Business', NULL, '177E7D55-1C10-4266-9466-BFA652E5D84F', '1', 'external-business', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('0469B2F4-CC2F-41CC-B936-22EF84FCD99C', N'业务管理', 'Business Management', NULL, '32D2F95C-F32E-477F-9465-714007679B74', NULL, 'business-management', '0', 'sync', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('33F40454-1D62-48CF-A055-310E3AE4EA66', N'流程查询（待实现）', 'Process', NULL, '020A9025-C7F7-46EF-8030-F40F14DB6D72', '1', 'process', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('355A0A49-4DE1-4711-8E4D-343F0A19F96B', N'用户详情', 'User Detail', NULL, '9C9AF639-BED1-46FC-A6E8-9B88BCB6E5B3', '/platform/user-management/user/query/query.module.ts', 'user/query', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('BFB3A428-F9A2-4981-99E4-************', N'创建测试模型', 'Create Model', NULL, '2F02B81C-3B37-4F76-A3F8-03AC5D81799B', '/automator/process-test-model/create-model/create-model.module.ts', 'test-model/create', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('2F6F3219-5CA9-4A55-995F-3653997CC17E', N'数据库对象（待实现）', 'Database Management', NULL, 'E3EF14D5-DBAE-4511-8F0D-530E034BD484', '1', 'database', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('F39E7E3D-05AF-4309-8262-384D3B80D87F', N'权限管理（待实现）', 'Rights Management', NULL, 'AFF2C0FC-D19B-48B5-8CA8-54A881472A74', '/platform/system-management/role/index/index.module.ts', 'rights', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('87E61F5B-8755-4022-B74C-3B70D5D1C86A', N'数据行安全性', 'Data Security', NULL, 'E1D11C8A-E65A-4C32-9746-092975234FE6', '/platform/user-management/data-security/data-security.module.ts', 'data-security', '1', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('2D741187-316B-4287-855F-403E54779E94', N'初始化配置（待实现）', 'Init Management', NULL, '23938B57-BA3B-426B-888A-A857CAF9785C', '1', 'init', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('8D05E41F-F4D1-4C11-8234-4076E9EEC7EF', N'矩阵角色（待实现）', 'Matrix Role Management', NULL, 'E74E3EB7-81FE-42A2-B1A9-2438933DFD65', '1', 'matrix-role', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('EDA9B0DE-AD67-4558-963F-4A400F997265', N'矩阵配置人员', 'Matrix User Setting', NULL, '598250C1-9660-4F37-8A80-498E53274D50', '/platform/user-management/matrix/user-setting/user-setting.module.ts', 'matrix/user-setting', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('EE19E9DE-7A47-420C-B7EA-4AB2B45A93EF', N'模板添加', 'Templete Add', NULL, '9FA103D2-7D34-4D24-BD28-293707C6D2A3', '/platform/message-management/templete/add/add.module.ts', 'templete/add', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('F6C84E6E-C382-496B-AC44-5558DB681CA2', N'矩阵编辑', 'Matrix Edit', NULL, 'F06EBB4D-A288-4289-B4EC-E6BB9DBDC1E2', '/platform/user-management/matrix/edit/edit.module.ts', 'matrix/edit', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('C47E425A-FD82-4FAE-A8EB-558806E50B6F', N'运维管理', 'Maintain Management', NULL, '3D1820CF-B900-49A2-983B-BA46CD064EE5', NULL, 'maintain-management', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('12F5585D-A609-401E-80BF-567152569BD3', N'消息规则', 'Message Rule', NULL, '8CD91884-99C1-4C8A-804C-47706DF4D5E0', '/platform/message-management/rule/index/index.module.ts', 'rule', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('3638E657-675D-4C11-8840-58CDF00B1555', N'应用管理（新）', 'App Management', NULL, '975DABBB-E2AA-47D9-8B93-E30C17049281', '/platform/system-management/application/application.module.ts', 'application', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('24C8A24F-3E9E-473F-93A9-5A9CF20B20A0', N'流程管理', 'One Stop Service', NULL, '442070C9-43DE-46DD-9830-DC9C7E778F8F', '/bpm/one-stop-management/index/index.module.ts', 'one-stop-service', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('0648C465-160A-45EF-92FB-5B2BFEF8EA21', N'流程管理', 'Process', NULL, 'B3E2ACB2-3FE6-4182-B8FD-BE9EBF096B30', NULL, 'process', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('E17E1696-525A-4E69-9291-5B3F8D2027A5', N'业务事项分类', 'Business Category', NULL, '26631364-A83B-4FBC-BAA6-DC6ABFB716E8', '/bpm/process-management/process-category/index/index-process-category.module.ts', 'process-category', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('EBA1FB4B-1D43-429F-ACD8-5E8FF5E9B540', N'前台角色成员', 'front-role/member', NULL, '35D2BCA4-DF31-438D-9DC8-0F7177E8032A', '/platform/user-management/front-role/member/member.module.ts', 'front-role/member', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('DEA547BC-A045-4D37-9987-5FE63C7BF7AE', N'表单模型编辑', 'edit', NULL, '99C0DFA2-B13A-4AAB-BA1A-6C780D4100C6', '/bpm/form-management/model/form-edit/form-edit.module.ts', 'model/form-edit', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('F797B5A3-0B6A-45F1-9128-689A522FE5B1', N'业务事项列表', 'business-matters', NULL, 'B31A9167-8116-4C70-AAF1-BB2A03E0FF29', '/bpm/process-management/business-matters/business-matters.module.ts', 'business-matters', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('78068484-B28A-49EF-AF40-6A53FAD115A5', N'角色匹配人员', 'Common Role Users', NULL, '20E76C1D-9EE1-4D60-ABA9-C62B20D0E47C', '/platform/user-management/role/member/member.module.ts', 'role/member', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('A7795768-9B30-4791-945E-72ED74398DE7', N'标签列表', 'Tag List', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF', '/platform/tag-management/tag/tag.module.ts', 'tag', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('7E80638E-F1D8-498C-BD0E-742AE7F7B137', N'矩阵管理', 'Matrix', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13', '/platform/user-management/matrix/index/index.module.ts', 'matrix', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('8975A57D-CA00-4092-BAFF-7610627D9B6F', N'流程分类', 'Process Category', NULL, '901874E1-3867-4B06-832E-ED772758E9C4', '/bpm/process-management/process-category/index/index-process-category.module.ts', 'process-category', '1', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('74FAD1A6-2ADE-4EF5-9341-7618C8501D1D', N'菜单配置', 'Menus', NULL, '6101692B-2AC2-40E7-B89C-6D8B0D0DA72F', '/platform/system-management/role/menu/menu.module.ts', 'role/menu', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('9CBC3FC5-0EE5-4DE2-8A5B-766CDC323B08', N'异常统计（待实现）', 'Abnormal Report', NULL, 'DBE944EB-46F8-4B1C-B0C0-6C0FB29D9F45', '1', 'abnormal-report', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('B8A95979-6F08-4347-9EA9-76D22F8A7304', N'矩阵管理', 'Matrix Management', NULL, '358B1FC1-7B7E-41C0-AB99-3A28C75D55A6', NULL, 'matrix-management', '0', 'switcher', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('3E7BA1D6-7864-4077-BFD6-776FA2CCFBB2', N'消息中心', 'Message Management', NULL, 'B9600A6E-7BCE-4E21-8847-463F3DE5630E', NULL, 'message-management', '0', 'mail', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('6E051E84-EF86-41B3-995E-7B29D668FF14', N'按角色', 'Match Common Role', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F', '/platform/user-management/common-role/match-role/match-role.module.ts', 'match-common-role', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('AF04087F-9318-42E9-841B-7F5055B476C5', N'按业态（待实现）', 'Format', NULL, '97A75EBF-CAD7-4BE8-8494-7D0D3DBF527B', '1', 'format', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('655FB751-8F7F-47B0-9847-7F515B42B713', N'矩阵配置人员', 'Matrix User Setting', NULL, '806B13FB-B241-41C0-8A16-E16728B29118', '/platform/common_roles/matrix/user-setting/user-setting.module.ts', 'matrix/user-setting', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('8EAB320D-1516-42CB-AEAC-8737B5AA7FD9', N'流程列表', 'Process List', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A', '/bpm/process-management/process-list/index/index.module.ts', 'process-list', '0', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('E018A612-0856-42D6-A975-8AA5186CBDA8', N'异常流程（待实现）', 'Abnormal process', NULL, '7940BD39-5868-4944-8CDB-7D8E305DE8AC', '1', 'abnormal', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('8C684C61-5DAC-4925-B521-8B0CB0394F4B', N'业务管理', 'Business Management', NULL, '50031EBE-FEC9-41D9-B072-F050CC59119B', NULL, 'business-management', '0', 'profile', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('78D44D57-C0D4-46DE-8767-8B3A1B9E5908', N'表单管理', 'Form Management', NULL, '0AAB450E-C442-4719-BAC0-F10409110918', NULL, 'form-management', '0', 'profile', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('00F993ED-2338-4C39-BB94-8D7FCFF2552F', N'公司管理', 'Company', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398', '/platform/organization-management/company/company.module.ts', 'company', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('CAFB8D3A-5FE1-43C9-AAF6-8DAB6A70FF71', N'外部系统注册', 'External System', NULL, 'D56E4281-C515-4279-B0D0-64051C2CC160', '/platform/system-management/external-system/external-system.module.ts', 'external-system', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('C0591CD2-5DA0-4019-95CD-8E2BED365140', N'业务类型', 'Business Type', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5', '/bpm/business-management/business-type/business-type.module.ts', 'business-type', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('39AF66DC-E3D5-416D-AD2B-8E4FC7EBB668', N'流程图', 'Process Design', NULL, '7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2', '/bpm/process-design/bpmn-designer/bpmn-designer.module.ts', 'process-design', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('5C7BEC0F-AC8F-4CD6-A096-922BD2CE4EFC', N'业务事项流程图编辑', 'Bpmn Designer', NULL, '343E4B8D-77BF-4FEA-A217-32522933BB01', '/bpm/process-design/bpmn-designer/bpmn-designer.module.ts', 'bpmn-designer', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('F96C9085-6CA3-40D6-8196-************', N'自动化测试', 'Automator', NULL, 'DC0C1DE4-3D2A-4BBF-97A2-066E59EF9F6F', NULL, 'automator', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('39FCE678-759D-42BB-B78A-9F5E316D0564', N'菜单管理', 'Menu Management', NULL, '3F500497-8864-4DF9-B63D-991D74464830', '/platform/system-management/menu/menu.module.ts', 'menu', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('72D34042-A7E4-447F-9C7B-A6CC52754836', N'系统管理', 'System Management', NULL, '074B78B5-FFF9-4DBD-BC54-1A36D11886F0', NULL, 'system-management', '0', 'setting', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('18E60BDC-E3C9-416E-B64D-A99864309F18', N'后台角色成员', 'Role Member', NULL, 'D576B728-C6F9-4B31-974B-48845FEB7A08', '/platform/user-management/role/member/member.module.ts', 'role/member', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('174BBE80-C6A0-4737-A4A1-AA701D4313C5', N'测试2', 'test2', NULL, 'D69022EA-19C7-462F-B734-24547264E2E0', NULL, 'test2', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('850B2B08-40F8-4C04-A8E5-ADF2CE21226E', N'组织结构', 'Organization Management', NULL, '9A36430A-29D6-4A55-8614-68DEC1B5F77C', NULL, 'organization-management', '0', 'team', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('83515BF1-6AA7-423E-A74D-AF36E99FD50F', N'平台管理', 'Platform', NULL, '165DAC5C-9E7E-48F5-8FB6-945A8725E0BD', NULL, 'platform', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('73D6EE01-8F88-4184-AFF3-AFE9623C215F', N'流程设置', 'Process Settings', NULL, 'FB7E6186-407E-440B-AE56-7D6D416F9242', NULL, 'process-management', '0', 'setting', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('6B436DE0-B168-4986-930D-BA58597B715B', N'岗位管理', 'Position', NULL, '50126FFF-813F-40BC-B872-C39886999FD5', '/platform/organization-management/position-level/position-level.module.ts', 'position', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('AF9D1ABC-AF92-49E0-8A83-BD5D5BC2CF1D', N'培训管理', 'Training', NULL, 'DD2530DD-DCCD-4962-A722-9912E99466B7', NULL, 'training', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('6191D326-DC0D-4E84-882C-BDC17A93F7FB', N'人员配置', 'Users', NULL, 'D7659DFA-6A1B-41D5-91F3-995D9D181365', '/platform/system-management/role/member/member.module.ts', 'role/member', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('65504D04-42A0-43CC-944D-BF10CA6DAF11', N'业务对象', 'Business Object', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017', '/bpm/business-management/business-object/business-object.module.ts', 'business-object', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('6A2E175F-BDE7-411F-B78A-C0AB60953AB2', N'表单模型', 'Form Model', NULL, '0BAC816E-1F7E-40F4-A52D-45AD1BD95520', '/bpm/form-management/model/index/index.module.ts', 'model', '1', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('3D66597D-D730-4B9A-872A-C0E7C0AD0A18', N'矩阵新增', 'Add', NULL, 'C9D81832-BA20-42E9-B85C-C057A71E07FC', '/platform/user-management/matrix/add/add.module.ts', 'matrix/add', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('3B47E81F-38CA-4486-9618-C68587A2515A', N'消息模板', 'Message Template', NULL, 'DCCA2676-8912-41F8-BC0F-A03FB9480256', '/platform/message-management/templete/index/index.module.ts', 'templete', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('7C5A0008-9DDF-4B1E-9555-C7B1E7905389', N'前台角色权限', 'front-role', NULL, '8D86D709-2A28-4FE3-8EC5-7F868195670E', '/platform/user-management/front-role/index/index.module.ts', 'front-role', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('D85ACB0F-E01A-483F-8FBB-C8AAAA882E89', N'服务器信息（待实现）', 'Server Management', NULL, 'E0BE4248-A0D1-4C8F-A185-E95774198907', '1', 'Server', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('59BC1A5E-4769-41EE-B62C-CBDD227814C1', N'业务对象明细', 'Business Object Query', NULL, '092CDAD4-B6A6-4FD3-9BFE-9F89B798164C', '/bpm/business-management/business-object/query/query.module.ts', 'business-object/query', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('22986C9F-CFD2-43CF-BAE7-CD1911C61BBE', N'系统集成', 'System Integration', NULL, '91D01897-FC9A-4054-9BDA-E5BCE4BF9A8A', NULL, 'Integration', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('69A4D3FD-DC2D-446D-86C7-CDF3F4CC4474', N'矩阵新增', 'Add', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9', '/platform/user-management/matrix/add/add.module.ts', 'matrix/add', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('03F6CD30-6545-42DA-BA83-D374DB696B4C', N'标签管理', 'Tag Management', NULL, '49E23799-3147-43B7-B5D9-ED179E659275', NULL, 'tag-management', '0', 'tags', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('061002F4-093B-4729-A988-D52470FFFBB8', N'维度管理', 'Dimension', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB', '/platform/user-management/matrix/dimension/dimension.module.ts', 'dimension', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('A97F4A15-F7D2-46F2-9E17-D6AD446AB7F6', N'规则编辑', 'Rule Edit', NULL, '232FE2FC-E919-48FC-A83B-CC7D88D3143B', '/platform/message-management/rule/edit/edit.module.ts', 'rule/edit', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('AD9E7431-4775-4E93-87C8-D884F7EE2DA8', N'三级菜单测试', 'third page test', NULL, 'EA346FA6-A3E8-40DB-AD80-44C307BEEB93', NULL, 'test', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('9B55949F-2FDC-4328-AB91-DA3BC19F796D', N'代理管理（待实现）', 'Agent', NULL, '6DAAA5D9-D3AD-4456-88F4-F3A2631A8833', '1', 'agent', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('50DE04EF-A8EA-4D12-8315-DAE56CE50E19', N'表单模型查看', 'query', NULL, 'BEAA4AD1-31FC-4BEB-8C03-B42D6084A97C', '/bpm/form-management/model/query/query.module.ts', 'model/query', '1', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('30C4AC62-42FB-4560-9032-E16CEA1F23B0', N'人员管理', 'User Management', NULL, '598E1FF9-C622-4FBF-9DE8-C701D1BFA37F', '/platform/user-management/user/index/index.module.ts', 'users', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('193C46D8-CBA5-4BF8-9C1D-E3A83387E57A', N'前台角色菜单', 'front-role/menu', NULL, '67843A29-23A5-41BE-A60E-D0A26D5602B9', '/platform/user-management/front-role/menu/menu.module.ts', 'front-role/menu', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('52CCF575-0F27-46F7-A05F-E596EA8FB959', N'按组织', 'Common Role', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B', '/platform/user-management/common-role/role-user/common-role-setting.module.ts', 'common-role-user', '0', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('4DA3CD9D-D6BF-4915-9E75-E67273DCD9E8', N'模板编辑', 'Templete Edit', NULL, 'F8C98398-FA5F-49A0-A364-C9FE7E2F948C', '/platform/message-management/templete/edit/edit.module.ts', 'templete/edit', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('21DD1A8D-A61C-40C0-89CB-EE7D5FD9810A', N'首页', 'HR', NULL, '8169D475-C314-4608-999A-380CB12DBD52', NULL, 'business-test1', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('597D62EF-4D81-4588-9D51-F3E7BF6B3230', N'数据字典', 'Dictionary', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8', '/platform/system-management/dictionary/dictionary.module.ts', 'dictionary', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('4E56C480-00A0-48FF-A508-F42C34349F60', N'表单模型添加', 'add', NULL, '82626B0F-D436-4799-ABF5-AE968383EA85', '/bpm/form-management/model/edit/edit.module.ts', 'model/edit', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('0E51C9FB-6737-4E82-AA1D-F4C8B9BBE512', N'规则添加', 'Rule Add', NULL, 'D05AFCCA-7425-43A2-AF9A-F3384E9206F2', '/platform/message-management/rule/add/add.module.ts', 'rule/add', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('48E4B6BB-F119-4BC9-9865-F64D2CE9CADA', N'模型管理', NULL, NULL, 'FAB88C3A-BBDA-4F63-8A1B-8AA064CD1563', NULL, 'form', NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('165E5E02-2453-4C7F-87C9-F9B991999291', N'后台角色菜单', 'Role Menu', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A', '/platform/user-management/role/menu/menu.module.ts', 'role/menu', '1', 'stop', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('A0C16F94-C9E9-4886-8300-FB9C484C54C8', N'业务对象编辑', 'Business Object Detail', NULL, '89269868-9E00-43F8-8493-F18A450C49E9', '/bpm/business-management/business-object/detail/detail.module.ts', 'business-object/detail', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('5333C10C-5845-4E44-A395-FC72FDCC1581', N'任务详情', 'Task Detail', NULL, '36F88F01-31B8-4298-83E7-199132D3AD54', '/automator/process-test-result/detail/detail.module.ts', 'test-result/detail', '1', 'folder', NULL, NULL);
INSERT INTO [dbo].[MenuDetails] ([MenuDetailId], [MenuName], [MenuEnglishName], [LanguageCode], [MenuId], [FileURL], [FunctionURL], [Hidden], [IconURL1], [IconURL2], [IconURL3]) VALUES ('4440FD57-929E-4242-B9DF-FD6C0F5B03B7', N'工作交接（待实现）', 'Hand Over', NULL, '7D084C4C-7701-49DC-A260-7ED72FE50038', '1', 'handover', '0', 'book', NULL, NULL);
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('export', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('user', NULL, '598250C1-9660-4F37-8A80-498E53274D50');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, 'A56EC975-F98C-4271-A77A-BF6CF73F1BD8');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '50126FFF-813F-40BC-B872-C39886999FD5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '352999C3-F6CA-43FF-9C0D-5CCF331CB45A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('user', NULL, 'BC733AC8-CFB2-4A52-B14F-325B9245D3EB');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, 'D576B728-C6F9-4B31-974B-48845FEB7A08');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, 'D576B728-C6F9-4B31-974B-48845FEB7A08');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '7E7E7017-878D-4B69-9823-03398A257AC8');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('export', NULL, '32F564B4-8E4D-4FA4-BDCA-C813670ABBBE');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '2F02B81C-3B37-4F76-A3F8-03AC5D81799B');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '2F02B81C-3B37-4F76-A3F8-03AC5D81799B');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '3F500497-8864-4DF9-B63D-991D74464830');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '82626B0F-D436-4799-ABF5-AE968383EA85');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '99C0DFA2-B13A-4AAB-BA1A-6C780D4100C6');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '2973735E-AE52-4D99-9C73-BA6452E7652D');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '7CBCC1AD-DF0D-425E-B07A-F97285B1CDC2');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, 'E418D5F9-1327-48E9-88AB-DF66595D7BAF');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '0D8341FA-F696-4C94-A85C-FF2030D214B5');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, 'A662CC1C-B11E-4F60-93DA-3E233C812017');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '9C5CFF6C-B264-4A2B-B442-D321C97E3A6A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '901874E1-3867-4B06-832E-ED772758E9C4');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '0BAC816E-1F7E-40F4-A52D-45AD1BD95520');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '975DABBB-E2AA-47D9-8B93-E30C17049281');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '975DABBB-E2AA-47D9-8B93-E30C17049281');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '8D9720E2-A342-4C1A-A785-794F6EC8E398');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '435851EA-72D0-4FB9-B391-F74907772790');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('user', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('iam', NULL, 'AA4F489E-AC04-4DF8-81BD-4F55C16AB08A');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('user', NULL, '001A1C91-5FC4-4682-83F0-D6B5336E5E4B');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('delete', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '8EC42A85-FEBD-4E31-B31F-3162C893D80F');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('user', NULL, '07C03644-FF61-49E4-B3AB-82F89B344F13');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('add', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('save', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9');
INSERT INTO [dbo].[MenuActionRelations] ([Code], [Name], [MenuId]) VALUES ('edit', NULL, '39C79F2C-23E0-476D-AFAD-3926825D75E9');
INSERT INTO [dbo].[Companies] ([CompanyCode], [ComapnyName], [CompanyNameEn], [City], [BusinessType], [Address], [ContactUser1], [ContactUser2], [PhoneNumber1], [PhoneNumber2], [Fax], [Status], [Remark], [CompanyManager], [CompanyType], [Domain], [UpperId], [Level], [SortCode], [ShowInMDS], [ProcessLevel], [ProcessType], [FullPathText], [FullPathCode], [F1], [F2], [F3], [F4], [F5] ) VALUES ('10010001', N'测试公司', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, NULL, 4, NULL, NULL, 0, 1, '0', NULL, NULL, N'测试公司', '51BDBAD5-88E6-4474-BA70-D88EB00B2434', N'51BDBAD5-88E6-4474-BA70-D88EB00B2434', NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[Organizations] ([Name], [Name2], [DeptCode], [CompanyId], [UpperId], [DeptManager], [DeptTelephone], [Level], [Status], [Remark], [CityId], [IndependentRoll], [SortCode], [ProcessLevel], [ProcessType], [FullPathText], [FullPathCode], [F1], [F2], [F3] ) VALUES (N'测试部门', NULL, N'10010002', 1, NULL, NULL, NULL, 0, 1, NULL, NULL, '0', 1, NULL, NULL, N'测试公司_测试部门', '51BDBAD5-88E6-4474-BA70-D88EB00B2434_D9B5A723-A0B3-4473-95BE-971C9A6C3C47', N'D9B5A723-A0B3-4473-95BE-971C9A6C3C47', NULL, NULL);
INSERT INTO [dbo].[Positions] ([PositionId], [OrganizationId], [JobTitleId], [OfficeId], [PositionCode], [Name], [Description], [UpperId], [IsActive], [CompanyId], [Type], [F1], [F2], [F3], [F4], [F5] ) VALUES ('C2B1B3F2-0AF3-4C1E-B3F7-0004A5ED77B4', 1, 'F78F0793-D062-48C2-8732-A70105BE8D93', NULL, N'10010003', '部门经理', NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO [dbo].[UserPositionRelations] ([UserPositionRelationId], [PositionId], [UserId], [PrimaryPosition], [StartDate], [EndDate], [IsActive], [F1], [F2], [F3] ) VALUES ('13E3B799-9259-4B6D-A42E-753744F91B20', 'C2B1B3F2-0AF3-4C1E-B3F7-0004A5ED77B4', '********-17E8-4D9A-8AB3-E055C366DC62', '1', '2017-12-14 00:10:33.630', '2517-12-14 00:10:33.630', '0', NULL, NULL, NULL);
INSERT INTO [dbo].[JobTitles] ([JobTitleId], [JobNo], [Name], [JobNameEn], [JobLevel], [JobCode], [JobCategory], [Description], [Status], [F1], [F2], [F3] ) VALUES ('F78F0793-D062-48C2-8732-A70105BE8D93', '6', N'测试岗位', NULL, 1, NULL, 'D', NULL, NULL, NULL, NULL, N'1');
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('78A295E2-B393-47BA-8546-25AE1DA5433F', N'reject', N'退回', 'checked', 2, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('1B4D5D64-193A-4561-9756-274AEA904338', N'discuss', N'沟通', '', 6, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('7B2DD223-CFA5-454A-B63E-2791C60DC25E', N'notice', N'通知', 'checked', 3, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('1C6BEF5A-B3B6-4354-8054-3E125034A4F4', N'approve', N'同意', 'checked', 1, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL,NULL, NULL);
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('09363866-0C39-4557-869D-6BDC40971617', N'cancel', N'作废', '', 7, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('3CD8836D-D150-4F90-BD7D-8A5095DDB289', N'bpm-instance-actions', N'BPM实例功能', 'bpm-instance-actions', 1, 1, N'按钮支持默认选中：checked', NULL, NULL, NULL, '********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('66B91A7F-7C66-4162-A335-A2B051C8DC35', N'counter-sign', N'加签', '', 4, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO [dbo].[Dictionaries] ([DictionaryId], [Code], [Name], [Value], [OrderNum], [Status], [Remark], [UpperId], [TypeCode], [TypeName], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('80E7F0F3-17BB-4020-B53E-FE9F858F9E9B', N'handover', N'交办', '', 5, 1, N'', NULL, 'bpm-instance-actions', N'BPM实例功能','********-17E8-4D9A-8AB3-E055C366DC62', NULL, NULL, NULL);
INSERT INTO [dbo].[Sequences]([SequenceId], [FormType], [FormPrefix], [FormNo], [SequenceType], [SequenceLength], [Description], [LastUpdateDate]) VALUES ('C85DE4DC-0485-48AE-84E7-80F044E9CC2D', 'process', NULL, 3, 1, 6, N'流程编号', NULL);
INSERT INTO [dbo].[Applications]([ApplicationId], [AppCode], [AppName], [AppType], [Url], [Description], [OrderNo], [Status], [CreateUserId], [CreateDate], [ModifyUserId], [ModifyDate]) VALUES ('EEBB53E4-136A-4B97-89F0-3E58FBBC40A5', 'platform', N'后台管理', 'S', NULL, N'后台管理的描述', 0, 1, '********-17E8-4D9A-8AB3-E055C366DC62', '1900-01-01 10:01:10.000', NULL, NULL);
