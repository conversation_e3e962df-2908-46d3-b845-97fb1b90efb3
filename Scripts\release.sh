args=("$@")
num=${#args[@]}
noBuild=0
dir=''

if [ $num = 2 ]; then
  if [ ${args[0]} = '--no-build' ]; then
    noBuild=1
    dir=${args[1]}
  elif [ ${args[1]} = '--no-build' ]; then
    noBuild=1
    dir=${args[0]}
  fi
elif [ $num = 1 ]; then
  dir=${args[0]}
else
  echo -e '\033[31m 当前脚本支持的参数：目标目录 & --no-build。 \033[0m'
  exit 1
fi

if [ ! -d $dir ]; then
  echo -e '\033[31m 目标目录不存在！ \033[0m'
  exit 2
fi

if [ $noBuild = 0 ]; then
  echo '==== 开始构建 ===='

  SOURCE_PUBLIC=https://api.nuget.org/v3/index.json
  SOURCE_PRIVATE=http://*************:15840/nuget

  dotnet restore -s $SOURCE_PUBLIC -s $SOURCE_PRIVATE
  dotnet publish ./Medusa.Service.Platform.Entrance/Medusa.Service.Platform.Entrance.csproj --no-restore -o $dir/api
fi

echo '==== 开始更新目标目录 ===='

# 移除目标目录下的产出物
rm -rf $dir/scripts/.gitlab-ci.yml

# 将新构建的产出物复制到目标目录
cp scripts/.gitlab-ci.yml $dir

echo '==== 完成 ===='
