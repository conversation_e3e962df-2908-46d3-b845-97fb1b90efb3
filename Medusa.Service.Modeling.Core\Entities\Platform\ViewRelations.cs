using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 视图关联表
    /// </summary>
    [EntityTable("ViewRelations")]
    public class ViewRelations
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 视图ID
        /// </summary>
        public Guid ViewId { get; set; }

        /// <summary>
        /// 父业务对象ID
        /// </summary>
        public Guid ParentBusinessObjectId { get; set; }

        /// <summary>
        /// 父业务对象字段ID
        /// </summary>
        public Guid ParentBusinessObjectColumnId { get; set; }

        /// <summary>
        /// 子业务对象ID
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// 子业务对象字段ID
        /// </summary>
        public Guid BusinessObjectColumnId { get; set; }

        /// <summary>
        /// 关联类型
        /// </summary>
        public int JoinType { get; set; }

        /// <summary>
        /// 对象类型：1-主对象，2-关联对象
        /// </summary>
        public int ObjectType { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int OrderNumber { get; set; }
    }
}
