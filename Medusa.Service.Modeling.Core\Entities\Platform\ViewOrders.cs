using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 视图排序表
    /// </summary>
    [EntityTable("ViewOrders")]
    public class ViewOrders
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 视图ID
        /// </summary>
        public Guid ViewId { get; set; }

        /// <summary>
        /// 业务对象ID
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// 业务对象字段ID
        /// </summary>
        public Guid BusinessObjectColumnId { get; set; }

        /// <summary>
        /// 排序方式：ASC，DESC
        /// </summary>
        public string OrderType { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int OrderNumber { get; set; }
    }
}
