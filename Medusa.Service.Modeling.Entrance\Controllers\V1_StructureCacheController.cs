using System;
using Medusa.Service.Modeling.Application.StructureCache;
using Medusa.Service.Modeling.Application.StructureCache.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// StructureCache controller
    /// </summary>
    [Route("v1/structure-cache")]
    [ApiExplorerSettings(GroupName = "StructureCache.v1")]
    public class V1_StructureCacheController : ProductControllerBase
    {
        #region //  服务注入
        readonly IStructureCacheService _structureCacheService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_StructureCacheController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="structureCacheService">structureCacheService</param>
        public V1_StructureCacheController(IStructureCacheService structureCacheService)
        {
            _structureCacheService = structureCacheService;
        }
        #endregion

        /// <summary>
        /// SetRedis
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("update")]
        public void SetRedis([FromBody]StructureCacheSetDto dto)
        {
            _structureCacheService.SetRedis(dto);
        }

        /// <summary>
        /// SetAllRedis
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("update-all")]
        public void SetAllRedis([FromBody] StructureCacheSetDto dto)
        {
            _structureCacheService.SetAllRedis(dto);
        }
    }
}
