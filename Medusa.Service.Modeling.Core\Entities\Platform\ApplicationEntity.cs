using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 应用
    /// </summary>
    [EntityTable("Applications")]
    public partial class ApplicationEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Describe { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateDate { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string UpdateUser { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDelete { get; set; }
    }
}
