using System;
using Medusa.Service.Modeling.Application.DataBaseManage.Dtos;

namespace Medusa.Service.Modeling.Application.DataBaseManage
{
    
    
    
    public interface IDataBaseManageService : IServiceBase
    {
        
        
        
        
        
        PageResult<DataBaseDto> GetDataBases(DataBaseQueryDto dto);

        
        
        
        
        void SaveDataBase(DataBaseDto dto);

        
        
        
        
        void Test(DataBaseDto dto);

        
        
        
        
        
        
        PageResult<ObjectResultDto> GetObject(Guid id, ObjectQueryDto dto);

        
        
        
        
        
        void ObjectLoad(Guid id, ObjectLoadDto dto);
    }
}
