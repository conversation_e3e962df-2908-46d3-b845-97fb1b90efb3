using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.Boost
{
    /// <summary>
    /// 用户
    /// </summary>
    [EntityTable("users")]
    public class Users
    {
        /// <summary>
        /// 主键
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        public string UserLoginId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 用户别名
        /// </summary>
        public string UserAlias { get; set; }

        /// <summary>
        /// 登录密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserType { get; set; }

        /// <summary>
        /// 数据源类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 数据源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// First名称
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// Middle名称
        /// </summary>
        public string MiddleName { get; set; }

        /// <summary>
        /// Last名称
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// 名称拼音首
        /// </summary>字母
        public string PinyinFirstWord { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 后备邮件
        /// </summary>
        public string Emailbake { get; set; }

        /// <summary>
        /// 生日
        /// </summary>
        public DateTime? BirthDay { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string MobilePhone { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string CreateUserName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 最后修改人
        /// </summary>Id
        public string MidifyUserId { get; set; }

        /// <summary>
        /// 最后修改人
        /// </summary>名称
        public string ModifyUserName { get; set; }

        /// <summary>
        /// 最后修改时
        /// </summary>间
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        /// 最新登录时
        /// </summary>间
        public DateTime? LastestLoginDate { get; set; }

        /// <summary>
        /// 密码锁定时
        /// </summary>间
        public DateTime? PasswordLockTime { get; set; }

        /// <summary>
        /// 锁定状态
        /// </summary>
        public int? LockStatus { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        public string WorkNumber { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? SortCode { get; set; }

        /// <summary>
        /// 直属上级Id
        /// </summary>
        public Guid? UpperUserId { get; set; }

        /// <summary>
        /// 主岗所属组
        /// </summary>织Id
        public string OrganizationId { get; set; }

        /// <summary>
        /// 主岗所属组
        /// </summary>织名称
        public string OrganizationName { get; set; }

        /// <summary>
        /// 主岗所属组
        /// </summary>织全路径
        public string FullPathCode { get; set; }

        /// <summary>
        /// 主岗所属组
        /// </summary>织全路径
        public string FullPathText { get; set; }

        /// <summary>
        /// F1
        /// </summary>
        public string F1 { get; set; }

        /// <summary>
        /// F2
        /// </summary>
        public string F2 { get; set; }

        /// <summary>
        /// F3
        /// </summary>
        public string F3 { get; set; }

        /// <summary>
        /// F4
        /// </summary>
        public string F4 { get; set; }

        /// <summary>
        /// F5
        /// </summary>
        public string F5 { get; set; }

        /// <summary>
        /// F6
        /// </summary>
        public string F6 { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public string Avatar { get; set; }
    }
}
