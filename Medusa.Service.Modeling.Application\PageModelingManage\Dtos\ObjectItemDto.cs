using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.Dtos;

namespace Medusa.Service.Modeling.Application.PageModelingManage.Dtos
{
    
    
    
    public class ObjectItemDto
    {
        
        
        
        public Guid ObjectId { get; set; }

        
        
        
        public string ObjectName { get; set; }

        
        
        
        public string ObjectDescription { get; set; }

        
        
        
        public bool? IsTree { get; set; }

        
        
        
        public List<ObjectStructureDto> Structure { get; set; }
    }
}
