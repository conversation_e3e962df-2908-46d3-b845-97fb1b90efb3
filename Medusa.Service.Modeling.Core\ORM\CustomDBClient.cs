using Medusa.Service.Modeling.Core.Entity;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.ORM
{
    /// <summary>
    /// 自定义数据库链接
    /// </summary>
    public static class CustomDbClient
    {
        /// <summary>
        /// 自定义数据库链接
        /// </summary>
        /// <param name="entity">entity</param>
        /// <returns>returns</returns>
        public static SqlSugarClient CustomDB(DataBase entity)
        {
            DbType temp = DbType.SqlServer;
            switch (entity.Type)
            {
                case "SqlServer":
                    temp = DbType.SqlServer;
                    break;
                case "MySql":
                    temp = DbType.MySql;
                    break;
                case "Oracle":
                    temp = DbType.Oracle;
                    break;
            }

            string connectionString = GetConnectionString(entity);
            return new SqlSugarClient(new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = temp,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute
            });
        }

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        /// <param name="entity">entity</param>
        /// <returns>returns</returns>
        private static string GetConnectionString(DataBase entity)
        {
            string str = string.Empty;
            switch (entity.Type)
            {
                case "SqlServer":
                    str = $"Server={entity.Localhost};Database={entity.Name};Integrated Security=False;User ID={entity.Account};Password={entity.Password};";
                    break;
                case "MySql":
                    string port = !string.IsNullOrEmpty(entity.Port) ? $"Port={entity.Port};" : string.Empty;
                    str = $"server={entity.Localhost};{port}Database={entity.Name};Uid={entity.Account};Pwd={entity.Password}";
                    break;
            }

            return str;
        }
    }
}
