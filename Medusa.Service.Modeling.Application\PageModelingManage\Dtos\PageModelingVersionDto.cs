using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.PageModelingManage.Dtos
{
    
    
    
    public class PageModelingVersionDto
    {
        
        
        
        public int? Version { get; set; }

        
        
        
        public Guid Id { get; set; }

        
        
        
        public Guid PageModelingId { get; set; }

        
        
        
        public int? SourceVersion { get; set; }

        
        
        
        public string PageDesginJson { get; set; }

        
        
        
        public int Status { get; set; }
    }
}
