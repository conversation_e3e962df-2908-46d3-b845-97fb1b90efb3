using System;
using System.Collections.Generic;
using System.Text;
using Medusa.Service.Modeling.Application.CRMTasks.Dtos;

namespace Medusa.Service.Modeling.Application.CRMTasks
{
    
    
    
    public interface ICrmTasksService
    {
        
        
        
        
        void SaveVisit(TaskVisitDto task);

        
        
        
        
        
        public CrmPageResult<XianChangVisit> GetXianChangVisists(TaskVisitQueryDto taskVisitQuery);

        
        
        
        
        
        public CrmPageResult<DianHuaVisit> GetDianHuaVisists(TaskVisitQueryDto taskVisitQuery);

        
        
        
        
        
        public CrmPageResult<SatisfactionVisitDto> GetSatisfactionVisits(TaskVisitQueryDto taskVisitQuery);

        
        
        
        
        
        public CrmPageResult<SaleClueVisitDto> GetSaleClueVisits(TaskVisitQueryDto taskVisitQuery);

        
        
        
        
        
        public List<string> GetTaskConfigs(Guid taskId);
    }
}
