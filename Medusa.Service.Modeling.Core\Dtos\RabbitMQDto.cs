using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Medusa.Service.Modeling.Core
{
    /// <summary>
    /// RabbitMQ
    /// </summary>
    public class RabbitMQDto
    {
        /// <summary>
        /// ExchangeName
        /// </summary>
        public string ExchangeName { get; set; }

        /// <summary>
        /// HostName
        /// </summary>
        public string HostName { get; set; }

        /// <summary>
        /// UserName
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Password
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// StartQueue
        /// </summary>
        public string InterfaceQueue { get; set; }

        /// <summary>
        /// 是否允许消息用于调试的消息，用于方便本地调试使用
        /// </summary>
        public bool? IsSubscribeDebugMessage { get; set; } = false;

        /// <summary>
        /// 端口默认5672
        /// </summary>
        public int? Port { get; set; }
    }
}
