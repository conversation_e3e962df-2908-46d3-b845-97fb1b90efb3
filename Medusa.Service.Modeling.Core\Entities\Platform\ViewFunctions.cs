using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 函数表
    /// </summary>
    [EntityTable("ViewFunctions")]
    public class ViewFunctions
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 函数名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 函数说明
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// C#函数逻辑代码
        /// </summary>
        public string CodeScript { get; set; }

        /// <summary>
        /// 状态：0-未发布 1-已发布
        /// </summary>
        public int State { get; set; }
    }
}
