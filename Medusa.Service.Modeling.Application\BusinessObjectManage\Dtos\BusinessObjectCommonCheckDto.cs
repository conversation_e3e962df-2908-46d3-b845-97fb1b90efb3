using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos
{
    
    
    
    public class BusinessObjectCommonCheckDto
    {
        
        
        
        public bool? IsOr { get; set; }

        
        
        
        public string Wheres { get; set; }

        
        
        
        public bool? IsDelete { get; set; }

        
        
        
        public string CheckFieldName { get; set; }

        
        
        
        public string CheckFieldValue { get; set; }
    }
}
