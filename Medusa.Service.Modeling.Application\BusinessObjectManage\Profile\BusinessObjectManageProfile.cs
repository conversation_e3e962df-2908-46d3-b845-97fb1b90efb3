using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Core.Entity;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.BusinessObjectManage.Profile
{
    
    
    
    public class BusinessObjectManageProfile : AutoMapper.Profile
    {
        
        
        
        public BusinessObjectManageProfile()
        {
            CreateMap<BusinessObject, ObjectDto>();
            CreateMap<ObjectDto, BusinessObject>();
            CreateMap<BusinessObjectColumn, ObjectColumnDto>();
            CreateMap<ObjectColumnDto, BusinessObjectColumn>();
            CreateMap<DbTableInfo, ObjectDto>();
        }
    }
}
