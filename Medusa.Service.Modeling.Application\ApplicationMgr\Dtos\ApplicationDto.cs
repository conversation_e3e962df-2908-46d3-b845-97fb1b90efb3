using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.ModuleMgr.Dtos;
using Medusa.Service.Modeling.Application.PageModelingManage.Dtos;

namespace Medusa.Service.Modeling.Application.ApplicationMgr.Dtos
{
    
    
    
    public class ApplicationDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Describe { get; set; }

        
        
        
        public DateTime CreateDate { get; set; }

        
        
        
        public List<PageModelingDto> Pages { get; set; }

        
        
        
        public List<ModuleDto> Modules { get; set; }

        
        
        
        public List<ObjectDto> Bos { get; set; }

        
        
        
        public List<CompositeObjectDto> Combos { get; set; }
    }
}
