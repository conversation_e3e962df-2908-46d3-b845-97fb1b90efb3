using System.Threading.Tasks;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.OperationLog;
using Medusa.Service.Modeling.Application.OperationLog.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// operationLog controller
    /// </summary>
    [Route("v1/operation-log")]
    [ApiExplorerSettings(GroupName = "OperationLog.v1")]
    public class V1_OperationLogController : ProductControllerBase
    {
        #region //  服务注入
        readonly IOperationLogService _operationLogService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_OperationLogController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="operationLogService">operationLogService</param>
        public V1_OperationLogController(IOperationLogService operationLogService)
        {
            _operationLogService = operationLogService;
        }
        #endregion

        /// <summary>
        /// 获取日志
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet]
        public async Task<PageResult<LogResultDto>> GetLog([FromQuery] LogQueryDto dto)
        {
            return await _operationLogService.GetLog(dto);
        }
    }
}
