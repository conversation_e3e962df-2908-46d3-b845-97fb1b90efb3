using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// CRM任务应用历史表
    /// </summary>
    [SugarTable("crmtaskapplicationhistory")]
    public class Crmtaskapplicationhistory
    {
        /// <summary>
        /// 模板应用历史ID
        /// </summary>
        [SugarColumn(ColumnName = "templateHistoryId", IsPrimaryKey = true)]
        public Guid TemplateHistoryId { get; set; }

        /// <summary>
        /// 模版ID
        /// </summary>
        [SugarColumn(ColumnName = "templateId")]
        public Guid TemplateId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [SugarColumn(ColumnName = "templateName")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 应用部门(名称逗号分隔字符串)
        /// </summary>
        [SugarColumn(ColumnName = "appliedDepartment")]
        public string AppliedDepartment { get; set; }

        /// <summary>
        /// 应用人员(名称逗号分隔字符串)
        /// </summary>
        [SugarColumn(ColumnName = "appliedPersonnel")]
        public string AppliedPersonnel { get; set; }

        /// <summary>
        /// 应用部门(id逗号分隔字符串)
        /// </summary>
        [SugarColumn(ColumnName = "appliedDepartmentIds")]
        public string AppliedDepartmentIds { get; set; }

        /// <summary>
        /// 应用人员(id逗号分隔字符串)
        /// </summary>
        [SugarColumn(ColumnName = "appliedPersonnelIds")]
        public string AppliedPersonnelIds { get; set; }

        /// <summary>
        /// 模板描述
        /// </summary>
        [SugarColumn(ColumnName = "templateDescription")]
        public string TemplateDescription { get; set; }

        /// <summary>
        /// 任务项配置JSON
        /// </summary>
        [SugarColumn(ColumnName = "taskConfigJson")]
        public string TaskConfigJson { get; set; }

        /// <summary>
        /// 应用状态 1已应用,0未引用
        /// </summary>
        [SugarColumn(ColumnName = "appliedStatus")]
        public bool AppliedStatus { get; set; }

        /// <summary>
        /// 生效开始时间
        /// </summary>
        [SugarColumn(ColumnName = "effectiveStartDate")]
        public DateTime? EffectiveStartDate { get; set; }

        /// <summary>
        /// 生效结束时间
        /// </summary>
        [SugarColumn(ColumnName = "effectiveEndDate")]
        public DateTime? EffectiveEndDate { get; set; }

        /// <summary>
        /// cron表达式
        /// </summary>
        [SugarColumn(ColumnName = "cronExpression")]
        public string CronExpression { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 是否删除 1已删除,0未删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [SugarColumn(ColumnName = "createdUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "createdDate")]
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// 修改用户ID
        /// </summary>
        [SugarColumn(ColumnName = "modifiedUserId")]
        public string ModifiedUserId { get; set; }
    }
}
