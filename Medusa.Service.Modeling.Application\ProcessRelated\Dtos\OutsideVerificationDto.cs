using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class OutsideVerificationDto
    {
        
        
        
        public string Type { get; set; }

        
        
        
        public Guid ProcessId { get; set; }

        
        
        
        public string ProcessVersion { get; set; }

        
        
        
        public string Boid { get; set; }

        
        
        
        public string Number { get; set; }

        
        
        
        public string DataType { get; set; }

        
        
        
        public string DataKey { get; set; }

        
        
        
        public string DataValue { get; set; }
    }
}
