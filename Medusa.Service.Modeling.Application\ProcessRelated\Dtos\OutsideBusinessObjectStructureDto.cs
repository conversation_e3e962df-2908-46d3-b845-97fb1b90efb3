using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class OutsideBusinessObjectStructureDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public List<OutsideBusinessObjectColumnStructureDto> Columns { get; set; }

        
        
        
        public List<OutsideBusinessObjectStructureDto> Chi<PERSON>ren { get; set; }
    }
}
