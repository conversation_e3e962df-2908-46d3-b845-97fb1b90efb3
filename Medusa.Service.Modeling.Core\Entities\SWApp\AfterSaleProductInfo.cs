using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 客诉登记-产品信息
    /// </summary>
    [SugarTable("aftersaleproductinfo")]

    public class AfterSaleProductInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(ColumnName = "ID", IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 厚度
        /// </summary>
        [SugarColumn(ColumnName = "Thickness")]
        public string Thickness { get; set; }

        /// <summary>
        /// 创建用户Id
        /// </summary>
        [SugarColumn(ColumnName = "CreateUserId")]
        public string CreateUserId { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "ModifyDate")]
        public string ModifyDate { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [SugarColumn(ColumnName = "BanchID")]
        public string BanchID { get; set; }

        /// <summary>
        /// 品质部负责人对象
        /// </summary>
        [SugarColumn(ColumnName = "PZBFZRObject")]
        public string PZBFZRObject { get; set; }

        /// <summary>
        /// 创建用户组织路径Id
        /// </summary>
        [SugarColumn(ColumnName = "CreateUserOrgPathId")]
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        /// 品质部负责人
        /// </summary>
        [SugarColumn(ColumnName = "PZBFZR")]
        public string Pzbfzr { get; set; }

        /// <summary>
        /// 财务部负责人编码
        /// </summary>
        [SugarColumn(ColumnName = "CWBFZRCode")]
        public string CWBFZRCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "Remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 财务部负责人对象
        /// </summary>
        [SugarColumn(ColumnName = "CWBFZRObject")]
        public string CWBFZRObject { get; set; }

        /// <summary>
        /// 产品大类
        /// </summary>
        [SugarColumn(ColumnName = "ProductType")]
        public string ProductType { get; set; }

        /// <summary>
        /// 平米数
        /// </summary>
        [SugarColumn(ColumnName = "Square")]
        public string Square { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "CreateDate")]
        public string CreateDate { get; set; }

        /// <summary>
        /// 长度
        /// </summary>
        [SugarColumn(ColumnName = "Long")]
        public string Long { get; set; }

        /// <summary>
        /// 品质部负责人编码
        /// </summary>
        [SugarColumn(ColumnName = "PZBFZRCode")]
        public string PZBFZRCode { get; set; }

        /// <summary>
        /// 财务部负责人
        /// </summary>
        [SugarColumn(ColumnName = "CWBFZR")]
        public string Cwbfzr { get; set; }

        /// <summary>
        /// 单方成本
        /// </summary>
        [SugarColumn(ColumnName = "Cost")]
        public string Cost { get; set; }

        /// <summary>
        /// 幅宽
        /// </summary>
        [SugarColumn(ColumnName = "Width")]
        public string Width { get; set; }

        /// <summary>
        /// 修改用户Id
        /// </summary>
        [SugarColumn(ColumnName = "ModifyUserId")]
        public string ModifyUserId { get; set; }

        /// <summary>
        /// 产品大类ID
        /// </summary>
        [SugarColumn(ColumnName = "ProductTypeID")]
        public string ProductTypeID { get; set; }

        /// <summary>
        /// 客诉ID
        /// </summary>
        [SugarColumn(ColumnName = "CustomerComplaintID")]
        public string CustomerComplaintID { get; set; }

        /// <summary>
        /// 上级ID
        /// </summary>
        [SugarColumn(ColumnName = "ParentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// 最终金额
        /// </summary>
        [SugarColumn(ColumnName = "Amount")]
        public string Amount { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [SugarColumn(ColumnName = "ProductName")]
        public string ProductName { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public byte? IsDelete { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        [SugarColumn(ColumnName = "ProductModel")]
        public string ProductModel { get; set; }
    }
}
