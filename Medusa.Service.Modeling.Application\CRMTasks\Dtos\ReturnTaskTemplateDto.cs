using System;
using System.Collections.Generic;
using System.Text;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.CRMTasks.Dtos
{
    
    
    
    public class ReturnTaskTemplateDto
    {
        
        
        
        public Guid Templateid { get; set; }

        
        
        
        public string Templatename { get; set; }

        
        
        
        public string CreatedDate { get; set; }

        
        
        
        public bool IsDelete { get; set; }

        
        
        
        public bool IsActive { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public string TaskConfigJson { get; set; }
    }
}
