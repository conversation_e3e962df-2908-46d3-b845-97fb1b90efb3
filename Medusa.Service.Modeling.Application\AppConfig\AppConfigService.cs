using System;
using Medusa.Service.Modeling.Application.ProductRegistration;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;

namespace Medusa.Service.Modeling.Application.AppConfig
{
    
    
    
    public class AppConfigService : IAppConfigService
    {
        private readonly IMemoryCache _cache;
        private readonly IServiceProvider _serviceProvider;

        
        
        
        
        public AppConfigService(IServiceProvider serviceProvider)
        {
            _cache = serviceProvider.GetService<IMemoryCache>();
            _serviceProvider = serviceProvider;
        }

        
        
        
        
        public void AppConfigCache(JObject appConfigDto)
        {
            var entryOptions = new MemoryCacheEntryOptions().SetPriority(CacheItemPriority.NeverRemove);
            _cache.Set("AppSettings", appConfigDto, entryOptions);

            var productRegistrationService = _serviceProvider.GetService<IProductRegistrationService>();
            productRegistrationService.ProductRegistrationCheck();
        }
    }
}
