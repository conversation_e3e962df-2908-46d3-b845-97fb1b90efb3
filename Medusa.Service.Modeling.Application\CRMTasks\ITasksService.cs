using System;
using System.Collections.Generic;
using System.Text;
using Medusa.Service.Modeling.Application.CRMTasks.Dtos;
using Medusa.Service.Modeling.Core.Entities.LowCode;

namespace Medusa.Service.Modeling.Application.CRMTasks
{
    
    
    
    public interface ITasksService
    {
        
        
        
        
        
        PageResult<ReturnTaskTemplateDto> SearchTaskTemplate(SearchTaskTemplateDto dto);

        
        
        
        
        void AddTaskTemplate(CrmtasktemplateDto dto);

        
        
        
        
        void UpdateTaskTemplate(CrmtasktemplateDto dto);

        
        
        
        
        List<TaskConfigDto> GetTaskConfig();

        
        
        
        
        
        PageResult<Crmtaskapplicationhistory> SearchTaskTemplateHistory(SearchTaskTemplateHistoryDto dto);

        
        
        
        
        void AddTaskTemplateApplication(CrmtasktemplateApplicationDto dto);

        
        
        
        
        void ChangeTaskTemplateApplication(ChangeTemplateApplicationDto dto);

        
        
        
        
        List<CronConfigDto> GetCronConfig();

        
        
        
        
        
        PageResult<Crmtask> SearchTask(SearchTaskDto dto);

        
        
        
        
        void AddCrmTask(Guid? templateId);
    }
}
