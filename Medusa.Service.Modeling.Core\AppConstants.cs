using System.Threading;

namespace Medusa.Service.Modeling.Core
{
    /// <summary>
    /// 应用常量
    /// </summary>
    public static class AppConstants
    {
        /// <summary>
        /// 应用版本
        /// </summary>
        public const string AppVersion = "Version";

        /// <summary>
        /// 应用默认语言
        /// </summary>
        public const string I18nDefaultLang = "zh-CN";

        /// <summary>
        /// 当前项目名称
        /// </summary>
        public const string ProjectName = "Medusa.Service.Modeling.Entrance";
    }
}
