using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ProcessRelated.Dtos
{
    
    
    
    public class OutsideBusinessObjectDataSearchDto
    {
        
        
        
        public object BusinessObjectKeyValue { get; set; }

        
        
        
        public Guid MainBusinessObjectId { get; set; }

        
        
        
        public List<Guid> DetailBusinessObjectIds { get; set; }

        
        
        
        public Guid? RelationId { get; set; }
    }
}
