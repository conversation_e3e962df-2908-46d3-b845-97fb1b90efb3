using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.ApplicationMgr;
using Medusa.Service.Modeling.Application.ApplicationMgr.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// ApplicationMgr controller
    /// </summary>
    [Route("v1/application")]
    [ApiExplorerSettings(GroupName = "ApplicationMgr.v1")]
    public class V1_ApplicationMgrController : ProductControllerBase
    {
        #region //  服务注入
        readonly IApplicationMgrService _applicationMgrService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_ApplicationMgrController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="applicationMgrService">applicationMgrService</param>
        public V1_ApplicationMgrController(IApplicationMgrService applicationMgrService)
        {
            _applicationMgrService = applicationMgrService;
        }
        #endregion

        /// <summary>
        /// 获取应用
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet]
        public List<ApplicationDto> GetApplications(ApplicationQueryDto dto)
        {
            return _applicationMgrService.GetApplications(dto);
        }

        /// <summary>
        /// 获取应用
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpGet("{id}")]
        public ApplicationDto GetApplication([FromRoute]Guid id)
        {
            return _applicationMgrService.GetApplication(id);
        }

        /// <summary>
        /// 删除应用
        /// </summary>
        /// <param name="id">id</param>
        [HttpDelete("{id}")]
        public void DeleteApplication([FromRoute] Guid id)
        {
            _applicationMgrService.DeleteApplication(id);
        }

        /// <summary>
        /// 新增应用
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost]
        public void AddApplication([FromBody] ApplicationDto dto)
        {
            _applicationMgrService.AddApplication(dto);
        }

        /// <summary>
        /// 修改应用
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        [HttpPut("{id}")]
        public void UpdateApplication([FromRoute] Guid id, [FromBody] ApplicationDto dto)
        {
            _applicationMgrService.UpdateApplication(id, dto);
        }
    }
}
