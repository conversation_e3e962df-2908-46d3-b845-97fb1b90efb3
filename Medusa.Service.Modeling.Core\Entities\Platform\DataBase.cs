using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 数据库--库
    /// </summary>
    [EntityTable("DataBases")]
    public class DataBase
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 数据库唯一编码
        /// </summary>
        public string OnlyCode { get; set; }

        /// <summary>
        /// 数据库名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Desc:数据库描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 数据库登录账号
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// 数据库登录密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 数据库地址
        /// </summary>
        public string Localhost { get; set; }

        /// <summary>
        /// 数据库类型 SqlServer，MySql，Oracle
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateDate { get; set; }

        /// <summary>
        /// 是否外部业务库
        /// </summary>
        public bool IsOutSideBusinessBase { get; set; }

        /// <summary>
        /// 端口
        /// </summary>
        public string Port { get; set; }
    }
}
