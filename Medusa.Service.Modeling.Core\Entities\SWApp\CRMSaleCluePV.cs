using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entities.SWApp
{
    /// <summary>
    /// 销售线索（PV）
    /// </summary>
    [EntityTable("crmsalecluepv")]
    public class CRMSaleCluePV
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true, IsIdentity = true)]
        public string ID { get; set; }

        /// <summary>
        /// //厂家
        /// </summary>
        public string FactoryName { get; set; }

        /// <summary>
        /// //组件边框解决方案
        /// </summary>
        public string BKSolution { get; set; }

        /// <summary>
        ///  //生产
        /// </summary>
        public string Production { get; set; }

        /// <summary>
        ///  //客户要求
        /// </summary>
        public string Requirement { get; set; }

        /// <summary>
        ///  //是否删除
        /// </summary>
        public int IsDelete { get; set; }

        /// <summary>
        ///  //电池片主栅、细栅印刷解决方案
        /// </summary>
        public string YSSolution { get; set; }

        /// <summary>
        ///  //创建用户组织路径Id
        /// </summary>
        public string CreateUserOrgPathId { get; set; }

        /// <summary>
        ///  //客户产能信息
        /// </summary>
        public string Capacity { get; set; }

        /// <summary>
        ///  //客户痛点
        /// </summary>
        public string PainSpot { get; set; }

        /// <summary>
        ///  //创建日期
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        ///  //客户需求背景
        /// </summary>
        public string Background { get; set; }

        /// <summary>
        ///  //管理
        /// </summary>
        public string Management { get; set; }

        /// <summary>
        ///  //销售
        /// </summary>
        public string Sale { get; set; }

        /// <summary>
        ///  //竞品销售
        /// </summary>
        public string CompetitiveProSales { get; set; }

        /// <summary>
        ///  //组件/电池片技术路线
        /// </summary>
        public string TechRoute { get; set; }

        /// <summary>
        ///  //修改用户Id
        /// </summary>
        public string ModifyUserId { get; set; }

        /// <summary>
        ///  //技术来源
        /// </summary>
        public string TechSource { get; set; }

        /// <summary>
        ///  //销售线索主表主键
        /// </summary>
        public string SaleClueID { get; set; }

        /// <summary>
        ///  //组件其他胶带
        /// </summary>
        public string AssemblyOtherTape { get; set; }

        /// <summary>
        ///  //上级ID
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        ///  //电池片制程材料解决方案
        /// </summary>
        public string MaterialSolution { get; set; }

        /// <summary>
        ///  //竞品产品
        /// </summary>
        public string CompetitivePro { get; set; }

        /// <summary>
        ///  //客户新动向
        /// </summary>
        public string NewTrend { get; set; }

        /// <summary>
        ///  //竞品未满足的需求
        /// </summary>
        public string NotMeet { get; set; }

        /// <summary>
        ///  //竞品生产
        /// </summary>
        public string CompetitiveProduction { get; set; }

        /// <summary>
        ///  //组件浆料、焊接(互联方式)解决方案
        /// </summary>
        public string HJSolution { get; set; }

        /// <summary>
        ///  //组件封装解决方案
        /// </summary>
        public string FZSolution { get; set; }

        /// <summary>
        ///  //创建用户Id
        /// </summary>
        public string CreateUserId { get; set; }

        /// <summary>
        ///  //终端客户需求
        /// </summary>
        public string TerminalRequirement { get; set; }

        /// <summary>
        ///  //竞品管理
        /// </summary>
        public string CompetitiveProManagement { get; set; }

        /// <summary>
        ///  //产品
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        ///  //修改日期
        /// </summary>
        public DateTime ModifyDate { get; set; }

        /// <summary>
        ///  //客户指定终端客户（若有）
        /// </summary>
        public string TerminalCuatomer { get; set; }
    }
}
