using System;
using System.Collections.Generic;
using System.Text;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.CRMTasks.Dtos
{
    
    
    
    public class CrmtasktemplateApplicationDto
    {
        
        
        
        public Guid Templateid { get; set; }

        
        
        
        public string Templatename { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public string TaskConfigJson { get; set; }

        
        
        
        public bool IsDelete { get; set; }

        
        
        
        public bool IsActive { get; set; }

        
        
        
        public string AppliedDepartment { get; set; }

        
        
        
        public string AppliedPersonnel { get; set; }

        
        
        
        public string AppliedDepartmentIds { get; set; }

        
        
        
        public string AppliedPersonnelIds { get; set; }

        
        
        
        public string TemplateDescription { get; set; }

        
        
        
        public bool AppliedStatus { get; set; }

        
        
        
        public DateTime? EffectiveStartDate { get; set; }

        
        
        
        public DateTime? EffectiveEndDate { get; set; }

        
        
        
        public string CronExpression { get; set; }

        
        
        
        public string Remark { get; set; }
    }
}
