using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 关联对象
    /// </summary>
    [EntityTable("CompositeObjectRelations")]
    public partial class CompositeObjectRelation
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 组合对象Id
        /// </summary>
        public Guid CompositeObjectId { get; set; }

        /// <summary>
        /// 父级业务对象列Id
        /// </summary>
        public Guid ParentBusinessObjectColumnId { get; set; }

        /// <summary>
        /// 业务对象Id
        /// </summary>
        public Guid BusinessObjectId { get; set; }

        /// <summary>
        /// 业务对象列Id
        /// </summary>
        public Guid BusinessObjectColumnId { get; set; }

        /// <summary>
        /// 关联类型(1:inner、2:left)
        /// </summary>
        public int JoinType { get; set; }

        /// <summary>
        /// 关联关系(1:一对一、2:一对多)
        /// </summary>
        public int JoinRelation { get; set; }

        /// <summary>
        /// 上级组合对象ID
        /// </summary>
        public Guid? ParentId { get; set; }

        /// <summary>
        /// 对象类型
        /// </summary>
        public string ObjectType { get; set; }

        /// <summary>
        /// 额外条件
        /// </summary>
        public object ExtraCondition { get; set; }
    }
}
