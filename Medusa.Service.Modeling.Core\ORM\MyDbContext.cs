using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core
{
    /// <summary>
    /// DbContext 访问类
    /// </summary>
    public class MyDbContext
    {
        readonly IDbContext _dbContext;

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="dbContext">注入 DbContext</param>
        public MyDbContext(IDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Modeling
        /// </summary>
        public MultipleClient Modeling
        {
            // get { return _dbContext["modeling"]; }
            get
            {
                _dbContext.Client.Aop.OnLogExecuting = (sql, pars) =>
                {
                    Console.WriteLine(sql);
                };
                return _dbContext["modeling"];
            }
        }

        /// <summary>
        /// Modeling
        /// </summary>
        public MultipleClient Boost
        {
            get { return _dbContext["Boost"]; }
        }
    }
}
