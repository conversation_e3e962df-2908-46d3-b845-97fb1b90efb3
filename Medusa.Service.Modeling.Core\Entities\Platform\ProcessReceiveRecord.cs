using System;
using MT.Enterprise.Core.ORM;

namespace Medusa.Service.Modeling.Core.Entity
{
    /// <summary>
    /// 流程接收记录
    /// </summary>
    [EntityTable("ProcessReceiveRecord")]
    public class ProcessReceiveRecord
    {
        /// <summary>
        /// 主键
        /// </summary>
        [EntityColumn(IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// Boid
        /// </summary>
        public string Boid { get; set; }

        /// <summary>
        /// Data
        /// </summary>
        public string Data { get; set; }

        /// <summary>
        /// CreateDate
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Type
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Btid
        /// </summary>
        public string Btid { get; set; }

        /// <summary>
        /// Bsid
        /// </summary>
        public string Bsid { get; set; }
    }
}
