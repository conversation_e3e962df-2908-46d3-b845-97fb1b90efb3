using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Medusa.Service.Modeling.Application.ProcessRelated;
using Medusa.Service.Modeling.Application.ProcessRelated.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json.Linq;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

namespace Medusa.Service.Modeling.Connect
{
    /// <summary>
    /// MQ对接流程操作
    /// </summary>
    public class ProcessReceiveByMQService
    {
        private RabbitMQDto mqConfig;
        readonly IProcessRelatedService _processRelatedService;
        private ILogger logger;

        /// <summary>
        /// ProcessReceiveByMQService
        /// </summary>
        /// <param name="serviceProvider">serviceProvider</param>
        public ProcessReceiveByMQService(IServiceProvider serviceProvider)
        {
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            mqConfig = new RabbitMQDto
            {
                HostName = appSettings["RabbitMQ"]?["HostName"]?.ToString(),
                UserName = appSettings["RabbitMQ"]?["UserName"]?.ToString(),
                Password = appSettings["RabbitMQ"]?["Password"]?.ToString(),
                Port = !string.IsNullOrEmpty(appSettings["RabbitMQ"]?["Port"]?.ToString()) ? int.Parse(appSettings["RabbitMQ"]?["Port"]?.ToString()) : 5672,
                ExchangeName = appSettings["RabbitMQ"]?["ExchangeName"]?.ToString(),
                InterfaceQueue = appSettings["RabbitMQ"]?["InterfaceQueue"]?.ToString(),
            };
            _processRelatedService = serviceProvider.GetService<IProcessRelatedService>();
            logger = serviceProvider.GetService<ILogger<ProcessReceiveByMQService>>();
        }

        /// <summary>
        /// 启动运行
        /// </summary>
        /// <returns>void</returns>
        public void Execute()
        {
            if (string.IsNullOrWhiteSpace(mqConfig.InterfaceQueue))
            {
                throw new Exception("Process Receive start-queue config not found！");
            }

            var factory = new ConnectionFactory()
            {
                HostName = mqConfig.HostName,
                Port = mqConfig.Port ?? 5672,
                UserName = mqConfig.UserName,
                Password = mqConfig.Password
            };
            var connection = factory.CreateConnection();
            var channel = connection.CreateModel();
            channel.QueueDeclare(queue: mqConfig.InterfaceQueue, durable: false, exclusive: false, autoDelete: false, arguments: null);
            logger.LogInformation($"modeling connect watch queue:{mqConfig.InterfaceQueue}");
            var consumer = new EventingBasicConsumer(channel);
            consumer.Received += (model, ea) =>
            {
                var body = ea.Body.ToArray();
                var message = Encoding.UTF8.GetString(body);
                if (ea.BasicProperties.Headers != null)
                {
                    if (ea.BasicProperties.Headers.ContainsKey("endpointType") && ea.BasicProperties.Headers["endpointType"] != null)
                    {
                        string type = "approve";
                        switch (Encoding.UTF8.GetString((byte[])ea.BasicProperties.Headers["endpointType"]))
                        {
                            case "tripartite_system_instance_start":
                                type = "start";
                                break;
                            case "tripartite_system_instance_end":
                                type = "end";
                                break;
                        }
                        var dto = message.ToOurObject<CallDto>();
                        logger.LogInformation($"modeling connect receive message type:{type} instanceNumber:{dto.InstanceNumber}");
                        try
                        {
                            _processRelatedService.CallReceiveRecord(dto, type);
                        }
                        catch (Exception ex)
                        {
                            logger.LogInformation($"modeling connect receive error instanceNumber:{dto.InstanceNumber} message:{ex.Message}");
                        }
                    }
                }

                channel.BasicAck(ea.DeliveryTag, false);
            };
            channel.BasicConsume(queue: mqConfig.InterfaceQueue, autoAck: false, consumer: consumer);
        }
    }
}
