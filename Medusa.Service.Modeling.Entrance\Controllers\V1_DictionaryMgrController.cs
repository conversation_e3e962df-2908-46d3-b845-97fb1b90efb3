using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.DictionaryMgr;
using Medusa.Service.Modeling.Application.DictionaryMgr.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// DictionaryMgr controller
    /// </summary>
    [Route("v1/dictionary")]
    [ApiExplorerSettings(GroupName = "DictionaryMgr.v1")]
    public class V1_DictionaryMgrController : ProductControllerBase
    {
        #region //  服务注入
        readonly IDictionaryMgrService _dictionaryMgrService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_DictionaryMgrController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="dictionaryMgrService">dictionaryMgrService</param>
        public V1_DictionaryMgrController(IDictionaryMgrService dictionaryMgrService)
        {
            _dictionaryMgrService = dictionaryMgrService;
        }
        #endregion

        /// <summary>
        /// 获取数据字典分组信息
        /// </summary>
        /// <param name="isHasStructure">isHasStructure</param>
        /// <returns>returns</returns>
        [HttpGet("group")]
        public DictionaryGroupDto GetDictionaryGroup([FromQuery]bool isHasStructure)
        {
            return _dictionaryMgrService.GetDictionaryGroup(isHasStructure);
        }

        /// <summary>
        /// 获取字典数据
        /// </summary>
        /// <param name="groupId">分组Id</param>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("group/{groupId}")]
        public PageResult<Dictionary> GetDictionaryByGroupId([FromRoute]Guid groupId, DictionaryQueryDto dto)
        {
            return _dictionaryMgrService.GetDictionaryByGroupId(groupId, dto);
        }

        /// <summary>
        /// 新增/修改
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("save")]
        public void Save([FromBody] Dictionary dto)
        {
            _dictionaryMgrService.Save(dto);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">id</param>
        [HttpDelete("delete/{id}")]
        public void Delete([FromRoute] Guid id)
        {
            _dictionaryMgrService.Delete(id);
        }
    }
}
