using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text;
using Medusa.Service.Modeling.Core.Entity;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Application
{
    
    
    
    public class CommonHelper
    {
        
        
        
        
        
        public static ConditionalType GetOperatorFormat(JObject setting)
        {
            ConditionalType result = ConditionalType.Equal;
            switch (setting["operator"].ToString())
            {
                case "=":
                    result = ConditionalType.Equal;
                    break;
                case "!=":
                    result = ConditionalType.NoEqual;
                    break;
                case ">":
                    result = ConditionalType.GreaterThan;
                    break;
                case "<":
                    result = ConditionalType.LessThan;
                    break;
                case ">=":
                    result = ConditionalType.GreaterThanOrEqual;
                    break;
                case "<=":
                    result = ConditionalType.LessThanOrEqual;
                    break;
                case "like":
                    result = ConditionalType.Like;
                    break;
                case "leftLike":
                    result = ConditionalType.LikeLeft;
                    break;
                case "rightLike":
                    result = ConditionalType.LikeRight;
                    break;
            }

            return result;
        }

        
        
        
        
        
        
        public static Type CreateDataTableClass(BusinessObject table, List<BusinessObjectColumn> columns)
        {
            StringBuilder sbCode = new StringBuilder();
            sbCode.AppendLine("using System;");
            sbCode.AppendLine("using SqlSugar;");
            sbCode.AppendLine("namespace Medusa.Service.Modeling.Application.DataBaseManage");
            sbCode.AppendLine("{");
            sbCode.AppendLine($"[SugarTable(\"{table.Name}\", \"{table.Description}\")]");
            sbCode.AppendLine("public class TestCreateTable");
            sbCode.AppendLine("{");
            int count = 1;
            columns.ForEach(f =>
            {
                StringBuilder att = new StringBuilder();
                att.Append($"ColumnName = \"{f.Name}\",IsPrimaryKey = {f.IsPrimaryKey.ToString().ToLower()},IsNullable = {f.IsNullable.ToString().ToLower()},ColumnDataType = \"{f.Type}\",Length = {f.Length},DecimalDigits = {f.Decimal},ColumnDescription = \"{f.Description}\"");
                if (!string.IsNullOrEmpty(f.DefaultValue))
                {
                    att.Append($",DefaultValue = \"{f.DefaultValue}\"");
                }

                sbCode.AppendLine($"[SugarColumn({att.ToString()})]");
                sbCode.AppendLine($"public string abc{count} {{ get; set; }}");
                count++;
            });

            sbCode.AppendLine("}");
            sbCode.AppendLine("}");
            var compiler = new Compiler();
            var assembly = compiler.Compile(sbCode.ToString(), Assembly.Load(new AssemblyName("System.Runtime")), typeof(object).Assembly);
            var dyObject = assembly.GetType("Medusa.Service.Modeling.Application.DataBaseManage.TestCreateTable");
            return dyObject;
        }
    }
}
