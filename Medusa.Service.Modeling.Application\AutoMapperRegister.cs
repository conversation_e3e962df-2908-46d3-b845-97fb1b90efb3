using AutoMapper.Configuration;
using Medusa.Service.Modeling.Application.Authority.Profile;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Profile;
using Medusa.Service.Modeling.Application.CompositeObjectManage.Profile;
using Medusa.Service.Modeling.Application.DataBaseManage.Profile;
using Medusa.Service.Modeling.Application.PageModelingManage.Profile;
using Medusa.Service.Modeling.Application.Users;

namespace Medusa.Service.Modeling.Application
{
    
    
    
    public static class AutoMapperRegister
    {
        
        
        
        
        
        public static MapperConfigurationExpression AddApplicationMappers(this MapperConfigurationExpression config)
        {
            config.AddProfile<DataBaseManageProfile>();
            config.AddProfile<PageModelingManageProfile>();
            config.AddProfile<AuthorityProfile>();
            config.AddProfile<BusinessObjectManageProfile>();
            config.AddProfile<CompositeObjectManageProfile>();
            return config;
        }
    }
}