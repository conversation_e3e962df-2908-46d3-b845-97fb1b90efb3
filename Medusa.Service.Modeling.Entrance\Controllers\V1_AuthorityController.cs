using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.Authority;
using Medusa.Service.Modeling.Application.Authority.Dtos;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// DataBases controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "Authority.v1")]
    public class V1_AuthorityController : ProductControllerBase
    {
        #region //  服务注入
        readonly IAuthorityService _authorityService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_AuthorityController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="authorityService">authorityService</param>
        public V1_AuthorityController(IAuthorityService authorityService)
        {
            _authorityService = authorityService;
        }
        #endregion

        /// <summary>
        /// 获取业务系统信息
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("business-sys")]
        public PageResult<BusinessSysDto> GetBusinessSystems([FromQuery] BusinessSysQueryDto dto)
        {
            return _authorityService.GetBusinessSystems(dto);
        }

        /// <summary>
        /// 新增/修改业务系统信息
        /// </summary>
        /// <param name="dto">dto</param>
        [HttpPost("business-sys/save")]
        [ModelFieldCheck]
        public void SaveBusinessSystems([FromBody] BusinessSysDto dto)
        {
            _authorityService.SaveBusinessSystems(dto);
        }

        /// <summary>
        /// 删除业务系统信息
        /// </summary>
        /// <param name="id">id</param>
        [HttpPost("business-sys/{id}/delete")]
        public void DeleteBusinessSystems([FromRoute] Guid id)
        {
            _authorityService.DeleteBusinessSystems(id);
        }

        /// <summary>
        /// 获取业务对象权限树
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>returns</returns>
        [HttpGet("business-sys/{id}/business-object")]
        public List<BusinessObjectItemDto> GetBusinessObjectAuthority([FromRoute] Guid id)
        {
            return _authorityService.GetBusinessObjectAuthority(id);
        }

        /// <summary>
        /// 保存业务对象权限
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="dto">dto</param>
        [HttpPost("business-sys/{id}/business-object/save")]
        public void SaveBusinessObjectAuthority([FromRoute] Guid id, [FromBody] List<BusinessObjectItemDto> dto)
        {
            _authorityService.SaveBusinessObjectAuthority(id, dto);
        }

        /// <summary>
        /// 获取业务对象
        /// </summary>
        /// <param name="dto">dto</param>
        /// <returns>returns</returns>
        [HttpGet("business-sys/business-objects")]
        public List<BusinessObjectItemDto> GetBusinessObjects(BusinessObjectQueryDto dto)
        {
            return _authorityService.GetBusinessObjects(dto);
        }
    }
}
