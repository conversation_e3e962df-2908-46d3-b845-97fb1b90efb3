using System.Collections.Generic;
using Medusa.Service.Modeling.Application.HomePage;
using Medusa.Service.Modeling.Application.HomePage.Dto;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// 首页接口
    /// </summary>
    [Route("v1/homepage")]
    [ApiExplorerSettings(GroupName = "HomePage.v1")]
    public class V1_HomePageController : ProductControllerBase
    {
        #region //  服务注入
        readonly IHomePageService _homePageService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_HomePageController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="homePageService">homePageService</param>
        public V1_HomePageController(IHomePageService homePageService)
        {
            _homePageService = homePageService;
        }
        #endregion

        /// <summary>
        /// 获取提交给我的工作日报
        /// </summary>
        /// <returns>returns</returns>
        [HttpGet("submittedtome")]
        public GetSubmittedToMeDto GetSubmittedToMe()
        {
            return _homePageService.GetSubmittedToMe();
        }

        /// <summary>
        /// 获取我提交的工作日报
        /// </summary>
        /// <returns>returns</returns>
        [HttpGet("mysubmitted")]
        public List<DailyWorkReportDto> GetMySubmitted()
        {
            return _homePageService.GetMySubmitted();
        }

        /// <summary>
        /// 获取客诉分析
        /// </summary>
        /// <param name="queryDto">查询参数</param>
        /// <returns>客诉分析</returns>
        [HttpGet("customer-complaint")]
        public List<CustomerComplaintDto> GetCustomerComplaint(CustomerComplaintQueryDto queryDto)
        {
            return _homePageService.GetCustomerComplaint(queryDto);
        }

        /// <summary>
        /// 校验用户是否是领导
        /// </summary>
        /// <returns>是否</returns>
        [HttpGet("check-user")]
        public dynamic CheckUser()
        {
            return _homePageService.CheckUser();
        }
    }
}
