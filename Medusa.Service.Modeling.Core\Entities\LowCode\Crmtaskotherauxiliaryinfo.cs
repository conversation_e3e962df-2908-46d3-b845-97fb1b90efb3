using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Core.Entities.LowCode
{
    /// <summary>
    /// 其他任务附属信息
    /// </summary>
    [SugarTable("crmtaskotherauxiliaryinfo")]
    public class Crmtaskotherauxiliaryinfo
    {
        /// <summary>
        /// 表中的唯一标识ID
        /// </summary>
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true)]
        public Guid Id { get; set; }

        /// <summary>
        /// 任务主键ID
        /// </summary>
        [SugarColumn(ColumnName = "taskId")]
        public Guid TaskId { get; set; }

        /// <summary>
        /// 紧要程度
        /// </summary>
        [SugarColumn(ColumnName = "urgency")]
        public string Urgency { get; set; }

        /// <summary>
        /// 关联客户
        /// </summary>
        [SugarColumn(ColumnName = "relatedCustomer")]
        public string RelatedCustomer { get; set; }

        /// <summary>
        /// 参与人员
        /// </summary>
        [SugarColumn(ColumnName = "participants")]
        public string Participants { get; set; }

        /// <summary>
        /// 参与人员名称
        /// </summary>
        [SugarColumn(ColumnName = "participantNames")]
        public string ParticipantNames { get; set; }

        /// <summary>
        /// 发起人
        /// </summary>
        [SugarColumn(ColumnName = "initiator")]
        public string Initiator { get; set; }

        /// <summary>
        /// 任务提醒
        /// </summary>
        [SugarColumn(ColumnName = "taskReminder")]
        public string TaskReminder { get; set; }

        /// <summary>
        /// 提醒方式
        /// </summary>
        [SugarColumn(ColumnName = "reminderMethod")]
        public string ReminderMethod { get; set; }

        /// <summary>
        /// 是否删除 1已删除,0未删除
        /// </summary>
        [SugarColumn(ColumnName = "IsDelete")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建用户ID
        /// </summary>
        [SugarColumn(ColumnName = "createdUserId")]
        public string CreatedUserId { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [SugarColumn(ColumnName = "createdDate")]
        public DateTime? CreatedDate { get; set; }

        /// <summary>
        /// 修改日期
        /// </summary>
        [SugarColumn(ColumnName = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// 修改用户ID
        /// </summary>
        [SugarColumn(ColumnName = "modifiedUserId")]
        public string ModifiedUserId { get; set; }
    }
}
