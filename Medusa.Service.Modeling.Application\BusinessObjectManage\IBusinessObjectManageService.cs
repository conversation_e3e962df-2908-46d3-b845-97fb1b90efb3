using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using Medusa.Service.Modeling.Application.BusinessObjectManage.Dtos;
using Medusa.Service.Modeling.Application.Dtos;

namespace Medusa.Service.Modeling.Application.BusinessObjectManage
{
    
    
    
    public interface IBusinessObjectManageService : IServiceBase
    {
        
        
        
        
        
        PageResult<ObjectDto> GetBusinessObjects(BusinessObjectQueryDto dto);

        
        
        
        
        
        ObjectDto GetBusinessObject(Guid id);

        
        
        
        
        void SaveBusinessObject(ObjectDto dto);

        
        
        
        
        void DeleteBusinessObject(Guid id);

        
        
        
        
        void PublishBusinessObject(Guid id);

        
        
        
        
        
        DataTable GetBusinessObjectDataList(BOColumnDataDto dto);

        
        
        
        
        
        BOColumnDataDto GetBusinessObjectTreeList(Guid id);

        
        
        
        
        void UpdateBusinessObjectData(List<BOColumnValueDto> dto);

        
        
        
        
        
        PageResult<ExpandoObject> GetBusinessObjectTree(BusinessObjectTreeQuery dto);

        
        
        
        
        
        
        PageResult<ExpandoObject> GetBusinessObjectData(Guid id, BusinessObjectCommonQueryDto dto);

        
        
        
        
        
        
        int GetBusinessObjectDataCount(Guid id, BusinessObjectCommonQueryDto dto);

        
        
        
        
        
        
        bool CheckRepeatBusinessObjectData(Guid id, BusinessObjectCommonCheckDto dto);
    }
}
