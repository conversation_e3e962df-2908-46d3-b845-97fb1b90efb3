﻿<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>netcoreapp3.1</TargetFramework>
        <CodeAnalysisRuleSet>..\csharp.ruleset</CodeAnalysisRuleSet>
        <NoWin32Manifest>true</NoWin32Manifest>
        <UserSecretsId>0889ecb7-ed11-491a-acf7-02eec88a2a19</UserSecretsId>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
        <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Modeling.Entrance.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
        <DocumentationFile>bin\Release\netcoreapp3.1\Medusa.Service.Modeling.Entrance.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Medusa.Service.Modeling.Application\Medusa.Service.Modeling.Application.csproj" />
        <ProjectReference Include="..\Medusa.Service.Modeling.Core\Medusa.Service.Modeling.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Hangfire.Core" Version="1.7.9" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="5.5.1" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.5" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="3.1.7" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Dockerfile">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
