using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using Medusa.Service.Modeling.Application.Authority.Dtos;
using Medusa.Service.Modeling.Application.Dtos;
using Medusa.Service.Modeling.Application.DynamicSql;
using Medusa.Service.Modeling.Application.OperationLog;
using Medusa.Service.Modeling.Application.PageModelingManage;
using Medusa.Service.Modeling.Application.ProcessRelated.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.SDK.ApiRequest;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.ProcessRelated
{
    public class ProcessRelatedService : ServiceBase, IProcessRelatedService
    {
        #region 
        readonly IRequestConnector _requestConnector;
        readonly MyDbContext _dbContext;
        readonly string _businessObjectApi;
        readonly string _startProcessApi;
        readonly string _recallProcessApi;
        readonly string _cancelProcessApi;
        readonly string _startUrl;
        readonly string _reStartUrl;
        readonly IPageModelingManageService _pageModelingManageService;
        readonly IDynamicSqlService _dynamicSqlService;
        private readonly IOperationLogService _operationLogService;

        public ProcessRelatedService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _requestConnector = serviceProvider.GetService<IRequestConnector>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            _businessObjectApi = appSettings["ProcessSettings"]?["BusinessObject"]?.ToString();
            _startProcessApi = appSettings["ProcessSettings"]?["StartProcess"]?.ToString();
            _recallProcessApi = appSettings["ProcessSettings"]?["RecallProcess"]?.ToString();
            _cancelProcessApi = appSettings["ProcessSettings"]?["CancelProcess"]?.ToString();
            _startUrl = appSettings["ProcessSettings"]?["StartUrl"]?.ToString();
            _reStartUrl = appSettings["ProcessSettings"]?["ReStartUrl"]?.ToString();
            _pageModelingManageService = serviceProvider.GetService<IPageModelingManageService>();
            _dynamicSqlService = serviceProvider.GetService<IDynamicSqlService>();
            _operationLogService = serviceProvider.GetService<IOperationLogService>();
        }

        #endregion

        public List<BusinessObjectItemDto> GetOutsideBusinessObjects(string code, Guid dataBaseId)
        {
            var authority = _dbContext.Modeling.Queryable<BusinessSystem, BusinessSystemsAuthority>((a, b) => a.Code == code)
               .Where((a, b) => !a.IsDelete).Select((a, b) => b).ToList();

            var businessObjects = authority.Where(a => a.BusinessObjectType == 1).Select(a => a.BusinessObjectId).ToList();
            var compositeObjects = authority.Where(a => a.BusinessObjectType == 2).Select(a => a.BusinessObjectId).ToList();

            var businessObjects_l = _dbContext.Modeling.Queryable<BusinessObject>()
                .Where(a => !a.IsDelete.Value && a.State == 1 && a.DataBaseId == dataBaseId && businessObjects.Contains(a.Id))
                .Select(a => new BusinessObjectItemDto
                {
                    BusinessObjectId = a.Id,
                    BusinessObjectDisplayName = SqlFunc.IF(SqlFunc.IsNullOrEmpty(a.Description)).Return(a.Name).End(a.Description),
                    BusinessObjectType = 1
                }).ToList();

            var compositeObjects_l = _dbContext.Modeling.Queryable<CompositeObject>()
               .Where(a => !a.IsDelete.Value && a.DataBaseId == dataBaseId && compositeObjects.Contains(a.Id))
               .Select(a => new BusinessObjectItemDto
               {
                   BusinessObjectId = a.Id,
                   BusinessObjectDisplayName = a.Name,
                   BusinessObjectType = 2
               }).ToList();
            return businessObjects_l.Union(compositeObjects_l).ToList();
        }

        public OutsideBusinessObjectStructureDto GetOutsideBusinessObjectStructure(Guid id, int type)
        {
            var result = new OutsideBusinessObjectStructureDto();
            if (type == 1)
            {
                var item = _dbContext.Modeling.Queryable<BusinessObject>().InSingle(id);
                if (item == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.businessObject.notfound"));
                }

                result.Id = item.Id;
                result.Name = item.Name;
                result.Description = item.Description;
                result.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn, BusinessObject>((a, b) => a.BusinessObjectId == b.Id)
                    .Where(a => a.BusinessObjectId == item.Id && a.IsEnable.Value)
                    .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                    .OrderBy(a => a.IsSystemColumn)
                    .OrderBy(a => a.Order)
                    .Select((a, b) => new OutsideBusinessObjectColumnStructureDto
                    {
                        Id = b.Id.ToString() + ";" + a.Id.ToString(),
                        Name = b.Name + a.Name,
                        Description = a.Description,
                        DisplayType = a.DisplayType,
                        Order = a.Order,
                        Length = a.Length,
                        Decimal = a.Decimal
                    }).ToList();
            }
            else if (type == 2)
            {
                var guidEmpty = Guid.Empty;
                var item = _dbContext.Modeling.Queryable<CompositeObject, BusinessObject>((a, b) => a.BusinessObjectId == b.Id)
               .Where((a, b) => a.Id == id)
               .Select((a, b) => b).First();
                if (item == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.compositeObject.notfound"));
                }

                result.Id = item.Id;
                result.Name = item.Name;
                result.Description = item.Description;
                result.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn, BusinessObject>((a, b) => a.BusinessObjectId == b.Id)
                    .Where(a => a.BusinessObjectId == item.Id && a.IsEnable.Value)
                    .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                    .OrderBy(a => a.IsSystemColumn)
                    .OrderBy(a => a.Order)
                    .Select((a, b) => new OutsideBusinessObjectColumnStructureDto
                    {
                        Id = b.Id.ToString() + ";" + a.Id.ToString(),
                        Name = b.Name + a.Name,
                        Description = a.Description,
                        DisplayType = a.DisplayType,
                        Order = a.Order,
                        Length = a.Length,
                        Decimal = a.Decimal
                    }).ToList();
                var ls = FilterColumns(id, null);
                result.Columns.AddRange(ls.Item2);

                var children = new List<OutsideBusinessObjectStructureDto>();
                var details = _dbContext.Modeling.Queryable<CompositeObjectRelation, BusinessObject>((a, b) => a.BusinessObjectId == b.Id)
               .Where((a, b) => a.CompositeObjectId == id && a.ObjectType == "businessObject" && a.JoinRelation == 2
               && (ls.Item1.Contains(a.ParentId.Value) || SqlFunc.IsNullOrEmpty(a.ParentId)))
               .Select((a, b) => new OutsideBusinessObjectStructureDto
               {
                   Id = b.Id,
                   Name = b.Name,
                   Description = b.Description
               }).ToList();
                details.ForEach(f =>
                {
                    f.Columns = _dbContext.Modeling.Queryable<BusinessObjectColumn, BusinessObject>((a, b) => a.BusinessObjectId == b.Id)
                    .Where(a => a.BusinessObjectId == f.Id && a.IsEnable.Value)
                    .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                    .OrderBy(a => a.IsSystemColumn)
                    .OrderBy(a => a.Order)
                    .Select((a, b) => new OutsideBusinessObjectColumnStructureDto
                    {
                        Id = b.Id.ToString() + ";" + a.Id.ToString(),
                        Name = b.Name + a.Name,
                        Description = a.Description,
                        DisplayType = a.DisplayType,
                        Order = a.Order,
                        Length = a.Length,
                        Decimal = a.Decimal
                    }).ToList();
                });
                children.AddRange(details);
                result.Chilren = children;
            }

            return result;
        }

        
        
        
        
        public async Task SaveOutsideBusinessObjectData(OutsideBusinessObjectDataDto dto)
        {
            var history = _dbContext.Modeling.Queryable<ProcessDataWriteBackEntity>().Where(w => w.ProcInstNo == dto.Number).First();
            if (history != null && history.Data == Newtonsoft.Json.JsonConvert.SerializeObject(dto.FormData))
            {
                return;
            }

            if (history == null)
            {
                _dbContext.Modeling.Insertable<ProcessDataWriteBackEntity>(new ProcessDataWriteBackEntity()
                {
                    Id = Guid.NewGuid(),
                    ProcInstNo = dto.Number,
                    CreateDate = DateTime.Now,
                    Data = Newtonsoft.Json.JsonConvert.SerializeObject(dto.FormData),
                    AllData = Newtonsoft.Json.JsonConvert.SerializeObject(dto),
                }).ExecuteCommand();
            }
            else
            {
                history.ModifyDate = DateTime.Now;
                history.Data = Newtonsoft.Json.JsonConvert.SerializeObject(dto.FormData);
                history.AllData = Newtonsoft.Json.JsonConvert.SerializeObject(dto);
                _dbContext.Modeling.Updateable<ProcessDataWriteBackEntity>(history).ExecuteCommand();
            }
            var url = _businessObjectApi.Replace("{id}", dto.ProcessId.ToString()) + "?processVersion=" + dto.ProcessVersion;
            var response = await _requestConnector.GetAsync<BusinessObjectDetailDto>(url);
            var structure = response.ResouceContent;
            if (structure != null && structure.Fields.Count > 0 && dto.FormData != null && dto.FormData.Count > 0)
            {
                var tables = _pageModelingManageService.GetBusinessObjectFields(new PageModelingManage.Dtos.ObjectColumnQueryDto { ObjectId = Guid.Parse(structure.Fields[0].OutsideBusinessObjectInfo.Split('|')[1]), ObjectType = int.Parse(structure.Fields[0].OutsideBusinessObjectInfo.Split('|')[0]) });
                var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(tables[0].DataBaseId);
                if (dataBase == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
                }
                List<string> onlyView = new List<string>();
                if (!string.IsNullOrEmpty(dto.CallBackSetting))
                {
                    JObject jo = JObject.Parse(dto.CallBackSetting);
                    var setting = JObject.Parse(jo["setting"].ToString());
                    if (setting.ContainsKey("dataWriteBack") && !string.IsNullOrEmpty(setting["dataWriteBack"].ToString()))
                    {
                        JObject dataWriteBack = JObject.Parse(setting["dataWriteBack"].ToString());
                        onlyView = dataWriteBack["onlyView"].ToString().ToOurObject<List<string>>();
                    }
                }

                SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
                var zb = tables.Where(w => w.IsMain && w.ObjectType == "businessObject").FirstOrDefault();
                if (zb != null && !onlyView.Contains(zb.Name))
                {
                    var dic = new Dictionary<string, object>();
                    zb.Columns.Where(w => !w.IsSystemColumn && !onlyView.Contains($"{zb.Name}_{w.Name}")).ToList().ForEach(f =>
                    {
                        var column = structure.Fields.Where(s => s.Code == $"{zb.Name}{f.Name}" && s.OutsideBusinessObjectInfo.IndexOf($"{zb.Id};{f.Id}") > -1).FirstOrDefault();
                        if (column != null && dto.FormData.ContainsKey(column.Code))
                        {
                            var da = OutsideDataFormat(f, dto.FormData[column.Code]);
                            if (da.isOk)
                            {
                                dic.Add(f.Name, da.result);
                            }
                        }
                    });

                    if (dic.Count > 0)
                    {
                        
                        var zj = dto.FormData.ContainsKey($"{zb.Name}ID") ? dto.FormData[$"{zb.Name}ID"].ToString() : string.Empty;
                        if (string.IsNullOrEmpty(zj))
                        {
                            
                            var zbkey = _dbContext.Modeling.Queryable<ProcessInstanceRecord>().Where(w => w.ProcInstNo == dto.Number).ToList();
                            zj = zbkey != null && zbkey.Count > 0 ? zbkey[0].DataId.ToString() : string.Empty;
                        }

                        if (!string.IsNullOrEmpty(zj))
                        {
                            SaveDic(dic, zj, "ID");
                            SaveDic(dic, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "ModifyDate");
                            SaveDic(dic, !string.IsNullOrEmpty(dto.StartUserId) ? dto.StartUserId : string.Empty, "ModifyUserId");
                            client.Updateable(dic).AS(zb.Name).WhereColumns("ID").ExecuteCommand();
                        }
                        else
                        {
                            SaveDic(dic, Guid.NewGuid(), "ID");
                            SaveDic(dic, 0, "IsDelete");
                            SaveDic(dic, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "CreateDate");
                            SaveDic(dic, !string.IsNullOrEmpty(dto.OwnerUserId) ? dto.OwnerUserId : string.Empty, "CreateUserId");
                            SaveDic(dic, dto.OrganizationPath, "CreateUserOrgPathId");
                            client.Insertable(dic).AS(zb.Name).ExecuteCommand();

                            _dbContext.Modeling.Insertable<ProcessInstanceRecord>(new ProcessInstanceRecord()
                            {
                                Id = Guid.NewGuid().ToString(),
                                DataId = dic["ID"].ToString(),
                                Boid = dto.Boid,
                                Btid = dto.Btid,
                                Bsid = dto.Bsid,
                                ProcInstNo = dto.Number,
                                BusinessObjectId = zb.Id.ToString(),
                                BusinessObjectName = zb.Name,
                                DataBaseId = zb.DataBaseId.ToString(),
                                CreateDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                CreateUserId = string.Empty,
                                Data = Newtonsoft.Json.JsonConvert.SerializeObject(dto.FormData),
                            }).ExecuteCommand();
                        }

                        SaveDic(dto.FormData, dic["ID"], $"{zb.Name}ID");
                        var zbid = new List<Guid>();
                        RecursionSaveOutsideBusinessObjectData(tables, dto, onlyView, structure.Fields, zbid, client, zb, null);
                        var db = tables.Where(w => !w.IsMain && w.ObjectType == "businessObject" && w.JoinRelation == 2 && ((w.ParentId.HasValue && zbid.Contains(w.ParentId.Value)) || !w.ParentId.HasValue)).ToList();
                        db.ForEach(f =>
                        {
                            var parentTable = f.ParentId.HasValue ? tables.Where(w => w.CompositeObjectRelationId == f.ParentId.Value).FirstOrDefault() : zb;
                            var parentColumn = parentTable.Columns.Where(w => w.Id == f.MainDataTableColumnId).FirstOrDefault();
                            var detailColumn = f.Columns.Where(w => w.Id == f.DetailDataTableColumnId).FirstOrDefault();
                            var tableStructure = structure.Fields.Where(w => w.Code == f.Name && w.Type == "table").FirstOrDefault();
                            if (dto.FormData.ContainsKey(f.Name) && tableStructure != null && !onlyView.Contains(f.Name))
                            {
                                var wjData = dto.FormData[$"{parentTable.Name}{parentColumn.Name}"];
                                var jarray = JArray.Parse(dto.FormData[f.Name].ToString());
                                var upList = new List<Dictionary<string, object>>();
                                var inList = new List<Dictionary<string, object>>();
                                foreach (var ja in jarray)
                                {
                                    var ddic = new Dictionary<string, object>();
                                    var jobject = JObject.Parse(ja.ToString());
                                    f.Columns.Where(w => !w.IsSystemColumn && !onlyView.Contains($"{f.Name}_{w.Name}")).ToList().ForEach(fe =>
                                    {
                                        var column = tableStructure.Children.Where(s => s.Code == $"{f.Name}{fe.Name}" && s.OutsideBusinessObjectInfo.IndexOf($"{f.Id};{fe.Id}") > -1).FirstOrDefault();
                                        if (column != null && jobject.ContainsKey(column.Code))
                                        {
                                            var da = OutsideDataFormat(fe, jobject[column.Code]);
                                            if (da.isOk)
                                            {
                                                ddic.Add(fe.Name, da.result);
                                            }
                                        }
                                    });
                                    SaveDic(ddic, 0, "IsDelete");
                                    SaveDic(ddic, wjData, detailColumn.Name);
                                    var zj = jobject.ContainsKey($"{f.Name}ID") ? jobject[$"{f.Name}ID"].ToString() : string.Empty;
                                    if (!string.IsNullOrEmpty(zj))
                                    {
                                        SaveDic(ddic, zj, "ID");
                                        SaveDic(ddic, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "ModifyDate");
                                        SaveDic(ddic, !string.IsNullOrEmpty(dto.StartUserId) ? dto.StartUserId : string.Empty, "ModifyUserId");
                                        upList.Add(ddic);
                                    }
                                    else
                                    {
                                        SaveDic(ddic, Guid.NewGuid(), "ID");
                                        SaveDic(ddic, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "CreateDate");
                                        SaveDic(ddic, !string.IsNullOrEmpty(dto.OwnerUserId) ? dto.OwnerUserId : string.Empty, "CreateUserId");
                                        SaveDic(ddic, dto.OrganizationPath, "CreateUserOrgPathId");
                                        inList.Add(ddic);
                                    }
                                }

                                var udic = new Dictionary<string, object>();
                                udic.Add("IsDelete", 1);
                                client.Updateable(udic).AS(f.Name).Where($"{detailColumn.Name}='{wjData}'").ExecuteCommand();

                                if (upList.Count > 0)
                                {
                                    client.Updateable(upList).AS(f.Name).WhereColumns("ID").ExecuteCommand();
                                }

                                if (inList.Count > 0)
                                {
                                    client.Insertable(inList).AS(f.Name).ExecuteCommand();
                                }
                            }
                        });
                    }
                }
            }
        }

        
        
        
        
        
        public Dictionary<string, object> GetBusinessObjectData(OutsideBusinessObjectDataSearchDto dto)
        {
            return null;
        }

        public async Task<string> Start(PorcessDto dto)
        {
            if (!string.IsNullOrEmpty(dto.Bsid) && !string.IsNullOrEmpty(dto.Btid) && !string.IsNullOrEmpty(dto.BusinessObjectId))
            {
                var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(dto.DataBaseId);
                if (dataBase == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
                }

                var bs = dto.BusinessObjectId.Split("||");
                var fields = _pageModelingManageService.GetBusinessObjectFields(new PageModelingManage.Dtos.ObjectColumnQueryDto { ObjectId = Guid.Parse(bs[0]), ObjectType = int.Parse(bs[1]) });

                var mainTable = fields.Where(w => w.Name == dto.MainTableName && w.ObjectType == "businessObject").FirstOrDefault();
                if (mainTable != null)
                {
                    var jobject = new JObject();
                    jobject.Add("bsid", dto.Bsid);
                    jobject.Add("btid", dto.Btid);
                    var selectList = new List<string>();
                    var businessDate = new Dictionary<string, string>();
                    var ls = new List<Guid>();
                    SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
                    ISugarQueryable<ExpandoObject> query = client.Queryable(mainTable.Name, "Cust" + mainTable.Name);
                    mainTable.Columns.ForEach(f =>
                    {
                        selectList.Add($"Cust{mainTable.Name}.{f.Name} as {mainTable.Name}{f.Name}");
                        businessDate.Add($"{mainTable.Name}{f.Name}", f.DisplayType);
                    });
                    FilterColumns(selectList, businessDate, ls, query, fields, mainTable, null);
                    var column = mainTable.Columns.Find(f => f.IsPrimaryKey.Value).Name;
                    var conditonModels = new List<IConditionalModel>();
                    conditonModels.Add(new ConditionalModel()
                    {
                        FieldName = $"Cust{mainTable.Name}.{column}",
                        ConditionalType = ConditionalType.Equal,
                        FieldValue = dto.ModalKey.ToString()
                    });
                    if (mainTable.IsLogicalDelete.HasValue && mainTable.IsLogicalDelete.Value)
                    {
                        conditonModels.Add(new ConditionalModel()
                        {
                            FieldName = $"Cust{mainTable.Name}.IsDelete",
                            ConditionalType = ConditionalType.Equal,
                            FieldValue = "0"
                        });
                    }

                    query.Select(string.Join(",", selectList));
                    query.Where(conditonModels);
                    var mainTableDate = query.First();
                    if (mainTableDate != null)
                    {
                        var business = new JObject();
                        var mainTableDateObject = JToken.FromObject(mainTableDate);
                        foreach (var k in businessDate.Keys)
                        {
                            business.Add(k, FieldFormat(businessDate[k], mainTableDateObject[k].ToString()));
                        }

                        var details = fields.Where(w => w.ObjectType == "businessObject" && !w.IsMain && w.JoinRelation == 2
                        && (!w.ParentId.HasValue || ls.Contains(w.ParentId.Value))).ToList();
                        details.ForEach(df =>
                        {
                            var selectListD = new List<string>();
                            var businessDateD = new Dictionary<string, string>();
                            var lsD = new List<Guid>();
                            JArray fs = new JArray();
                            ISugarQueryable<ExpandoObject> queryD = client.Queryable(df.Name, "Cust" + df.Name);
                            df.Columns.ForEach(f =>
                            {
                                selectListD.Add($"Cust{df.Name}.{f.Name} as {df.Name}{f.Name}");
                                businessDateD.Add($"{df.Name}{f.Name}", f.DisplayType);
                            });

                            FilterColumns(selectListD, businessDateD, lsD, queryD, fields, df, df.CompositeObjectRelationId);

                            var detailDataTableColumn = df.Columns.Find(f => f.Id == df.DetailDataTableColumnId).Name;
                            var parentDatatableColumn = string.Empty;
                            if (df.ParentId.HasValue)
                            {
                                var parent = fields.Find(w => w.CompositeObjectRelationId == df.ParentId.Value);
                                parentDatatableColumn = parent.Name + parent.Columns.Find(f => f.Id == df.MainDataTableColumnId).Name;
                            }
                            else
                            {
                                parentDatatableColumn = mainTable.Name + mainTable.Columns.Find(f => f.Id == df.MainDataTableColumnId).Name;
                            }

                            var conditonModelsD = new List<IConditionalModel>();
                            if (!string.IsNullOrEmpty(detailDataTableColumn) && !string.IsNullOrEmpty(parentDatatableColumn))
                            {
                                conditonModelsD.Add(new ConditionalModel()
                                {
                                    FieldName = $"Cust{df.Name}.{detailDataTableColumn}",
                                    ConditionalType = ConditionalType.Equal,
                                    FieldValue = mainTableDateObject[parentDatatableColumn].ToString()
                                });
                            }

                            if (df.IsLogicalDelete.HasValue && df.IsLogicalDelete.Value)
                            {
                                conditonModelsD.Add(new ConditionalModel()
                                {
                                    FieldName = $"Cust{df.Name}.IsDelete",
                                    ConditionalType = ConditionalType.Equal,
                                    FieldValue = "0"
                                });
                            }

                            queryD.Select(string.Join(",", selectListD));
                            queryD.Where(conditonModelsD);
                            var str = queryD.ToSql().ToString();
                            var detailTableDatas = queryD.ToList();
                            if (detailTableDatas != null && detailTableDatas.Count > 0)
                            {
                                var arr = new JArray();
                                foreach (var detailTableData in detailTableDatas)
                                {
                                    var detailTableDataObject = JObject.FromObject(detailTableData);
                                    var businessD = new JObject();
                                    foreach (var k in businessDateD.Keys)
                                    {
                                        businessD.Add(k, FieldFormat(businessDateD[k], detailTableDataObject[k].ToString()));
                                    }

                                    arr.Add(businessD);
                                }

                                business.Add(df.Name, arr);
                            }
                        });

                        jobject.Add("businessData", business);
                        var userId = string.Empty;
                        var organizationId = string.Empty;
                        if (dto.StartUserId[0] == "loginUser" && dto.StartUserId[1] == "loginUserId")
                        {
                            userId = dto.CurrentUserId;
                        }
                        else
                        {
                            userId = mainTableDateObject[$"{dto.StartUserId[0]}_{dto.StartUserId[1]}"].ToString();
                        }

                        if (dto.OrganizationId[0] == "loginUser" && dto.OrganizationId[1] == "loginUserOrganizationId")
                        {
                            organizationId = dto.CurrentUserOrganizationId;
                        }
                        else
                        {
                            organizationId = mainTableDateObject[$"{dto.OrganizationId[0]}_{dto.OrganizationId[1]}"].ToString();
                        }

                        jobject.Add("startUserId", userId);
                        jobject.Add("organizationId", organizationId);
                        jobject.Add("businessObjectVersion", Guid.NewGuid());

                        var boid = Guid.NewGuid().ToString();

                        var record = _dbContext.Modeling.Queryable<ProcessInstanceRecord>().Where(w => w.DataId == dto.ModalKey.ToString() && w.BusinessObjectId == mainTable.Id.ToString()
                        && w.DataBaseId == dto.DataBaseId.ToString() && w.Btid == dto.Btid && w.Bsid == dto.Bsid).OrderByDescending(w => w.CreateDate).First();
                        var currentUserId = UserConstants.CurrentUser.Value.Account;
                        var callBack = new JObject();
                        callBack.Add("setting", dto.CallBackSetting);
                        var startUrl = string.Empty;
                        if (record == null ||
                            (!string.IsNullOrEmpty(record.ProcInstNo) && (record.ProcInstStatus == "refused" || record.ProcInstStatus == "approved" || record.ProcInstStatus == "canceled")))
                        {
                            jobject.Add("boid", boid);
                            var start = new ProcessInstanceRecord()
                            {
                                Id = Guid.NewGuid().ToString(),
                                DataId = dto.ModalKey.ToString(),
                                BusinessObjectId = mainTable.Id.ToString(),
                                BusinessObjectName = mainTable.Name,
                                DataBaseId = dto.DataBaseId.ToString(),
                                CreateDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                CreateUserId = currentUserId,
                                Data = jobject.ToString(),
                                CallBackSetting = callBack.ToString(),
                                OperationStatus = "ready",
                                Boid = boid,
                                Btid = dto.Btid,
                                Bsid = dto.Bsid
                            };
                            _dbContext.Modeling.Insertable<ProcessInstanceRecord>(start).ExecuteCommand();
                            startUrl = _startUrl.Replace("{bsid}", dto.Bsid).Replace("{btid}", dto.Btid).Replace("{boid}", boid);
                            var content = await _requestConnector.PostAsync<string>(_startProcessApi, jobject);
                            if (content.IsSuccessStatusCode)
                            {
                                await _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                                {
                                    Menu = dto.Menu,
                                    Action = dto.Action,
                                    OperationType = "发起流程"
                                });
                                return startUrl;
                            }
                        }
                        else
                        {
                            boid = !string.IsNullOrEmpty(record.Boid) ? record.Boid : record.DataId;
                            if (!string.IsNullOrEmpty(record.ProcInstNo) && (record.ProcInstStatus == "rejected" || record.ProcInstStatus == "ready" || record.ProcInstStatus == "recallstart"))
                            {
                                startUrl = _reStartUrl.Replace("{procinstno}", record.ProcInstNo).Replace("{bsid}", dto.Bsid).Replace("{btid}", dto.Btid).Replace("{boid}", boid);
                            }
                            else if (string.IsNullOrEmpty(record.ProcInstNo) || string.IsNullOrEmpty(record.ProcInstStatus))
                            {
                                startUrl = _startUrl.Replace("{bsid}", dto.Bsid).Replace("{btid}", dto.Btid).Replace("{boid}", boid);
                            }
                            else
                            {
                                return string.Empty;
                            }

                            jobject.Add("boid", boid);
                            record.CallBackSetting = callBack.ToString();
                            record.Data = jobject.ToString();
                            record.ModifyDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                            record.ModifyUserId = currentUserId;
                            _dbContext.Modeling.Updateable<ProcessInstanceRecord>(record).ExecuteCommand();

                            var content = await _requestConnector.PostAsync<string>(_startProcessApi, jobject);
                            if (content.IsSuccessStatusCode)
                            {
                                await _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                                {
                                    Menu = dto.Menu,
                                    Action = dto.Action,
                                    OperationType = "发起流程"
                                });
                                return startUrl;
                            }
                        }
                    }
                }
            }

            return string.Empty;
        }

        public async Task Recall(PorcessDto dto)
        {
            if (!string.IsNullOrEmpty(dto.Btid) && !string.IsNullOrEmpty(dto.Bsid))
            {
                var record = _dbContext.Modeling.Queryable<ProcessInstanceRecord>().Where(w => w.DataId == dto.ModalKey.ToString()
                && w.Btid == dto.Btid && w.Bsid == dto.Bsid
                && w.BusinessObjectId == dto.MainTableId.ToString()
                      && w.DataBaseId == dto.DataBaseId.ToString()).OrderByDescending(w => w.CreateDate).First();
                if (record == null || string.IsNullOrEmpty(record.ProcInstNo))
                {
                    throw new StatusNotFoundException("流程实例不存在或未发起");
                }

                if (record != null && record.OperationStatus == "middlestatus")
                {
                    throw new StatusNotFoundException("流程处理中请不要重复操作");
                }

                var recall = new ProcessRecallRecord()
                {
                    Id = Guid.NewGuid(),
                    DataId = dto.ModalKey,
                    Boid = record.Boid,
                    Btid = dto.Btid,
                    Bsid = dto.Bsid,
                    BusinessObjectId = dto.MainTableId,
                    BusinessObjectName = dto.MainTableName,
                    DataBaseId = dto.DataBaseId,
                    CreateDate = DateTime.Now,
                    Data = string.Empty
                };
                _dbContext.Modeling.Insertable<ProcessRecallRecord>(recall).ExecuteCommand();
                record.OperationStatus = "middlestatus";
                _dbContext.Modeling.Updateable<ProcessInstanceRecord>(record).ExecuteCommand();
                await _requestConnector.PutAsync<string>(_recallProcessApi.Replace("{procinstno}", record.ProcInstNo), null);
                await _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                {
                    Menu = dto.Menu,
                    Action = dto.Action,
                    OperationType = "撤回流程"
                });
            }
        }

        public async Task Cancel(PorcessDto dto)
        {
            if (!string.IsNullOrEmpty(dto.Btid) && !string.IsNullOrEmpty(dto.Bsid))
            {
                var record = _dbContext.Modeling.Queryable<ProcessInstanceRecord>().Where(w => w.DataId == dto.ModalKey.ToString()
                && w.Btid == dto.Btid && w.Bsid == dto.Bsid
                && w.BusinessObjectId == dto.MainTableId.ToString()
                      && w.DataBaseId == dto.DataBaseId.ToString()).First();
                if (record == null || string.IsNullOrEmpty(record.ProcInstNo))
                {
                    throw new StatusNotFoundException("流程实例不存在或未发起");
                }

                if (record != null && record.OperationStatus == "middlestatus")
                {
                    throw new StatusNotFoundException("流程处理中请不要重复操作");
                }

                var cancel = new ProcessCancelRecord()
                {
                    Id = Guid.NewGuid(),
                    DataId = dto.ModalKey,
                    Boid = record.Boid,
                    Btid = dto.Btid,
                    Bsid = dto.Bsid,
                    BusinessObjectId = dto.MainTableId,
                    BusinessObjectName = dto.MainTableName,
                    DataBaseId = dto.DataBaseId,
                    CreateDate = DateTime.Now,
                    Data = string.Empty
                };
                _dbContext.Modeling.Insertable<ProcessRecallRecord>(cancel).ExecuteCommand();
                record.OperationStatus = "middlestatus";
                _dbContext.Modeling.Updateable<ProcessInstanceRecord>(record).ExecuteCommand();
                await _requestConnector.PutAsync<string>(_cancelProcessApi.Replace("{procinstno}", record.ProcInstNo), null);
                await _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                {
                    Menu = dto.Menu,
                    Action = dto.Action,
                    OperationType = "作废流程"
                });
            }
        }

        public void CallReceiveRecord(CallDto dto, string type)
        {
            var record = new ProcessReceiveRecord()
            {
                Id = Guid.NewGuid(),
                Boid = dto.Boid,
                Btid = dto.Btid,
                Bsid = dto.Bsid,
                Data = Newtonsoft.Json.JsonConvert.SerializeObject(dto),
                CreateDate = DateTime.Now,
                Type = type
            };
            _dbContext.Modeling.Insertable<ProcessReceiveRecord>(record).ExecuteCommand();
            if (!string.IsNullOrEmpty(dto.Boid) && !string.IsNullOrEmpty(dto.Btid) && !string.IsNullOrEmpty(dto.Bsid))
            {
                var instanceStatus = dto.InstanceStatus.Trim().ToOurLowerString();
                var detailStatus = dto.DetailStatus.Trim().ToOurLowerString();
                var instance = _dbContext.Modeling.Queryable<ProcessInstanceRecord>().Where(w => w.Boid == dto.Boid
                && w.Btid == dto.Btid && w.Bsid == dto.Bsid).OrderByDescending(w => w.CreateDate).First();
                if (instance != null && instance.OperationStatus != "finish")
                {
                    if (string.IsNullOrEmpty(instance.ProcInstNo))
                    {
                        instance.ProcInstNo = dto.InstanceNumber;
                    }

                    if (string.IsNullOrEmpty(instance.Topic))
                    {
                        instance.Topic = dto.InstanceTopic;
                    }

                    string status = "processing";
                    string statusName = "审批中";
                    string operationStatus = "processing";
                    if (instanceStatus == "ready")
                    {
                        status = "ready";
                        statusName = "待发起";
                        operationStatus = "againready";
                    }
                    else if (instanceStatus == "refused")
                    {
                        status = "refused";
                        statusName = "不通过";
                        operationStatus = "finish";
                    }
                    else if (instanceStatus == "approved")
                    {
                        status = "approved";
                        statusName = "通过";
                        operationStatus = "finish";
                    }
                    else if (instanceStatus == "canceled")
                    {
                        status = "canceled";
                        statusName = "作废";
                        operationStatus = "finish";
                    }
                    else if (instanceStatus == "processing")
                    {
                        if (detailStatus == "rejectstartdirect" || detailStatus == "rejectstart")
                        {
                            status = "rejected";
                            statusName = "退回修改";
                            operationStatus = "againready";
                        }
                        else if (detailStatus == "recallstart")
                        {
                            status = "ready";
                            statusName = "待发起";
                            operationStatus = "againready";
                        }
                        else if (detailStatus == "refused")
                        {
                            status = "refused";
                            statusName = "不通过";
                            operationStatus = "finish";
                        }
                        else if (detailStatus == "canceled")
                        {
                            status = "canceled";
                            statusName = "作废";
                            operationStatus = "finish";
                        }
                    }

                    instance.ProcInstStatus = status;
                    instance.ProcInstStatusName = statusName;
                    instance.OperationStatus = operationStatus;
                    _dbContext.Modeling.Updateable<ProcessInstanceRecord>(instance).ExecuteCommand();

                    if (dto.VirtualObjectDataWriteback.HasValue && dto.VirtualObjectDataWriteback.Value)
                    {
                        this.SaveOutsideBusinessObjectData(new OutsideBusinessObjectDataDto
                        {
                            Boid = dto.Boid,
                            Bsid = dto.Bsid,
                            Btid = dto.Btid,
                            Number = dto.InstanceNumber,
                            ProcessId = Guid.Parse(dto.ProcessId),
                            ProcessVersion = dto.ProcessVersion,
                            FormData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(dto.BusinessData.ToString()),
                            CallBackSetting = instance.CallBackSetting
                        });
                    }

                    if (!string.IsNullOrEmpty(instance.CallBackSetting))
                    {
                        JObject jo = JObject.Parse(instance.CallBackSetting);
                        JArray jACallBack = null;
                        var setting = JObject.Parse(jo["setting"].ToString());
                        if (type == "start")
                        {
                            jACallBack = JArray.Parse(setting["startCallBack"].ToString());
                        }
                        else if (type == "approve")
                        {
                            jACallBack = JArray.Parse(setting["approvalCallBack"].ToString());
                        }
                        else if (type == "end")
                        {
                            jACallBack = JArray.Parse(setting["endCallBack"].ToString());
                        }

                        if (jACallBack != null && jACallBack.Count > 0)
                        {
                            this.ProcessCallBackExec(instance, jACallBack);
                        }
                    }
                }
            }
        }

        public async Task<List<ExpandoObject>> OutsideVerification(OutsideVerificationDto dto)
        {
            if (dto.Type == "repeat" && dto.DataValue != null && !string.IsNullOrEmpty(dto.DataValue.ToString()))
            {
                var url = _businessObjectApi.Replace("{id}", dto.ProcessId.ToString()) + "?processVersion=" + dto.ProcessVersion;
                var structure = await _requestConnector.GetAsync<BusinessObjectDetailDto>(url);
                if (structure.ResouceContent != null && structure.ResouceContent.Fields.Count > 0)
                {
                    if (dto.DataType == "mainField")
                    {
                        var field = structure.ResouceContent.Fields.Where(s => s.Code == dto.DataKey).FirstOrDefault();
                        if (field != null)
                        {
                            var tables = _pageModelingManageService.GetBusinessObjectFields(new PageModelingManage.Dtos.ObjectColumnQueryDto
                            {
                             ObjectId = Guid.Parse(field.OutsideBusinessObjectInfo.Split('|')[1]),
                             ObjectType = int.Parse(field.OutsideBusinessObjectInfo.Split('|')[0])
                            });
                            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(tables[0].DataBaseId);
                            if (dataBase == null)
                            {
                                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
                            }

                            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
                            var zb = tables.Where(w => w.IsMain).FirstOrDefault();
                            var pkey = zb.Columns.Where(w => w.IsPrimaryKey.Value).FirstOrDefault();
                            var dataKey = zb.Columns.Where(w => w.Name == dto.DataKey).FirstOrDefault();
                            if (zb != null && pkey != null && dataKey != null)
                            {
                                ISugarQueryable<ExpandoObject> query = client.Queryable(zb.Name, "Cust" + zb.Name);
                                query.Select($"Cust{zb.Name}.{pkey.Name}");
                                var conditonModels = new List<IConditionalModel>();
                                var res = OutsideDataFormat(dataKey, dto.DataValue);
                                if (res.isOk)
                                {
                                    conditonModels.Add(new ConditionalModel()
                                    {
                                        FieldName = $"Cust{zb.Name}.{dataKey.Name}",
                                        ConditionalType = ConditionalType.Equal,
                                        FieldValue = res.result.ToString()
                                    });

                                    if (zb.IsLogicalDelete.Value)
                                    {
                                        conditonModels.Add(new ConditionalModel()
                                        {
                                            FieldName = $"Cust{zb.Name}.IsDelete",
                                            ConditionalType = ConditionalType.Equal,
                                            FieldValue = "0"
                                        });
                                    }

                                    if (!string.IsNullOrEmpty(dto.Boid))
                                    {
                                        conditonModels.Add(new ConditionalModel()
                                        {
                                            FieldName = $"Cust{zb.Name}.{pkey.Name}",
                                            ConditionalType = ConditionalType.NoEqual,
                                            FieldValue = dto.Boid
                                        });
                                    }
                                    else if (!string.IsNullOrEmpty(dto.Number))
                                    {
                                        query.AddJoinInfo("ProcessInstanceRecord", "CustProcessInstanceRecord", $"Cust{zb.Name}.{pkey.Name} = CustProcessInstanceRecord.DataId", JoinType.Left);
                                        conditonModels.Add(new ConditionalModel()
                                        {
                                            FieldName = "CustProcessInstanceRecord.DataId",
                                            ConditionalType = ConditionalType.NoEqual,
                                            FieldValue = dto.Number
                                        });
                                    }

                                    query.Where(conditonModels);
                                    return query.ToList();
                                }
                            }
                        }
                    }
                }
            }

            return null;
        }

        private void ProcessCallBackExec(ProcessInstanceRecord instance, JArray callBack)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(instance.DataBaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);

            foreach (var call in callBack)
            {
                var callObject = JObject.Parse(call.ToString());
                if (callObject["type"].ToString() == "update")
                {
                    var businessObject = _dbContext.Modeling.Queryable<BusinessObject>().Where(w => !w.IsDelete.Value && w.State.Value == 1
                    && w.Id == SqlFunc.ToGuid(callObject["businessObjectId"].ToString())).First();
                    if (businessObject != null)
                    {
                        var conditonModels_proc = new List<IConditionalModel>();
                        conditonModels_proc.Add(new ConditionalModel()
                        {
                            FieldName = "Id",
                            ConditionalType = ConditionalType.Equal,
                            FieldValue = instance.Id
                        });

                        var conditonModels = new List<IConditionalModel>();
                        conditonModels.Add(new ConditionalModel()
                        {
                            FieldName = "ID",
                            ConditionalType = ConditionalType.Equal,
                            FieldValue = instance.DataId
                        });
                        var ddt_proc = new List<KeyValuePair<WhereType, ConditionalModel>>();
                        var ddt = new List<KeyValuePair<WhereType, ConditionalModel>>();
                        var gxtj_array = JArray.Parse(callObject["condition"].ToString());
                        foreach (var gxtj in gxtj_array)
                        {
                            JObject gxtj_object = JObject.Parse(gxtj.ToString());
                            if (!string.IsNullOrEmpty(gxtj_object["field"].ToString()))
                            {
                                JArray gxtj_object_field_array = JArray.Parse(gxtj_object["field"].ToString());
                                if (gxtj_object_field_array.Count > 1 && !string.IsNullOrEmpty(gxtj_object_field_array[1].ToString()))
                                {
                                    if (gxtj_object_field_array[0].ToString() == "ProcessInstanceRecord")
                                    {
                                        var cModel = new ConditionalModel()
                                        {
                                            FieldName = gxtj_object_field_array[1].ToString(),
                                            ConditionalType = CommonHelper.GetOperatorFormat(gxtj_object),
                                            FieldValue = gxtj_object["fieldValue"].ToString()
                                        };
                                        ddt_proc.Add(new KeyValuePair<WhereType, ConditionalModel>(callObject["relation"].ToString() == "and" ? WhereType.And : WhereType.Or, cModel));
                                    }
                                    else
                                    {
                                        var cModel = new ConditionalModel()
                                        {
                                            FieldName = gxtj_object_field_array[1].ToString(),
                                            ConditionalType = CommonHelper.GetOperatorFormat(gxtj_object),
                                            FieldValue = gxtj_object["fieldValue"].ToString()
                                        };
                                        ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(callObject["relation"].ToString() == "and" ? WhereType.And : WhereType.Or, cModel));
                                    }
                                }
                            }
                        }

                        if (ddt_proc.Count > 0)
                        {
                            conditonModels_proc.Add(new ConditionalCollections { ConditionalList = ddt_proc });
                            if (_dbContext.Modeling.Queryable<ProcessInstanceRecord>().Where(conditonModels_proc).First() == null)
                            {
                                return;
                            }
                        }

                        conditonModels.Add(new ConditionalCollections { ConditionalList = ddt });
                        var dt = new Dictionary<string, object>();
                        var gxzd_array = JArray.Parse(callObject["fields"].ToString());
                        foreach (var gxzd in gxzd_array)
                        {
                            JObject gxzd_object = JObject.Parse(gxzd.ToString());
                            if (!string.IsNullOrEmpty(gxzd_object["field"].ToString()))
                            {
                                JArray gxzd_object_field_array = JArray.Parse(gxzd_object["field"].ToString());
                                if (gxzd_object_field_array.Count > 1 && !string.IsNullOrEmpty(gxzd_object_field_array[1].ToString()))
                                {
                                    dt.Add(gxzd_object_field_array[1].ToString(), this.GetProcessCallBackFieldFormat(gxzd_object, null, businessObject.Name));
                                }
                            }
                        }

                        if (dt.Count > 0)
                        {
                            SaveDic(dt, DateTime.Now, "ModifyDate");
                            SaveDic(dt, "sys", "ModifyUserId");
                            client.Updateable(dt).AS(businessObject.Name).Where(conditonModels).ExecuteCommand();
                        }
                    }
                }
            }
        }

        private JToken FieldFormat(string displayType, string data)
        {
            if (displayType == "keyvalue" || displayType == "object" || displayType == "file")
            {
                if (!string.IsNullOrEmpty(data))
                {
                    if (data.StartsWith("[") && data.EndsWith("]"))
                    {
                        return JArray.Parse(data);
                    }
                    else if (data.StartsWith("{") && data.EndsWith("}"))
                    {
                        return JObject.Parse(data);
                    }
                }
            }

            return data;
        }

        private object GetProcessCallBackFieldFormat(JObject setting, JToken dataSource, string bName)
        {
            object result = null;
            switch (setting["fieldType"].ToString())
            {
                case "gdz":
                    result = setting["fieldValue"];
                    break;
            }

            return result;
        }

        private void SaveSystemCol(Dictionary<string, object> dic, List<BusinessObjectColumn> columns, string type)
        {
            var sys = columns.FindAll(f => f.IsSystemColumn.Value);
            if (type == "insert")
            {
                if (sys.FindIndex(f => f.Name == "CreateDate") > -1)
                {
                    dic.Add("CreateDate", DateTime.Now);
                }

                if (sys.FindIndex(f => f.Name == "CreateUserId") > -1)
                {
                    dic.Add("CreateUserId", UserConstants.CurrentUser.Value.Account);
                }

                if (sys.FindIndex(f => f.Name == "IsDelete") > -1)
                {
                    dic.Add("IsDelete", 0);
                }
            }
            else if (type == "update")
            {
                if (sys.FindIndex(f => f.Name == "ModifyDate") > -1)
                {
                    dic.Add("ModifyDate", DateTime.Now);
                }

                if (sys.FindIndex(f => f.Name == "ModifyUserId") > -1)
                {
                    dic.Add("ModifyUserId", UserConstants.CurrentUser.Value.Account);
                }
            }
            else if (type == "delete")
            {
                if (sys.FindIndex(f => f.Name == "ModifyDate") > -1)
                {
                    dic.Add("ModifyDate", DateTime.Now);
                }

                if (sys.FindIndex(f => f.Name == "ModifyUserId") > -1)
                {
                    dic.Add("ModifyUserId", UserConstants.CurrentUser.Value.Account);
                }

                if (sys.FindIndex(f => f.Name == "IsDelete") > -1)
                {
                    dic.Add("IsDelete", 1);
                }
            }
        }

        private void SaveDic(Dictionary<string, object> dic, object data, string key)
        {
            if (dic.ContainsKey(key))
            {
                dic[key] = data;
            }
            else
            {
                dic.Add(key, data);
            }
        }

        private (bool isOk, object result) OutsideDataFormat(ObjectColumnDto column, object data)
        {
            if (column.DisplayType == "keyvalue")
            {
                if (data != null && data.ToString().StartsWith("[") && data.ToString().EndsWith("]"))
                {
                    var array = JArray.Parse(data.ToString());
                    var result = new List<string>();
                    foreach (var a in array)
                    {
                        result.Add($"{{\"label\":\"{a}\",\"value\":\"{a}\"}}");
                    }

                    return (true, $"[{string.Join(",", result)}]");
                }
                else
                {
                    return (true, $"{{\"label\":\"{data}\",\"value\":\"{data}\"}}");
                }
            }
            else if (column.DisplayType == "object")
            {
                if (data != null && data.ToString().StartsWith("[") && data.ToString().EndsWith("]"))
                {
                    var array = JArray.Parse(data.ToString());
                    var result = new List<string>();
                    foreach (var a in array)
                    {
                        var obj = JObject.Parse(a.ToString());
                        if (obj.ContainsKey("userId"))
                        {
                            result.Add($"{{\"label\":\"{obj["userName"]}\",\"value\":\"{obj["userId"]}\",\"account\":\"{obj["userLoginId"]}\",\"number\":\"{obj["userNumber"]}\",\"fullDivision\":\"{obj["fullDivision"]}\",\"email\":\"{obj["email"]}\"}}");
                        }
                        else if (obj.ContainsKey("organizationId"))
                        {
                            result.Add($"{{\"label\":\"{obj["organizationName"]}\",\"value\":\"{obj["organizationId"]}\",\"path\":\"{obj["organizationPath"]}\"}}");
                        }
                    }

                    if (result.Count > 0)
                    {
                        return (true, $"[{string.Join(",", result)}]");
                    }
                }
                else if (data != null && data.ToString().StartsWith("{") && data.ToString().EndsWith("}"))
                {
                    var obj = JObject.Parse(data.ToString());
                    if (obj.ContainsKey("userId"))
                    {
                        return (true, $"{{\"label\":\"{obj["userName"]}\",\"value\":\"{obj["userId"]}\",\"account\":\"{obj["userLoginId"]}\",\"number\":\"{obj["userNumber"]}\",\"fullDivision\":\"{obj["fullDivision"]}\",\"email\":\"{obj["email"]}\"}}");
                    }
                    else if (obj.ContainsKey("organizationId"))
                    {
                        return (true, $"{{\"label\":\"{obj["organizationName"]}\",\"value\":\"{obj["organizationId"]}\",\"path\":\"{obj["organizationPath"]}\"}}");
                    }
                }

                return (false, string.Empty);
            }
            else if (column.DisplayType == "number")
            {
                return (true, data == null || string.IsNullOrEmpty(data.ToString()) ? null : data);
            }
            else if (column.DisplayType == "bool")
            {
                var isBol = false;
                var isNum = 0;
                if (data != null)
                {
                    if (int.TryParse(data.ToString(), out isNum))
                    {
                        return (true, isNum == 0 ? false : true);
                    }
                    else if (bool.TryParse(data.ToString(), out isBol))
                    {
                        return (true, isBol);
                    }
                }

                return (true, isBol);
            }

            return (true, data);
        }

        private object GetDataByType(List<BusinessObjectColumn> columns, string columnName, object columnValue)
        {
            var column = columns.Find(f => f.Name == columnName);
            if (column != null)
            {
                if (column.DisplayType == "number" && string.IsNullOrEmpty(columnValue.ToString()))
                {
                    return null;
                }
                else if (column.DisplayType == "bool" && !string.IsNullOrEmpty(columnValue.ToString()))
                {
                    var isBol = false;
                    var isNum = 0;
                    if (int.TryParse(columnValue.ToString(), out isNum))
                    {
                        return isNum == 0 ? false : true;
                    }
                    else if (bool.TryParse(columnValue.ToString(), out isBol))
                    {
                        return isBol;
                    }

                    return isBol;
                }
            }

            return columnValue;
        }

        private (List<Guid>, List<OutsideBusinessObjectColumnStructureDto>) FilterColumns(Guid compositeObjectId, Guid? parentId)
        {
            var result = new List<OutsideBusinessObjectColumnStructureDto>();
            var compositeObjectIds = new List<Guid>();
            var details = _dbContext.Modeling.Queryable<CompositeObjectRelation, BusinessObject>((a, b) => a.BusinessObjectId == b.Id)
               .Where((a, b) => a.CompositeObjectId == compositeObjectId && a.ObjectType == "businessObject" && a.JoinRelation == 1)
               .WhereIF(!parentId.HasValue, (a, b) => SqlFunc.IsNullOrEmpty(a.ParentId.Value))
               .WhereIF(parentId.HasValue, (a, b) => a.ParentId == parentId)
               .Select((a, b) => a).ToList();
            details.ForEach(f =>
            {
                compositeObjectIds.Add(f.Id);
                result.AddRange(_dbContext.Modeling.Queryable<BusinessObjectColumn, BusinessObject>((a, b) => a.BusinessObjectId == b.Id)
                .Where(a => a.BusinessObjectId == f.BusinessObjectId && a.IsEnable.Value && !a.IsSystemColumn.Value)
                .OrderBy(a => a.IsPrimaryKey, OrderByType.Desc)
                .OrderBy(a => a.IsSystemColumn)
                .OrderBy(a => a.Order)
                .Select((a, b) => new OutsideBusinessObjectColumnStructureDto
                {
                    Id = b.Id.ToString() + ";" + a.Id.ToString(),
                    Name = b.Name + a.Name,
                    Description = a.Description,
                    DisplayType = a.DisplayType,
                    Order = a.Order,
                    Length = a.Length,
                    Decimal = a.Decimal
                }).ToList());
                var ls = FilterColumns(compositeObjectId, f.Id);
                compositeObjectIds.AddRange(ls.Item1);
                result.AddRange(ls.Item2);
            });
            return (compositeObjectIds, result);
        }

        private void FilterColumns(List<string> selectList, Dictionary<string, string> businessDate, List<Guid> ls, ISugarQueryable<ExpandoObject> query, List<ObjectStructureDto> fields, ObjectStructureDto parent, Guid? parentId)
        {
            List<ObjectStructureDto> li = new List<ObjectStructureDto>();
            if (parentId.HasValue)
            {
                li = fields.Where(w => w.ObjectType == "businessObject" && !w.IsMain && w.ParentId.HasValue && w.ParentId.Value == parentId && w.JoinRelation == 1).ToList();
            }
            else
            {
                li = fields.Where(w => w.ObjectType == "businessObject" && !w.IsMain && !w.ParentId.HasValue && w.JoinRelation == 1).ToList();
            }

            li.ForEach(f =>
            {
                var pc = parent.Columns.Where(w => w.Id == f.MainDataTableColumnId).FirstOrDefault();
                var dc = f.Columns.Where(w => w.Id == f.DetailDataTableColumnId).FirstOrDefault();
                if (pc != null && dc != null)
                {
                    ls.Add(f.CompositeObjectRelationId.Value);
                    query.AddJoinInfo(f.Name, "Cust" + f.Name, $"Cust{parent.Name}.{pc.Name}=Cust{f.Name}.{dc.Name}", f.JoinType == 1 ? JoinType.Inner : JoinType.Left);
                    f.Columns.ForEach(fc =>
                    {
                        selectList.Add($"Cust{f.Name}.{fc.Name} as {f.Name}{fc.Name}");
                        businessDate.Add($"{f.Name}{fc.Name}", fc.DisplayType);
                    });
                    FilterColumns(selectList, businessDate, ls, query, fields, f, f.CompositeObjectRelationId);
                }
            });
        }

        private void RecursionSaveOutsideBusinessObjectData(List<ObjectStructureDto> tables, OutsideBusinessObjectDataDto dto, List<string> onlyView, List<BusinessObjectFieldDto> fields, List<Guid> zbid, SqlSugarClient client, ObjectStructureDto parent, Guid? parentId)
        {
            List<ObjectStructureDto> li = new List<ObjectStructureDto>();
            if (parentId.HasValue)
            {
                li = tables.Where(w => w.IsCustom && w.ObjectType == "businessObject" && !w.IsMain && w.ParentId.HasValue && w.ParentId.Value == parentId && w.JoinRelation == 1).ToList();
            }
            else
            {
                li = tables.Where(w => w.IsCustom && w.ObjectType == "businessObject" && !w.IsMain && !w.ParentId.HasValue && w.JoinRelation == 1).ToList();
            }

            li.ForEach(d =>
            {
                zbid.Add(d.CompositeObjectRelationId.Value);
                if (!onlyView.Contains(d.Name))
                {
                    var dic = new Dictionary<string, object>();
                    d.Columns.Where(w => !w.IsSystemColumn && !onlyView.Contains($"{d.Name}_{w.Name}")).ToList().ForEach(wf =>
                    {
                        var column = fields.Where(s => s.Code == $"{d.Name}{wf.Name}" && s.OutsideBusinessObjectInfo.IndexOf($"{d.Id};{wf.Id}") > -1).FirstOrDefault();
                        if (column != null && dto.FormData.ContainsKey(column.Code))
                        {
                            var da = OutsideDataFormat(wf, dto.FormData[column.Code]);
                            if (da.isOk)
                            {
                                dic.Add(wf.Name, da.result);
                            }
                        }
                    });

                    if (dic.Count > 0)
                    {
                        var zj = dto.FormData.ContainsKey($"{d.Name}ID") ? dto.FormData[$"{d.Name}ID"].ToString() : string.Empty;
                        var pcC = parent.Columns.Where(w => w.Id == d.MainDataTableColumnId).FirstOrDefault();
                        var dcC = d.Columns.Where(w => w.Id == d.DetailDataTableColumnId).FirstOrDefault();
                        SaveDic(dic, 0, "IsDelete");
                        SaveDic(dic, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "ModifyDate");
                        SaveDic(dic, !string.IsNullOrEmpty(dto.StartUserId) ? dto.StartUserId : string.Empty, "ModifyUserId");
                        SaveDic(dic, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "CreateDate");
                        SaveDic(dic, !string.IsNullOrEmpty(dto.OwnerUserId) ? dto.OwnerUserId : string.Empty, "CreateUserId");
                        SaveDic(dic, dto.OrganizationPath, "CreateUserOrgPathId");
                        SaveDic(dic, dto.FormData[$"{parent.Name}{pcC.Name}"], dcC.Name);
                        if (!string.IsNullOrEmpty(zj))
                        {
                            SaveDic(dic, zj, "ID");
                        }
                        else
                        {
                            SaveDic(dic, Guid.NewGuid(), "ID");
                        }

                        var data = new DataTable();
                        foreach (var key in dic.Keys)
                        {
                            data.Columns.Add(key);
                        }

                        data.TableName = d.Name;

                        var dr = data.NewRow();
                        foreach (var key in dic.Keys)
                        {
                            dr[key] = dic[key];
                        }

                        data.Rows.Add(dr);
                        var x = client.Storageable(data).WhereColumns(new string[] { dcC.Name }).ToStorage();
                        x.AsInsertable.IgnoreColumns(new string[] { "ModifyDate", "ModifyUserId" }).ExecuteCommand();
                        var upR = x.AsUpdateable.IgnoreColumns(new string[] { "CreateDate", "CreateDate", "CreateUserOrgPathId", "IsDelete", "ID" }).ExecuteCommand();
                        if (upR > 0)
                        {
                            var conModels = new List<IConditionalModel>();
                            var cModel = new ConditionalModel()
                            {
                                FieldName = dcC.Name,
                                ConditionalType = ConditionalType.Equal,
                                FieldValue = dic[dcC.Name].ToString()
                            };
                            conModels.Add(cModel);
                            var ex = client.Queryable<dynamic>().AS(d.Name, "Cust" + d.Name).Where(conModels).ToList();
                            if (ex != null && ex.Count > 0)
                            {
                                dic["ID"] = ex[0].ID;
                            }
                        }

                        SaveDic(dto.FormData, dic["ID"], $"{d.Name}ID");
                        SaveDic(dto.FormData, dic[dcC.Name], $"{parent.Name}{pcC.Name}");
                    }
                }

                RecursionSaveOutsideBusinessObjectData(tables, dto, onlyView, fields, zbid, client, d, d.CompositeObjectRelationId);
            });
        }
    }
}
