using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Medusa.Service.Modeling.Application.CompositeObjectManage.Dtos
{
    
    
    
    public class CompositeObjectRelationDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        [Required]
        public Guid CompositeObjectId { get; set; }

        
        
        
        [Required]
        public Guid ParentBusinessObjectColumnId { get; set; }

        
        
        
        public string ParentBusinessObjectColumnName { get; set; }

        
        
        
        public string ParentBusinessObjectColumnDescription { get; set; }

        
        
        
        [Required]
        public Guid BusinessObjectId { get; set; }

        
        
        
        public string BusinessObjectName { get; set; }

        
        
        
        public string BusinessObjectDescription { get; set; }

        
        
        
        [Required]
        public Guid BusinessObjectColumnId { get; set; }

        
        
        
        public string BusinessObjectColumnName { get; set; }

        
        
        
        public string BusinessObjectColumnDescription { get; set; }

        
        
        
        public int JoinType { get; set; }

        
        
        
        public int JoinRelation { get; set; }

        
        
        
        public Guid? ParentId { get; set; }

        
        
        
        public string ObjectType { get; set; }

        
        
        
        public object ExtraCondition { get; set; }
    }
}
