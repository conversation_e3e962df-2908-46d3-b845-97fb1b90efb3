using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application.DictionaryMgr.Dtos;
using Medusa.Service.Modeling.Core.Entity;

namespace Medusa.Service.Modeling.Application.DictionaryMgr
{
    
    
    
    public interface IDictionaryMgrService : IServiceBase
    {
        
        
        
        
        
        DictionaryGroupDto GetDictionaryGroup(bool isHasStructure);

        
        
        
        
        
        
        PageResult<Dictionary> GetDictionaryByGroupId(Guid groupId, DictionaryQueryDto dto);

        
        
        
        
        void Save(Dictionary dto);

        
        
        
        
        void Delete(Guid id);
    }
}
