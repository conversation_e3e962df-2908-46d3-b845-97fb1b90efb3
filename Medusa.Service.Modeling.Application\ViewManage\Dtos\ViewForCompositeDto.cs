using System;
using System.Collections.Generic;
using System.Text;

namespace Medusa.Service.Modeling.Application.ViewManage.Dtos
{
    
    
    
    public class ViewForCompositeDto
    {
        
        
        
        public Guid Id { get; set; }

        
        
        
        public Guid DataBaseId { get; set; }

        
        
        
        public string Name { get; set; }

        
        
        
        public string Description { get; set; }

        
        
        
        public string DataBaseName { get; set; }

        
        
        
        public string DataBaseDescription { get; set; }

        
        
        
        public DateTime? CreateDate { get; set; }

        
        
        
        public string DataBaseType { get; set; }

        
        
        
        public bool IsCustom { get; set; }

        
        
        
        public bool IsOutSideBusinessBase { get; set; }

        
        
        
        public int? State { get; set; }

        
        
        
        public List<ViewForCompositeColumnDto> Columns { get; set; }

        
        
        
        public bool? IsTree { get; set; }

        
        
        
        public bool? IsLogicalDelete { get; set; }

        
        
        
        public string ApplicationId { get; set; }

        
        
        
        public string ApplicationName { get; set; }
    }
}
