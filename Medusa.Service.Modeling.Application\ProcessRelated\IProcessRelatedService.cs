using Medusa.Service.Modeling.Application.Authority.Dtos;
using Medusa.Service.Modeling.Application.ProcessRelated.Dtos;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Threading.Tasks;

namespace Medusa.Service.Modeling.Application.ProcessRelated
{
    
    
    
    public interface IProcessRelatedService : IServiceBase
    {
        List<BusinessObjectItemDto> GetOutsideBusinessObjects(string sysCode, Guid dataBaseId);

        OutsideBusinessObjectStructureDto GetOutsideBusinessObjectStructure(Guid id, int type);

        Task SaveOutsideBusinessObjectData(OutsideBusinessObjectDataDto dto);

        Dictionary<string, object> GetBusinessObjectData(OutsideBusinessObjectDataSearchDto dto);

        Task<string> Start(PorcessDto dto);

        Task Recall(PorcessDto dto);

        Task Cancel(PorcessDto dto);

        void CallReceiveRecord(CallDto dto, string type);

        Task<List<ExpandoObject>> OutsideVerification(OutsideVerificationDto dto);
    }
}
