using System;
using System.Collections.Generic;
using System.Linq;
using Medusa.Service.Modeling.Application.Authority.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Utils.Extensions;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.Authority
{
    
    
    
    public class AuthorityService : ServiceBase, IAuthorityService
    {
        #region 
        readonly MyDbContext _dbContext;

        
        
        
        
        
        public AuthorityService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
        }

        #endregion

        
        
        
        
        
        public PageResult<BusinessSysDto> GetBusinessSystems(BusinessSysQueryDto dto)
        {
            var count = 0;
            var itemsQuery = _dbContext.Modeling.Queryable<BusinessSystem>()
                   .Where(x => !x.IsDelete)
                   .WhereIF(!string.IsNullOrEmpty(dto.Name), x => x.Name.Contains(dto.Name))
                   .WhereIF(!string.IsNullOrEmpty(dto.Code), x => x.Code.Contains(dto.Code))
                   .OrderBy(x => x.CreateDate, SqlSugar.OrderByType.Desc)
                   .Select(x => new BusinessSysDto
                   {
                       Id = x.Id,
                       Name = x.Name,
                       Code = x.Code,
                       Description = x.Description,
                       CreateDate = x.CreateDate
                   });
            var items = dto.IsAll ? itemsQuery.ToList() : itemsQuery.ToPageList(dto.PageIndex, dto.PageSize, ref count);
            return new PageResult<BusinessSysDto>
            {
                Items = items,
                Total = count
            };
        }

        
        
        
        
        public void SaveBusinessSystems(BusinessSysDto dto)
        {
            if (dto.Id == Guid.Empty)
            {
                if (_dbContext.Modeling.Queryable<BusinessSystem>().Any(t => t.Code == dto.Code && !t.IsDelete))
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.businessSystem.exist"));
                }

                var item = dto.MapTo<BusinessSystem>();
                item.Id = Guid.NewGuid();
                item.IsDelete = false;
                DateTime now = DateTime.Now;
                item.CreateDate = now;
                _dbContext.Modeling.Insertable(item).ExecuteCommand();
            }
            else
            {
                if (_dbContext.Modeling.Queryable<BusinessSystem>().Any(t => t.Code == dto.Code && !t.IsDelete && t.Id != dto.Id))
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.businessSystem.exist"));
                }

                var item = _dbContext.Modeling.Queryable<BusinessSystem>().InSingle(dto.Id);
                if (item == null)
                {
                    throw new StatusNotFoundException(I18nManager.GetString("modeling.businessSystem.notfound"));
                }

                item.Code = dto.Code;
                item.Description = dto.Description;
                item.Name = dto.Name;
                _dbContext.Modeling.Updateable(item).ExecuteCommand();
            }
        }

        
        
        
        
        public void DeleteBusinessSystems(Guid id)
        {
            var item = _dbContext.Modeling.Queryable<BusinessSystem>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessSystem.notfound"));
            }

            item.IsDelete = true;
            _dbContext.Modeling.Updateable(item).ExecuteCommand();
        }

        
        
        
        
        
        public List<BusinessObjectItemDto> GetBusinessObjectAuthority(Guid id)
        {
            var authority1 = _dbContext.Modeling.Queryable<BusinessSystem, BusinessSystemsAuthority, BusinessObject>((a, b, c) => a.Id == b.BusinessSystemId && b.BusinessObjectId == c.Id)
                .Where((a, b, c) => !a.IsDelete && b.BusinessObjectType == 1 && a.Id == id)
                .Select((a, b, c) => new BusinessObjectItemDto
                {
                    BusinessObjectId = b.BusinessObjectId,
                    BusinessObjectType = b.BusinessObjectType.Value,
                    BusinessObjectDisplayName = c.Description
                });
            var authority2 = _dbContext.Modeling.Queryable<BusinessSystem, BusinessSystemsAuthority, CompositeObject>((a, b, c) => a.Id == b.BusinessSystemId && b.BusinessObjectId == c.Id)
                .Where((a, b, c) => !a.IsDelete && b.BusinessObjectType == 2 && a.Id == id)
                  .Select((a, b, c) => new BusinessObjectItemDto
                  {
                      BusinessObjectId = b.BusinessObjectId,
                      BusinessObjectType = b.BusinessObjectType.Value,
                      BusinessObjectDisplayName = c.Name
                  });
            var authority = _dbContext.Modeling.Union(authority1, authority2).ToList();
            return authority;
        }

        
        
        
        
        
        public void SaveBusinessObjectAuthority(Guid id, List<BusinessObjectItemDto> dto)
        {
            var item = _dbContext.Modeling.Queryable<BusinessSystem>().InSingle(id);
            if (item == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.businessSystem.notfound"));
            }

            _dbContext.Modeling.Deleteable<BusinessSystemsAuthority>().Where(w => w.BusinessSystemId == id).ExecuteCommand();
            if (dto != null)
            {
                var list = new List<BusinessSystemsAuthority>();
                dto.ForEach(f =>
                {
                    var l = new BusinessSystemsAuthority();
                    l.BusinessSystemId = id;
                    l.CreateDate = DateTime.Now;
                    l.BusinessObjectId = f.BusinessObjectId;
                    l.BusinessObjectType = f.BusinessObjectType;
                    l.Id = Guid.NewGuid();
                    list.Add(l);
                });

                if (list.Count > 0)
                {
                    _dbContext.Modeling.Insertable(list).ExecuteCommand();
                }
            }
        }

        
        
        
        
        
        public List<BusinessObjectItemDto> GetBusinessObjects(BusinessObjectQueryDto dto)
        {
            var result = new List<BusinessObjectItemDto>();
            var businessObjects_l = _dbContext.Modeling.Queryable<BusinessObject>()
                   .WhereIF(!string.IsNullOrEmpty(dto.Name), bo => bo.Name.Contains(dto.Name) || bo.Description.Contains(dto.Name))
                         .WhereIF(!string.IsNullOrEmpty(dto.ApplicationId), bo => bo.ApplicationId.Contains(dto.ApplicationId))
                      .Select(a => new BusinessObjectItemDto
                      {
                          BusinessObjectType = 1,
                          BusinessObjectId = a.Id,
                          BusinessObjectDisplayName = SqlFunc.IF(SqlFunc.IsNullOrEmpty(a.Description)).Return(a.Name).End(a.Description)
                      }).ToList();
            result.AddRange(businessObjects_l);
            var compositeObjects_l = _dbContext.Modeling.Queryable<CompositeObject>()
                     .WhereIF(!string.IsNullOrEmpty(dto.Name), bo => bo.Name.Contains(dto.Name))
                         .WhereIF(!string.IsNullOrEmpty(dto.ApplicationId), bo => bo.ApplicationId.Contains(dto.ApplicationId))
                      .Select(a => new BusinessObjectItemDto
                      {
                          BusinessObjectType = 2,
                          BusinessObjectId = a.Id,
                          BusinessObjectDisplayName = a.Name
                      }).ToList();
            result.AddRange(compositeObjects_l);
            return result;
        }
    }
}
