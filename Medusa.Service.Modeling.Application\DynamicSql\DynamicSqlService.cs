using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using Medusa.Service.Modeling.Application.DynamicSql.Dtos;
using Medusa.Service.Modeling.Application.OperationLog;
using Medusa.Service.Modeling.Application.ViewManage.Dtos;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entity;
using Medusa.Service.Modeling.Core.ORM;
using Microsoft.AspNetCore.JsonPatch.Operations;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.DependencyInjection;
using MT.Enterprise.Core.GlobalExceptions;
using MT.Enterprise.Core.I18n;
using MT.Enterprise.Core.Middlewares.UserState;
using MT.Enterprise.Utils.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.DynamicSql
{
    
    
    
    public class DynamicSqlService : ServiceBase, IDynamicSqlService
    {
        
        
        
        readonly MyDbContext _dbContext;
        private readonly IOperationLogService _operationLogService;

        
        
        
        
        public DynamicSqlService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            _operationLogService = serviceProvider.GetService<IOperationLogService>();
        }

        
        
        
        
        
        
        public JObject Query(JObject jObject, Guid databaseId)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(databaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var resultObject = new JObject
            {
                { "code", "200" },
                { "msg", "success" }
            };
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            foreach (var jItem in jObject)
            {
                string key = jItem.Key.Trim();
                if (key == "[]")
                {
                    this.QueryList(resultObject, jItem, client);
                }
            }

            return resultObject;
        }

        
        
        
        
        
        public void Update(JObject jObject, Guid databaseId)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(databaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var menu = jObject["@menu"] == null ? string.Empty : jObject["@menu"].ToString();
            var operation = jObject["@action"] == null ? string.Empty : jObject["@action"].ToString();
            var mainTable = jObject["@mainTable"] == null ? string.Empty : jObject["@mainTable"].ToString();
            var mainTableKey = jObject["@mainTableKey"] == null ? string.Empty : jObject["@mainTableKey"].ToString();
            jObject.Remove("@menu");
            jObject.Remove("@action");
            jObject.Remove("@mainTable");
            jObject.Remove("@mainTableKey");
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            foreach (var item in jObject)
            {
                
                string tableName = item.Key.Trim();
                var value = (JObject)item.Value;
                if (value.ContainsKey("add") && value["add"] != null)
                {
                    AddData(tableName, value["add"].ToString(), client, menu, operation, mainTable, mainTableKey);
                }

                if (value.ContainsKey("edit") && value["edit"] != null)
                {
                    EditData(tableName, value["edit"].ToString(), client, menu, operation, mainTable, mainTableKey);
                }

                if (value.ContainsKey("delete") && value["delete"] != null)
                {
                    DeleteData(tableName, value["delete"].ToString(), client, menu, operation, mainTable, mainTableKey);
                }
            }
        }

        
        
        
        
        
        
        public IWorkbook Export(JObject jObject, Guid databaseId)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(databaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var resultObject = new JObject
            {
                { "code", "200" },
                { "msg", "success" }
            };

            var menu = jObject["@menu"] == null ? string.Empty : jObject["@menu"].ToString();
            var operation = jObject["@action"] == null ? string.Empty : jObject["@action"].ToString();
            jObject.Remove("@menu");
            jObject.Remove("@action");
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            JArray columns = null;
            string sheetName = string.Empty;
            bool multiTable = true;
            foreach (var jItem in jObject)
            {
                string key = jItem.Key.Trim();
                if (key == "[]")
                {
                    this.QueryList(resultObject, jItem, client);
                }
                else if (key == "columns")
                {
                    columns = JArray.Parse(jItem.Value.ToString());
                }
                else if (key == "name")
                {
                    sheetName = jItem.Value.ToString();
                }
                else if (key == "multiTable")
                {
                    multiTable = Convert.ToBoolean(jItem.Value.ToString());
                }
            }

            var dic = new Dictionary<string, List<string>>();

            
            if (columns != null)
            {
                foreach (var ca in columns)
                {
                    var cj = JObject.Parse(ca.ToString());
                    var tableName = multiTable ? cj["table"].ToString() : sheetName;
                    if (dic.ContainsKey(tableName))
                    {
                        dic[tableName].Add(ca.ToString());
                    }
                    else
                    {
                        dic.Add(tableName, new List<string> { ca.ToString() });
                    }
                }
            }

            IWorkbook workbook = new XSSFWorkbook();
            foreach (var tb in dic.Keys)
            {
                ISheet sheet = workbook.CreateSheet(!string.IsNullOrEmpty(tb) ? tb : Guid.NewGuid().ToString());
                IRow firstTitle = sheet.CreateRow(0);
                (ICellStyle borderStyle, ICellStyle titleStyle) = GetExcelStyle(workbook);
                var numberCell = firstTitle.CreateCell(0);
                numberCell.SetCellValue("序号");
                numberCell.CellStyle = titleStyle;

                if (dic[tb].Count > 0)
                {
                    int i = 1;
                    foreach (var ca in dic[tb])
                    {
                        var cj = JObject.Parse(ca.ToString());
                        var cell = firstTitle.CreateCell(i);
                        cell.SetCellValue(cj["otherName"] != null && !string.IsNullOrEmpty(cj["otherName"].ToString()) ? cj["otherName"].ToString() : cj["label"].ToString());
                        cell.CellStyle = titleStyle;
                        i++;
                    }

                    var items = resultObject["[]"] == null ? string.Empty : resultObject["[]"].ToString();
                    if (!string.IsNullOrEmpty(items))
                    {
                        var itemObject = JObject.Parse(items);
                        foreach (var io in itemObject)
                        {
                            var itemArray = JArray.Parse(io.Value.ToString());
                            var rowDatas = new List<string>();
                            int j = 1;
                            foreach (var item in itemArray)
                            {
                                var item_o = JObject.Parse(item.ToString());
                                var row = sheet.CreateRow(j);
                                var number = row.CreateCell(0);
                                number.CellStyle = borderStyle;
                                number.SetCellValue(j);
                                int k = 1;
                                var rowData = string.Empty;
                                foreach (var item_ca in dic[tb])
                                {
                                    var item_cj = JObject.Parse(item_ca.ToString());
                                    var cell = row.CreateCell(k);
                                    var field = item_cj["table"].ToString() + "_" + item_cj["value"].ToString();
                                    var fieldV = item_o[field] == null ? string.Empty : item_o[field].ToString();
                                    if (!string.IsNullOrEmpty(fieldV))
                                    {
                                        switch (item_cj["type"].ToString())
                                        {
                                            case "object":
                                                try
                                                {
                                                    var fieldA = JArray.Parse(fieldV);
                                                    var res = new List<string>();
                                                    foreach (var f in fieldA)
                                                    {
                                                        var fo = JObject.Parse(f.ToString());
                                                        res.Add(fo["label"].ToString());
                                                    }

                                                    fieldV = string.Join(";", res);
                                                }
                                                catch
                                                {
                                                }

                                                break;
                                            case "keyvalue":
                                                try
                                                {
                                                    if (fieldV.StartsWith("[") && fieldV.EndsWith("]"))
                                                    {
                                                        var fieldA_1 = JArray.Parse(fieldV);
                                                        var res_1 = new List<string>();
                                                        foreach (var f in fieldA_1)
                                                        {
                                                            var fo = JObject.Parse(f.ToString());
                                                            res_1.Add(fo["label"].ToString());
                                                        }

                                                        fieldV = string.Join(";", res_1);
                                                    }
                                                    else
                                                    {
                                                        var fieldO = JObject.Parse(fieldV);
                                                        fieldV = fieldO["label"].ToString();
                                                    }
                                                }
                                                catch
                                                {
                                                }

                                                break;
                                        }
                                    }

                                    cell.SetCellValue(fieldV);
                                    rowData += fieldV;
                                    cell.CellStyle = borderStyle;
                                    sheet.SetColumnWidth(k, 20 * 256);
                                    k++;
                                }

                                if (rowDatas.Contains(rowData))
                                {
                                    sheet.RemoveRow(row);
                                }
                                else
                                {
                                    rowDatas.Add(rowData);
                                    j++;
                                }
                            }
                        }
                    }
                }
            }

            _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
            {
                Menu = menu,
                Action = operation,
                OperationType = "导出数据"
            });
            return workbook;
        }

        
        
        
        
        
        
        public JObject InitView(string dbType, ViewDto dto)
        {
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(dto.DatabaseId);
            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var resultObject = new JObject
            {
                { "code", "200" },
                { "msg", "success" }
            };
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            foreach (var jItem in dto.ViewJson)
            {
                string key = jItem.Key.Trim();
                if (key == "[]")
                {
                    var jsonObject = JObject.Parse(jItem.Value.ToString());
                    var where = jsonObject["@where"] == null ? string.Empty : jsonObject["@where"].ToString();
                    var order = jsonObject["@order"] == null ? string.Empty : jsonObject["@order"].ToString();
                    var join = jsonObject["@join"] == null ? string.Empty : jsonObject["@join"].ToString();
                    var whereCustom = jsonObject["@whereCustom"] == null ? string.Empty : jsonObject["@whereCustom"].ToString();
                    jsonObject.Remove("@pagination");
                    jsonObject.Remove("@where");
                    jsonObject.Remove("@order");
                    jsonObject.Remove("@join");
                    jsonObject.Remove("@whereCustom");
                    var selectList = new List<string>();
                    var mainTable = string.Empty;
                    ISugarQueryable<ExpandoObject> query = null;
                    var pkObject = dto.Columns.FirstOrDefault(x => x.IsPrimaryKey == 1);
                    var relations = new List<Guid>();
                    relations.AddRange(dto.Relations.Select(x => x.ParentBusinessObjectId).ToList());
                    relations.AddRange(dto.Relations.Where(x => x.BusinessObjectId != null).Select(x => x.BusinessObjectId).ToList());
                    relations = relations.Distinct().ToList();
                    List<string> itemList = new List<string>();
                    foreach (var item in relations)
                    {
                        var table = client.Queryable<BusinessObject>().InSingle(item);
                        if (table != null)
                        {
                            itemList.Add($"Cust{table.Name}.ID");
                        }
                    }

                    string pkSql = string.Empty;
                    switch (dbType.ToLower())
                    {
                        case "mysql":
                            pkSql = $"CONCAT({string.Join(",'-',", itemList)})";
                            break;
                        case "sqlserver":
                            pkSql = $"{string.Join("+'-'+", itemList)}";
                            break;
                        case "oracle":
                            pkSql = $"({string.Join("||'-'||", itemList)})";
                            break;
                    }

                    foreach (var jsonTable in jsonObject)
                    {
                        var tableObject = JObject.Parse(jsonTable.Value.ToString());
                        mainTable = jsonTable.Key.Trim();
                        query = client.Queryable(mainTable, "Cust" + mainTable);
                        var select = tableObject["@select"] == null ? string.Empty : tableObject["@select"].ToString();
                        if (!string.IsNullOrEmpty(select))
                        {
                            var selectObject = JArray.Parse(select);
                            foreach (var selectColumn in selectObject)
                            {
                                if (selectColumn.ToString().Contains("*"))
                                {
                                    if (selectColumn.ToString().TrimEnd('*') == pkObject.BusinessObjectColumnName)
                                    {
                                        selectList.Add($"{pkSql} as Fun{selectColumn.ToString().TrimEnd('*')}");
                                    }
                                    else
                                    {
                                        selectList.Add($"'' as Fun{selectColumn.ToString().TrimEnd('*')}");
                                    }
                                }
                                else
                                {
                                    selectList.Add($"Cust{mainTable}.{selectColumn} as {mainTable}{selectColumn}");
                                }
                            }
                        }

                        break;
                    }

                    this.SetJoin(join, query, selectList);

                    List<string> selectOrderList = new List<string>();
                    dto.Columns.OrderBy(x => x.OrderNumber).ToList().ForEach(x =>
                    {
                        string colName = x.ColumnType.ToLower() == "objectcolumn" ? $"{x.BusinessObjectName}{x.BusinessObjectColumnName}" : $"Fun{x.BusinessObjectColumnName}";
                        string colResult = selectList.FirstOrDefault(x => x.Contains($"as {colName}"));
                        if (colResult != null)
                        {
                            selectOrderList.Add(colResult);
                        }
                    });

                    query.Select(string.Join(",", selectOrderList));

                    this.SetWhere(where, query);

                    this.SetWhereCustom(whereCustom, query);

                    JArray jOrder = JArray.Parse(order);
                    if (jOrder.Count > 0)
                    {
                        this.SetOrder(order, query);
                    }

                    var sql = query.ToSql().Key;

                    StringBuilder sbFilter = new StringBuilder();
                    if (dto.Filters != null && dto.Filters.Count > 0)
                    {
                        sbFilter.Append(" WHERE ");
                        if (string.IsNullOrEmpty(dto.GroupFilter))
                        {
                            dto.GroupFilter = string.Join(" AND ", dto.Filters.Select(x => x.FilterName).ToArray());
                        }

                        if (string.IsNullOrEmpty(dto.GroupFilter))
                        {
                            for (var i = 0; i < dto.Filters.Count; i++)
                            {
                                var item = dto.Filters[i];
                                var table = client.Queryable<BusinessObject>().InSingle(item.BusinessObjectId);
                                var column = client.Queryable<BusinessObjectColumn>().InSingle(item.BusinessObjectColumnId);
                                string suffix = string.Empty;
                                if (i != dto.Filters.Count - 1)
                                {
                                    suffix = " AND ";
                                }

                                if (item.Judge.ToLower() == "contains")
                                {
                                    sbFilter.AppendFormat("Cust{0}.{1} LIKE '%{2}%'{3}", table.Name, column.Name, item.FilterValue, suffix);
                                }
                                else
                                {
                                    sbFilter.AppendFormat("Cust{0}.{1}{2}'{3}'{4}", table.Name, column.Name, item.Judge, item.FilterValue, suffix);
                                }
                            }
                        }
                        else
                        {
                            string[] gfList = dto.GroupFilter.Split(' ');
                            for (var i = 0; i < gfList.Length; i++)
                            {
                                var filterName = gfList[i];
                                var dbItem = dto.Filters.FirstOrDefault(x => x.FilterName == filterName);
                                if (dbItem != null)
                                {
                                    var table = client.Queryable<BusinessObject>().InSingle(dbItem.BusinessObjectId);
                                    var column = client.Queryable<BusinessObjectColumn>().InSingle(dbItem.BusinessObjectColumnId);
                                    string filterValue = dbItem.FilterValue;
                                    if (dbItem.FilterSource.ToLower() == "dictionary")
                                    {
                                        var dics = JArray.Parse(dbItem.FilterValue);
                                        if (dics.Count == 3)
                                        {
                                            filterValue = $"(SELECT {dics[2]} FROM Dictionary WHERE TypeCode='{dics[0]}' and Code='{dics[1]}')";
                                        }
                                    }

                                    if (dbItem.Judge.ToLower() == "contains")
                                    {
                                        sbFilter.AppendFormat("Cust{0}.{1} LIKE '%{2}%' ", table.Name, column.Name, filterValue);
                                    }
                                    else
                                    {
                                        if (dbItem.FilterSource.ToLower() == "dictionary")
                                        {
                                            sbFilter.AppendFormat("Cust{0}.{1}{2}{3} ", table.Name, column.Name, dbItem.Judge, filterValue);
                                        }
                                        else
                                        {
                                            sbFilter.AppendFormat("Cust{0}.{1}{2}'{3}' ", table.Name, column.Name, dbItem.Judge, filterValue);
                                        }
                                    }
                                }
                                else
                                {
                                    sbFilter.Append($"{filterName} ");
                                }
                            }
                        }
                    }

                    string[] sqlList = sql.Split("ORDER BY");
                    if (sqlList.Length > 1)
                    {
                        sql = $"CREATE VIEW {dto.Name} AS {sqlList[0]} {sbFilter.ToString()} ORDER BY {sqlList[1]};";
                    }
                    else
                    {
                        sql = $"CREATE VIEW {dto.Name} AS {sqlList[0]} {sbFilter.ToString()};";
                    }

                    try
                    {
                        client.Ado.ExecuteCommand($"DROP VIEW {dto.Name}");
                    }
                    catch
                    {
                    }

                    client.Ado.ExecuteCommand(sql);
                }
            }

            return resultObject;
        }

        
        
        
        
        
        public ViewFunctionDto GetFunctionColumnResult(ViewFunctionDto paras)
        {
            View view = _dbContext.Modeling.Queryable<View>().Where(x => x.Name == paras.ViewName).ToList().FirstOrDefault();
            List<ViewColumns> columns = _dbContext.Modeling.Queryable<ViewColumns>().Where(x => paras.FunctionColumn.Select(z => z.Name).Contains(x.BusinessObjectColumnAlias)).ToList();
            var dataBase = _dbContext.Modeling.Queryable<DataBase>().InSingle(view.DatabaseId);
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            foreach (var item in paras.FunctionColumn)
            {
                var dbCol = columns.FirstOrDefault(x => x.BusinessObjectColumnAlias == item.Name);
                if (dbCol != null)
                {
                    if (dbCol.FunctionId.ToLower() == "rowtocolumn")
                    {
                        paras.Data = RowToColumn(dbCol, paras.Data, client);
                    }
                }
            }

            return paras;
        }

        
        
        
        
        
        
        public JArray SetFunctionSelect(string functionSelect, JArray data)
        {
            
            if (!string.IsNullOrEmpty(functionSelect))
            {
                var functionArray = JObject.Parse(functionSelect);
                foreach (var fo in functionArray)
                {
                    var paras = new JObject();
                    var viewName = fo.Key;
                    paras.Add("viewName", viewName);
                    var paras_functionA = new JArray();
                    var foo = JObject.FromObject(fo.Value);
                    var names = new List<string>();
                    var functionColumn = JArray.FromObject(foo["functionColumn"]);
                    foreach (var fc in functionColumn)
                    {
                        var paras_functionO = new JObject();
                        paras_functionO.Add("name", fc.ToString());
                        paras_functionA.Add(paras_functionO);
                        names.Add(fc.ToString());
                    }

                    paras.Add("functionColumn", paras_functionA);
                    paras.Add("data", JArray.Parse(data.ToString().Replace($"{viewName}_", string.Empty)));

                    var result = GetFunctionColumnResult(paras);
                    if (result != null && result["Data"] != null)
                    {
                        var rdata = JArray.FromObject(result["Data"]);
                        int index = 0;
                        foreach (var jd in data.DeepClone())
                        {
                            var jda = JObject.FromObject(jd);
                            var primaryKeyColumn = foo["primaryKeyColumn"].ToString();
                            var zj = jda[$"{viewName}_{primaryKeyColumn}"].ToString();
                            var sj = rdata.Where(w => JObject.FromObject(w)[primaryKeyColumn].ToString() == zj).FirstOrDefault();
                            if (sj != null)
                            {
                                names.ForEach(f =>
                                {
                                    jda[$"{viewName}_{f}"] = sj[f];
                                });
                            }

                            data[index] = jda;
                            index++;
                        }
                    }
                }
            }

            return data;
        }

        
        
        
        
        
        public JObject Query(DynamicQueryDto dto)
        {
            DataBase dataBase = null;
            switch (dto.ObjectType)
            {
                case "1":
                    dataBase = _dbContext.Modeling.Queryable<BusinessObject, DataBase>((bo, db) => bo.DataBaseId == db.Id)
                        .Where((bo, db) => bo.Id == dto.ObjectId)
                        .Select((bo, db) => db)
                        .First();
                    break;
                case "2":
                    dataBase = _dbContext.Modeling.Queryable<CompositeObject, DataBase>((co, db) => co.DataBaseId == db.Id)
                       .Where((co, db) => co.Id == dto.ObjectId)
                       .Select((co, db) => db)
                       .First();
                    break;
                case "3":
                    dataBase = _dbContext.Modeling.Queryable<View, DataBase>((v, db) => v.DatabaseId == db.Id)
                       .Where((v, db) => v.Id == dto.ObjectId)
                       .Select((v, db) => db)
                       .First();
                    break;
            }

            if (dataBase == null)
            {
                throw new StatusNotFoundException(I18nManager.GetString("modeling.dataBase.notfound"));
            }

            var resultObject = new JObject
            {
                { "code", "200" },
                { "msg", "success" }
            };
            SqlSugarClient client = CustomDbClient.CustomDB(dataBase);
            foreach (var jItem in dto.Structure)
            {
                string key = jItem.Key.Trim();
                if (key == "[]")
                {
                    this.QueryList(resultObject, jItem, client);
                }
            }

            return resultObject;
        }

        
        
        
        
        
        private JObject GetFunctionColumnResult(JObject paras)
        {
            ViewFunctionDto para = GetFunctionColumnResult(JsonConvert.DeserializeObject<ViewFunctionDto>(paras.ToString()));
            return JObject.Parse(JsonConvert.SerializeObject(para));
        }

        
        
        
        
        
        
        private JArray RowToColumn(ViewColumns vc, JArray data, SqlSugarClient db)
        {
            foreach (var item in data)
            {
                var foreginKey = item[$"{vc.ForeignKeyObjectName}{vc.ForeignKeyObjectColumnName}"].ToString();
                ISugarQueryable<ExpandoObject> query = db.Queryable(vc.FunctionBusinessObjectName, "Cust" + vc.FunctionBusinessObjectName);
                string where = $"[\"{vc.FunctionForeignKeyBusinessObjectColumnName}::{foreginKey}\"]";
                SetWhere(where, query, string.Empty);
                var subList = query.ToList();
                string result = string.Empty;
                foreach (var subItem in subList)
                {
                    var property = subItem as IDictionary<string, object>;
                    result += property[vc.FunctionBusinessObjectColumnName] + ",";
                }

                result = result.TrimEnd(',');
                item[vc.BusinessObjectColumnAlias] = result;
            }

            return data;
        }

        
        
        
        
        
        private (ICellStyle borderStyle, ICellStyle titleStyle) GetExcelStyle(IWorkbook workbook)
        {
            #region 

            
            ICellStyle borderStyle = workbook.CreateCellStyle();
            borderStyle.BorderLeft = BorderStyle.Thin;
            borderStyle.BorderRight = BorderStyle.Thin;
            borderStyle.BorderTop = BorderStyle.Thin;
            borderStyle.BorderBottom = BorderStyle.Thin;
            #endregion

            #region 

            
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;  
            titleStyle.VerticalAlignment = VerticalAlignment.Center;  
            IFont font = workbook.CreateFont(); 
            font.IsBold = true; 
            font.FontHeightInPoints = 10; 
            font.FontName = "微软雅黑"; 
            titleStyle.SetFont(font); 
            titleStyle.FillForegroundColor = HSSFColor.Grey25Percent.Index; 
            titleStyle.FillPattern = FillPattern.SolidForeground;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            #endregion

            return (borderStyle, titleStyle);
        }

        private void AddData(string tableName, string data, SqlSugarClient client, string menu, string action, string mainTable, string mainTableKey)
        {
            var columns = _dbContext.Modeling.Queryable<BusinessObject, BusinessObjectColumn>((bo, boc) => bo.Id == boc.BusinessObjectId)
                .Where((bo, boc) => bo.Name == tableName).Select((bo, boc) => boc).ToList();
            var dtList = new Dictionary<int, List<Dictionary<string, object>>>();
            var dataArray = JArray.Parse(data);
            foreach (var dA in dataArray)
            {
                var dt = new Dictionary<string, object>();
                var daObject = JObject.Parse(dA.ToString());
                foreach (var daO in daObject)
                {
                    if (daO.Key.ToString() == "@data")
                    {
                        var daoObject = JObject.Parse(daO.Value.ToString());
                        foreach (var daoo in daoObject)
                        {
                            dt.Add(daoo.Key, this.GetDataByType(columns, daoo.Key, daoo.Value));
                        }
                    }
                    else if (daO.Key.ToString() == "@ts")
                    {
                        var daoObject = JObject.Parse(daO.Value.ToString());
                        foreach (var daoo in daoObject)
                        {
                            var tsObject = JObject.Parse(daoo.Value.ToString());
                            var noLength = !string.IsNullOrEmpty(tsObject["noLength"].ToString()) ? Convert.ToInt32(tsObject["noLength"].ToString()) : 6;
                            var prefixType = !string.IsNullOrEmpty(tsObject["prefixType"].ToString()) ? tsObject["prefixType"].ToString() : "fixed";
                            var changeType = !string.IsNullOrEmpty(tsObject["changeType"].ToString()) ? tsObject["changeType"].ToString() : "default";
                            var prefix = !string.IsNullOrEmpty(tsObject["prefix"].ToString()) ? tsObject["prefix"].ToString() : string.Empty;
                            var number = string.Empty;
                            var lastNumber = _dbContext.Modeling.Queryable<SerialNumber>().Where(a => a.NoLength == noLength).First();
                            if (lastNumber == null)
                            {
                                number = $"{prefix}{"1".ToString().PadLeft(noLength, '0')}";
                                var entity = new SerialNumber
                                {
                                    Number = number,
                                    NoLength = noLength,
                                    CreateDate = DateTime.Now
                                };
                                _dbContext.Modeling.Insertable<SerialNumber>(entity).ExecuteCommand();
                            }
                            else
                            {
                                number = $"{prefix}{(Convert.ToInt32(lastNumber.Number.Substring(lastNumber.Number.Length - noLength)) + 1).ToString().PadLeft(noLength, '0')}";
                                var entity = new SerialNumber
                                {
                                    Number = number,
                                    NoLength = noLength,
                                    CreateDate = DateTime.Now
                                };
                                _dbContext.Modeling.Updateable<SerialNumber>(entity).WhereColumns(e => e.NoLength).ExecuteCommand();
                            }

                            if (dt.ContainsKey(daoo.Key))
                            {
                                dt[daoo.Key] = number;
                            }
                            else
                            {
                                dt.Add(daoo.Key, number);
                            }
                        }
                    }
                }

                if (dtList.ContainsKey(dt.Count))
                {
                    dtList[dt.Count].Add(dt);
                }
                else
                {
                    dtList.Add(dt.Count, new List<Dictionary<string, object>> { dt });
                }
            }

            if (dtList.Count > 0)
            {
                foreach (var key in dtList.Keys)
                {
                    dtList[key].ForEach(f =>
                    {
                        client.Insertable(f).AS(tableName).ExecuteCommand();
                        _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                        {
                            Menu = menu,
                            Action = action,
                            OperationType = "新增数据",
                            MainTableName = mainTable,
                            MainTableDataId = mainTableKey,
                            TableName = tableName,
                            TableDataId = f["ID"].ToString(),
                            Data = f
                        });
                    });
                }
            }
        }

        private void EditData(string tableName, string data, SqlSugarClient client, string menu, string action, string mainTable, string mainTableKey)
        {
            var columns = _dbContext.Modeling.Queryable<BusinessObject, BusinessObjectColumn>((bo, boc) => bo.Id == boc.BusinessObjectId)
              .Where((bo, boc) => bo.Name == tableName).OrderBy((bo, boc) => boc.Order).Select((bo, boc) => boc).ToList();
            var dtListMYTJ = new Dictionary<string, Dictionary<string, object>>();
            var dtListYTJ = new Dictionary<string, Dictionary<string, object>>();
            var whereYTJ = new Dictionary<string, List<string>>();
            var parameterYTJ = new Dictionary<string, List<SugarParameter>>();
            var parameterYTJID = new Dictionary<string, List<string>>();
            var dataArray = JArray.Parse(data);
            var dtLog = new Dictionary<string, Dictionary<string, object>>();
            foreach (var dA in dataArray)
            {
                var dt = new Dictionary<string, object>();
                var daObject = JObject.Parse(dA.ToString());
                var where = new List<string>();
                var parameter = new List<SugarParameter>();
                var parameterID = new List<string>();
                var changeData = daObject["@changeData"] == null ? new JArray() : JArray.Parse(daObject["@changeData"].ToString());
                daObject.Remove("@changeData");
                var changeDataKeys = new List<string>();
                foreach (var cd in changeData)
                {
                    changeDataKeys.Add(cd.ToString().Trim());
                }

                var log = new Dictionary<string, object>();
                foreach (var daO in daObject)
                {
                    if (daO.Key.ToString() == "@data")
                    {
                        var daoObject = JObject.Parse(daO.Value.ToString());
                        foreach (var daoo in daoObject)
                        {
                            dt.Add(daoo.Key, this.GetDataByType(columns, daoo.Key, daoo.Value));
                            if (changeDataKeys.Contains(daoo.Key))
                            {
                                log.Add(daoo.Key, dt[daoo.Key]);
                            }
                        }
                    }
                    else if (daO.Key.ToString() == "@where")
                    {
                        var daoArray = JArray.Parse(daO.Value.ToString());
                        var i = 0;
                        foreach (var daoa in daoArray)
                        {
                            if (daoa.ToString().Trim().Contains("::"))
                            {
                                var arrs = daoa.ToString().Trim().Split("::");
                                where.Add($"{arrs[0]}=@pt{i}");
                                parameter.Add(new SugarParameter($"@pt{i}", arrs[1]));
                                parameterID.Add(arrs[1]);
                                i++;
                            }
                            else
                            {
                                where.Add(daoa.ToString().Trim());
                            }
                        }
                    }
                }

                var guid = Guid.NewGuid().ToString();
                if (where.Count == 0)
                {
                    dtListMYTJ.Add(guid, dt);
                }
                else
                {
                    dtListYTJ.Add(guid, dt);
                    whereYTJ.Add(guid, where);
                    parameterYTJ.Add(guid, parameter);
                    parameterYTJID.Add(guid, parameterID);
                }

                dtLog.Add(guid, log);
            }

            if (dtListMYTJ.Count > 0)
            {
                foreach (var key in dtListMYTJ.Keys)
                {
                    client.Updateable(dtListMYTJ[key]).AS(tableName).WhereColumns("ID").ExecuteCommand();
                    _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                    {
                        Menu = menu,
                        Action = action,
                        OperationType = "更新数据",
                        MainTableName = mainTable,
                        MainTableDataId = mainTableKey,
                        TableName = tableName,
                        TableDataId = dtListMYTJ[key]["ID"].ToString(),
                        OperationCondition = $"ID={dtListMYTJ[key]["ID"]}",
                        Data = dtLog[key]
                    });
                }
            }

            if (dtListYTJ.Count > 0)
            {
                foreach (var key in dtListYTJ.Keys)
                {
                    client.Updateable(dtListYTJ[key]).AS(tableName).Where(string.Join(" ", whereYTJ[key]), parameterYTJ[key]).ExecuteCommand();
                    var gxtj = string.Join(" ", whereYTJ[key]);
                    parameterYTJ[key].ForEach(f =>
                    {
                        gxtj = gxtj.Replace(f.ParameterName, f.Value.ToString());
                    });

                    _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                    {
                        Menu = menu,
                        Action = action,
                        OperationType = "更新数据",
                        MainTableName = mainTable,
                        MainTableDataId = mainTableKey,
                        TableName = tableName,
                        TableDataId = string.Join(",", parameterYTJID[key]),
                        OperationCondition = gxtj,
                        Data = dtLog[key]
                    });
                }
            }
        }

        private void DeleteData(string tableName, string data, SqlSugarClient client, string menu, string action, string mainTable, string mainTableKey)
        {
            var dataArray = JArray.Parse(data);
            foreach (var dA in dataArray)
            {
                var daObject = JObject.Parse(dA.ToString());
                var where = new List<string>();
                var parameter = new List<SugarParameter>();
                foreach (var daO in daObject)
                {
                    if (daO.Key.ToString() == "@where")
                    {
                        var daoArray = JArray.Parse(daO.Value.ToString());
                        foreach (var daoa in daoArray)
                        {
                            var i = 0;
                            if (daoa.ToString().Trim().Contains("::"))
                            {
                                var arrs = daoa.ToString().Trim().Split("::");
                                where.Add($"{arrs[0]}=@pt{i}");
                                parameter.Add(new SugarParameter($"@pt{i}", arrs[1]));
                                i++;
                            }
                            else
                            {
                                where.Add(daoa.ToString().Trim());
                            }
                        }
                    }
                }

                client.Deleteable<object>().AS(tableName).Where(string.Join(" ", where), parameter).ExecuteCommand();
                var sctj = string.Join(" ", where);
                parameter.ForEach(f =>
                {
                    sctj = sctj.Replace(f.ParameterName, f.Value.ToString());
                });
                _operationLogService.WriteLog(new OperationLog.Dtos.WriteLogDto
                {
                    Menu = menu,
                    Action = action,
                    OperationType = "删除数据",
                    MainTableName = mainTable,
                    MainTableDataId = mainTableKey,
                    TableName = tableName,
                    OperationCondition = sctj
                });
            }
        }

        private void QueryList(JObject resultObject, KeyValuePair<string, JToken> jItem, SqlSugarClient db)
        {
            var jsonObject = JObject.Parse(jItem.Value.ToString());
            var where = jsonObject["@where"] == null ? string.Empty : jsonObject["@where"].ToString();
            var order = jsonObject["@order"] == null ? string.Empty : jsonObject["@order"].ToString();
            var join = jsonObject["@join"] == null ? string.Empty : jsonObject["@join"].ToString();
            var pagination = jsonObject["@pagination"] == null ? string.Empty : jsonObject["@pagination"].ToString();
            var whereCustom = jsonObject["@whereCustom"] == null ? string.Empty : jsonObject["@whereCustom"].ToString();
            var functionSelect = jsonObject["@functionSelect"] == null ? string.Empty : jsonObject["@functionSelect"].ToString();
            var group = jsonObject["@group"] == null ? string.Empty : jsonObject["@group"].ToString();
            var sourcePageTyge = jsonObject["@sourcePageTyge"] == null ? string.Empty : jsonObject["@sourcePageTyge"].ToString();
            jsonObject.Remove("@pagination");
            jsonObject.Remove("@where");
            jsonObject.Remove("@order");
            jsonObject.Remove("@join");
            jsonObject.Remove("@whereCustom");
            jsonObject.Remove("@functionSelect");
            jsonObject.Remove("@group");
            jsonObject.Remove("@sourcePageTyge");
            var total = 0;
            var mainTable = string.Empty;
            var processinstancerecord = new List<string>();
            var selectModels = new List<SelectModel>();
            ISugarQueryable<ExpandoObject> query = null;
            foreach (var jsonTable in jsonObject)
            {
                var tableObject = JObject.Parse(jsonTable.Value.ToString());
                mainTable = jsonTable.Key.Trim();
                query = db.Queryable(mainTable, "Cust" + mainTable);
                var select = tableObject["@select"] == null ? string.Empty : tableObject["@select"].ToString();
                if (!string.IsNullOrEmpty(select))
                {
                    var selectObject = JArray.Parse(select);
                    foreach (var selectColumn in selectObject)
                    {
                        selectModels.Add(new SelectModel { FiledName = $"Cust{mainTable}.{selectColumn}", AsName = $"{mainTable}_{selectColumn}" });
                    }
                }

                break;
            }

            this.SetJoin(join, query, selectModels,sourcePageTyge, processinstancerecord);

            query.Select(selectModels);

            this.SetWhere(where, query);

            this.SetWhereCustom(whereCustom, query);

            this.SetGroup(group, query);

            if (!string.IsNullOrEmpty(order))
            {
                var orderList = new List<string>();
                var orderObject = JArray.Parse(order);
                foreach (var orderColumn in orderObject)
                {
                    var col = orderColumn.ToString().Replace("+", string.Empty).Replace("-", string.Empty);
                    if (sourcePageTyge == "search_list" && processinstancerecord.Count > 0)
                    {
                        col = col.Replace(".", "_");
                    }
                    else
                    {
                        col = $"Cust{col}";
                    }

                    if (orderColumn.ToString().EndsWith("-"))
                    {
                        orderList.Add($"{col} desc");
                    }
                    else
                    {
                        orderList.Add($"{col} asc");
                    }
                }

                if (orderList.Count > 0)
                {
                    query.OrderBy(string.Join(",", orderList));
                }
            }

            var sql = query.ToSql();
            var objectItems = new List<ExpandoObject>();
            if (!string.IsNullOrEmpty(pagination))
            {
                var pObject = JObject.Parse(pagination);
                var pageIndex = string.IsNullOrEmpty(pObject["@pageIndex"].ToString()) ? "1" : pObject["@pageIndex"].ToString();
                var pageSize = string.IsNullOrEmpty(pObject["@pageSize"].ToString()) ? "10" : pObject["@pageSize"].ToString();
                objectItems = query.ToPageList(int.Parse(pageIndex), int.Parse(pageSize), ref total);
            }
            else
            {
                objectItems = query.ToList();
            }

            var data = this.SetFunctionSelect(functionSelect, JArray.FromObject(objectItems));
            var mainTableJObject = new JObject();
            mainTableJObject.Add(mainTable, data);
            resultObject.Add("[]", mainTableJObject);
            resultObject.Add("total", JToken.Parse(total.ToString()));
        }

        private void SetWhere(string where, ISugarQueryable<ExpandoObject> query, string prefix = null)
        {
            if (!string.IsNullOrEmpty(where))
            {
                if (prefix == null)
                {
                    prefix = "Cust";
                }

                var conditonModels = new List<IConditionalModel>();
                var whereArray = JArray.Parse(where);
                foreach (var whereColumn in whereArray)
                {
                    var wheres = whereColumn.ToString().Split(":,");
                    if (wheres.Length > 0)
                    {
                        var ddt = new List<KeyValuePair<WhereType, ConditionalModel>>();
                        foreach (var w in wheres)
                        {
                            var arrs = w.ToString().Split("::");
                            var fieldName = string.Empty;
                            var fieldValue = arrs[1];
                            var conditionalType = ConditionalType.Equal;
                            var cSharpTypeName = string.Empty;
                            var opt = arrs[0].Trim().EndsWith("|");
                            arrs[0] = arrs[0].Replace("|", string.Empty);
                            if (arrs[0].Trim().EndsWith(">"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace(">", string.Empty);
                                conditionalType = ConditionalType.GreaterThan;
                            }
                            else if (arrs[0].Trim().EndsWith(">="))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace(">=", string.Empty);
                                conditionalType = ConditionalType.GreaterThanOrEqual;
                            }
                            else if (arrs[0].Trim().EndsWith("<"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("<", string.Empty);
                                conditionalType = ConditionalType.LessThan;
                            }
                            else if (arrs[0].Trim().EndsWith("<="))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("<=", string.Empty);
                                conditionalType = ConditionalType.LessThanOrEqual;
                            }
                            else if (arrs[0].Trim().EndsWith("!"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("!", string.Empty);
                                conditionalType = ConditionalType.NoEqual;
                            }
                            else if (arrs[0].Trim().StartsWith("%") && arrs[0].Trim().EndsWith("%"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("%", string.Empty);
                                conditionalType = ConditionalType.Like;
                            }
                            else if (arrs[0].Trim().StartsWith("%"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("%", string.Empty);
                                conditionalType = ConditionalType.LikeRight;
                            }
                            else if (arrs[0].Trim().EndsWith("%"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("%", string.Empty);
                                conditionalType = ConditionalType.LikeLeft;
                            }
                            else if (arrs[0].Trim().EndsWith(">=d"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace(">=d", string.Empty);
                                conditionalType = ConditionalType.GreaterThanOrEqual;
                                cSharpTypeName = "DateTime";
                            }
                            else if (arrs[0].Trim().EndsWith("<=d"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("<=d", string.Empty);
                                conditionalType = ConditionalType.LessThanOrEqual;
                                cSharpTypeName = "DateTime";
                            }
                            else if (arrs[0].Trim().EndsWith("@"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("@", string.Empty);
                                conditionalType = ConditionalType.In;
                            }
                            else if (arrs[0].Trim().EndsWith("!@"))
                            {
                                fieldName = prefix + arrs[0].Trim().Replace("!@", string.Empty);
                                conditionalType = ConditionalType.NotIn;
                            }
                            else
                            {
                                fieldName = prefix + arrs[0].Trim();
                            }

                            var cModel = new ConditionalModel()
                            {
                                FieldName = fieldName,
                                ConditionalType = conditionalType,
                                FieldValue = fieldValue
                            };

                            if (!string.IsNullOrEmpty(cSharpTypeName))
                            {
                                cModel.CSharpTypeName = cSharpTypeName;
                            }

                            ddt.Add(new KeyValuePair<WhereType, ConditionalModel>(opt ? WhereType.Or : WhereType.And, cModel));
                        }

                        conditonModels.Add(new ConditionalCollections()
                        {
                            ConditionalList = ddt
                        });
                    }
                    else
                    {
                        var arrs = whereColumn.ToString().Split("::");
                        var fieldName = string.Empty;
                        var fieldValue = arrs[1];
                        var conditionalType = ConditionalType.Equal;
                        var cSharpTypeName = string.Empty;
                        if (arrs[0].Trim().EndsWith(">"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace(">", string.Empty);
                            conditionalType = ConditionalType.GreaterThan;
                        }
                        else if (arrs[0].Trim().EndsWith(">="))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace(">=", string.Empty);
                            conditionalType = ConditionalType.GreaterThanOrEqual;
                        }
                        else if (arrs[0].Trim().EndsWith("<"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("<", string.Empty);
                            conditionalType = ConditionalType.LessThan;
                        }
                        else if (arrs[0].Trim().EndsWith("<="))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("<=", string.Empty);
                            conditionalType = ConditionalType.LessThanOrEqual;
                        }
                        else if (arrs[0].Trim().StartsWith("%") && arrs[0].Trim().EndsWith("%"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("%", string.Empty);
                            conditionalType = ConditionalType.Like;
                        }
                        else if (arrs[0].Trim().StartsWith("%"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("%", string.Empty);
                            conditionalType = ConditionalType.LikeLeft;
                        }
                        else if (arrs[0].Trim().EndsWith("%"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("%", string.Empty);
                            conditionalType = ConditionalType.LikeRight;
                        }
                        else if (arrs[0].Trim().EndsWith(">=d"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace(">=d", string.Empty);
                            conditionalType = ConditionalType.GreaterThanOrEqual;
                            cSharpTypeName = "DateTime";
                        }
                        else if (arrs[0].Trim().EndsWith("<=d"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("<=d", string.Empty);
                            conditionalType = ConditionalType.LessThanOrEqual;
                            cSharpTypeName = "DateTime";
                        }
                        else if (arrs[0].Trim().EndsWith("@"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("@", string.Empty);
                            conditionalType = ConditionalType.In;
                        }
                        else if (arrs[0].Trim().EndsWith("!@"))
                        {
                            fieldName = prefix + arrs[0].Trim().Replace("!@", string.Empty);
                            conditionalType = ConditionalType.NotIn;
                        }
                        else
                        {
                            fieldName = prefix + arrs[0].Trim();
                        }

                        var cModel = new ConditionalModel()
                        {
                            FieldName = fieldName,
                            ConditionalType = conditionalType,
                            FieldValue = fieldValue
                        };

                        if (!string.IsNullOrEmpty(cSharpTypeName))
                        {
                            cModel.CSharpTypeName = cSharpTypeName;
                        }

                        conditonModels.Add(cModel);
                    }
                }

                if (conditonModels.Count > 0)
                {
                    query.Where(conditonModels);
                }
            }
        }

        private void SetWhereCustom(string whereCustom, ISugarQueryable<ExpandoObject> query)
        {
            query.Where(whereCustom);
        }

        private void SetOrder(string order, ISugarQueryable<ExpandoObject> query)
        {
            if (!string.IsNullOrEmpty(order))
            {
                var orderList = new List<string>();
                var orderObject = JArray.Parse(order);
                foreach (var orderColumn in orderObject)
                {
                    var col = orderColumn.ToString().Replace("+", string.Empty).Replace("-", string.Empty);
                    if (orderColumn.ToString().EndsWith("-"))
                    {
                        orderList.Add($"Cust{col} desc");
                    }
                    else
                    {
                        orderList.Add($"Cust{col} asc");
                    }
                }

                if (orderList.Count > 0)
                {
                    query.OrderBy(string.Join(",", orderList));
                }
            }
        }

        private void SetGroup(string group, ISugarQueryable<ExpandoObject> query)
        {
            if (!string.IsNullOrEmpty(group))
            {
                var groupList = new List<string>();
                var groupObject = JArray.Parse(group);
                foreach (var col in groupObject)
                {
                    groupList.Add($"Cust{col}");
                }

                if (groupList.Count > 0)
                {
                    query.GroupBy(string.Join(",", groupList));
                }
            }
        }

        private void SetJoin(string join, ISugarQueryable<ExpandoObject> query, List<SelectModel> selectModels, string sourcePageType, List<string> processinstancerecord = null, string colAliasSplitString = null)
        {
            if (!string.IsNullOrEmpty(join))
            {
                var tIndex = 0;
                var joinObject = JObject.Parse(join);
                foreach (var tableObject in joinObject)
                {
                    var tableName = tableObject.Key.ToString();
                    var joinType = JoinType.Inner;
                    if (tableName.EndsWith(">"))
                    {
                        tableName = tableName.Replace(">", string.Empty);
                        joinType = JoinType.Left;
                    }
                    else if (tableName.EndsWith("<"))
                    {
                        tableName = tableName.Replace("<", string.Empty);
                        joinType = JoinType.Right;
                    }

                    var tableValueObject = JObject.Parse(tableObject.Value.ToString());
                    var on = tableValueObject["@on"] == null ? string.Empty : tableValueObject["@on"].ToString();
                    var onList = new List<object>();
                    var pars = new List<int>();
                    if (!string.IsNullOrEmpty(on))
                    {
                        var index = 0;
                        var selectArray = JArray.Parse(on);
                        foreach (var selectColumn in selectArray)
                        {
                            if (index == 0)
                            {
                                var arrs = selectColumn.ToString().Split("::");
                                onList.Add($"Cust{tableName}.{arrs[0].Trim()}");
                                onList.Add("=");
                                onList.Add($"Cust{arrs[1].Trim()}");
                                if (tableName.ToOurLowerString() == "processinstancerecord" && processinstancerecord != null)
                                {
                                    processinstancerecord.Add($"Cust{arrs[1].Trim()}");
                                }
                            }
                            else
                            {
                                onList.Add("&&");
                                var arrs = selectColumn.ToString().Split("::");
                                if (arrs[0].Trim().EndsWith(">"))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace(">", string.Empty)}");
                                    onList.Add(">");
                                }
                                else if (arrs[0].Trim().EndsWith(">="))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace(">=", string.Empty)}");
                                    onList.Add(">=");
                                }
                                else if (arrs[0].Trim().EndsWith("<"))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace("<", string.Empty)}");
                                    onList.Add("<");
                                }
                                else if (arrs[0].Trim().EndsWith("<="))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace("<=", string.Empty)}");
                                    onList.Add("<=");
                                }
                                else if (arrs[0].Trim().StartsWith("%") && arrs[0].Trim().EndsWith("%"))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace("%", string.Empty)}");
                                    onList.Add("like");
                                }
                                else
                                {
                                    onList.Add($"Cust{arrs[0].Trim()}");
                                    onList.Add("=");
                                }

                                onList.Add($"{{string}}:{arrs[1]}");
                                pars.Add(pars.Count);
                            }

                            index++;
                        }
                    }

                    var funcModel = new ObjectFuncModel()
                    {
                        FuncName = "SqlFunc_Format",
                        Parameters = onList
                    };

                    var newTableName = sourcePageType == "search_list" && tableName.ToOurLowerString() == "processinstancerecord" ? "(select p1.* from (select p.*, row_number() over(partition by p.DataId,p.Btid,p.Bsid order by p.CreateDate desc) as rowNumber  from processinstancerecord p) p1 where p1.rowNumber = 1)" : tableName;
                    query.AddJoinInfo(newTableName, "Cust" + tableName, funcModel, joinType);
                    pars.Reverse();
                    pars.ForEach(f =>
                    {
                        var pname = $"@p{f}";
                        query.QueryBuilder.JoinQueryInfos[tIndex].JoinWhere = query.QueryBuilder.JoinQueryInfos[tIndex].JoinWhere.Replace(pname, $"{pname}{tIndex}");
                        var par = query.QueryBuilder.Parameters.Find(pf => pf.ParameterName == pname);
                        par.ParameterName = $"{pname}{tIndex}";
                    });

                    var select = tableValueObject["@select"] == null ? string.Empty : tableValueObject["@select"].ToString();
                    if (!string.IsNullOrEmpty(select))
                    {
                        string splitChar = colAliasSplitString == null ? "_" : colAliasSplitString;
                        var selectObject = JArray.Parse(select);
                        foreach (var selectColumn in selectObject)
                        {
                            selectModels.Add(new SelectModel { FiledName = $"Cust{tableName}.{selectColumn}", AsName = $"{tableName}{splitChar}{selectColumn}" });
                        }
                    }

                    tIndex++;
                }
            }
        }

        private void SetJoin(string join, ISugarQueryable<ExpandoObject> query, List<string> selectList, string colAliasSplitString = null)
        {
            if (!string.IsNullOrEmpty(join))
            {
                var tIndex = 0;
                var joinObject = JObject.Parse(join);
                foreach (var tableObject in joinObject)
                {
                    var tableName = tableObject.Key.ToString();
                    var joinType = JoinType.Inner;
                    if (tableName.EndsWith(">"))
                    {
                        tableName = tableName.Replace(">", string.Empty);
                        joinType = JoinType.Left;
                    }
                    else if (tableName.EndsWith("<"))
                    {
                        tableName = tableName.Replace("<", string.Empty);
                        joinType = JoinType.Right;
                    }

                    var tableValueObject = JObject.Parse(tableObject.Value.ToString());
                    var on = tableValueObject["@on"] == null ? string.Empty : tableValueObject["@on"].ToString();
                    var onList = new List<object>();
                    var pars = new List<int>();
                    if (!string.IsNullOrEmpty(on))
                    {
                        var index = 0;
                        var selectArray = JArray.Parse(on);
                        foreach (var selectColumn in selectArray)
                        {
                            if (index == 0)
                            {
                                var arrs = selectColumn.ToString().Split("::");
                                onList.Add($"Cust{tableName}.{arrs[0].Trim()}");
                                onList.Add("=");
                                onList.Add($"Cust{arrs[1].Trim()}");
                            }
                            else
                            {
                                onList.Add("&&");
                                var arrs = selectColumn.ToString().Split("::");
                                if (arrs[0].Trim().EndsWith(">"))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace(">", string.Empty)}");
                                    onList.Add(">");
                                }
                                else if (arrs[0].Trim().EndsWith(">="))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace(">=", string.Empty)}");
                                    onList.Add(">=");
                                }
                                else if (arrs[0].Trim().EndsWith("<"))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace("<", string.Empty)}");
                                    onList.Add("<");
                                }
                                else if (arrs[0].Trim().EndsWith("<="))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace("<=", string.Empty)}");
                                    onList.Add("<=");
                                }
                                else if (arrs[0].Trim().StartsWith("%") && arrs[0].Trim().EndsWith("%"))
                                {
                                    onList.Add($"Cust{arrs[0].Trim().Replace("%", string.Empty)}");
                                    onList.Add("like");
                                }
                                else
                                {
                                    onList.Add($"Cust{arrs[0].Trim()}");
                                    onList.Add("=");
                                }

                                onList.Add($"{{string}}:{arrs[1]}");
                                pars.Add(pars.Count);
                            }

                            index++;
                        }
                    }

                    var funcModel = new ObjectFuncModel()
                    {
                        FuncName = "SqlFunc_Format",
                        Parameters = onList
                    };

                    query.AddJoinInfo(tableName, "Cust" + tableName, funcModel, joinType);
                    pars.Reverse();
                    pars.ForEach(f =>
                    {
                        var pname = $"@p{f}";
                        query.QueryBuilder.JoinQueryInfos[tIndex].JoinWhere = query.QueryBuilder.JoinQueryInfos[tIndex].JoinWhere.Replace(pname, $"{pname}{tIndex}");
                        var par = query.QueryBuilder.Parameters.Find(pf => pf.ParameterName == pname);
                        par.ParameterName = $"{pname}{tIndex}";
                    });

                    var select = tableValueObject["@select"] == null ? string.Empty : tableValueObject["@select"].ToString();
                    if (!string.IsNullOrEmpty(select))
                    {
                        string splitChar = colAliasSplitString == null ? "_" : colAliasSplitString;
                        var selectObject = JArray.Parse(select);
                        foreach (var selectColumn in selectObject)
                        {
                            selectList.Add($"Cust{tableName}.{selectColumn} as {tableName}{splitChar}{selectColumn}");
                        }
                    }

                    tIndex++;
                }
            }
        }

        private object GetDataByType(List<BusinessObjectColumn> columns, string columnName, object columnValue)
        {
            var column = columns.Find(f => f.Name == columnName);
            if (column != null)
            {
                if (column.DisplayType == "text")
                {
                    return columnValue.ToString();
                }
                else if (column.DisplayType == "number" && string.IsNullOrEmpty(columnValue.ToString()))
                {
                    return null;
                }
                else if (column.DisplayType == "bool")
                {
                    if (string.IsNullOrEmpty(columnValue.ToString()))
                    {
                        return null;
                    }
                    else
                    {
                        var isBol = false;
                        var isNum = 0;
                        if (int.TryParse(columnValue.ToString(), out isNum))
                        {
                            return isNum == 0 ? false : true;
                        }
                        else if (bool.TryParse(columnValue.ToString(), out isBol))
                        {
                            return isBol;
                        }

                        return isBol;
                    }
                }
            }

            return columnValue;
        }
    }
}
