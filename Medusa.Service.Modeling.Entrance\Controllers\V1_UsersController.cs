using System;
using System.Collections.Generic;
using Medusa.Service.Modeling.Application;
using Medusa.Service.Modeling.Application.Users;
using Medusa.Service.Modeling.Core;
using Microsoft.AspNetCore.Mvc;

namespace Medusa.Service.Modeling.Entrance.Controllers
{
    /// <summary>
    /// users controller
    /// </summary>
    [Route("v1")]
    [ApiExplorerSettings(GroupName = "User.v1")]
    public class V1_UsersController : ProductControllerBase
    {
        #region //  服务注入
        readonly IUserService _userService;

        /// <summary>
        /// Initializes a new instance of the <see cref="V1_UsersController"/> class.
        /// Constructor
        /// </summary>
        /// <param name="userService">用户Service</param>
        public V1_UsersController(IUserService userService)
        {
            _userService = userService;
        }
        #endregion

        #region //  登录

        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="dto">登录信息</param>
        /// <returns>用户信息</returns>
        [HttpPost("users")]
        [ModelFieldCheck]
        public UserLoginDto Post([FromBody] LoginModel dto)
        {
            return _userService.Login(dto);
        }
        #endregion
    }
}