using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Medusa.Service.Modeling.Application.CRMReport.Dto;
using Medusa.Service.Modeling.Core;
using Medusa.Service.Modeling.Core.Entities.SWApp;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SqlSugar;

namespace Medusa.Service.Modeling.Application.CRMReport
{
    
    
    
    public class CRMReportService : ServiceBase, ICRMReportService
    {
        private readonly MyDbContext _dbContext;
        readonly string setPath = string.Empty;

        
        
        
        
        public CRMReportService(IServiceProvider serviceProvider)
        {
            _dbContext = serviceProvider.GetService<MyDbContext>();
            var appSettings = serviceProvider.GetService<IMemoryCache>().Get<JObject>("AppSettings");
            setPath = appSettings["BusinessOrg"]?["SET"]?.ToString();
        }

        
        
        
        public MyDbContext P_DbContext => _dbContext;

        
        
        
        
        
        public List<ReportBaseDto> GetStageCount(string flag)
        {
            var data = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .Where(x => x.IsDelete == 0 && x.Flag == flag && x.CreateUserOrgPathId.StartsWith(setPath))
                .GroupBy(p => new { p.SaleStage, p.SaleStageName })
                .OrderBy(o => o.SaleStage)
                .Select(j => new ReportBaseDto
                {
                    Value = SqlFunc.AggregateCount(j.SaleStage),
                    Name = j.SaleStageName
                }).ToList();
            return data;
        }

        
        
        
        
        
        public BarChartDto GetStageCount_Bar(string flag)
        {
            var bar = new BarChartDto();
            var data = GetStageCount(flag);
            if (data != null && data.Count > 0)
            {
                bar.Value = data.Select(x => x.Value).ToList();
                bar.Name = data.Select(x => x.Name).ToList();
            }

            return bar;
        }

        
        
        
        
        
        public FunnelDto GetStageCount_Funnel(string flag)
        {
            var funnel = new FunnelDto()
            {
                Data = new List<ReportBaseDto>(),
                SeriesData = new List<ReportBaseDto>()
            };
            var data = GetStageCount(flag);
            if (data != null && data.Count > 0)
            {
                var sum = data.Sum(x => x.Value);
                funnel.SeriesData = data;
                funnel.Legend = data.Select(x => x.Name).ToList();
                foreach (var item in data)
                {
                    var per = Math.Round((item.Value / sum) * 100, 0);
                    funnel.Data.Add(new ReportBaseDto()
                    {
                        Value = per,
                        Name = item.Name
                    });
                }
            }

            return funnel;
        }

        
        
        
        
        public List<ReportBaseDto> GetProductTypeCount_SET()
        {
            var data = _dbContext.Modeling.Queryable<CRMSaleClueSET>()
                .LeftJoin<CRMProduct>((a, b) => a.MainType == b.StockName)
                .Where((a, b) => a.IsDelete == 0 && b.IsDelete == 0 && b.CreateUserOrgPathId.StartsWith(setPath) && a.CreateUserOrgPathId.StartsWith(setPath))
                .GroupBy((a, b) => new { a.MainType, a.MainTypeName })
                .OrderBy((a, b) => a.MainType)
                .Select((a, b) => new ReportBaseDto
                {
                    Value = SqlFunc.AggregateCount(a.MainType),
                    Name = a.MainTypeName
                }).ToList();
            return data;
        }

        
        
        
        
        public BarChartDto GetProductTypeCount_SET_Bar()
        {
            var bar = new BarChartDto();
            var data = GetProductTypeCount_SET();
            if (data != null && data.Count > 0)
            {
                bar.Value = data.Select(x => x.Value).ToList();
                bar.Name = data.Select(x => x.Name).ToList();
            }

            return bar;
        }

        
        
        
        
        
        public List<SelectDto> GetProductType(string flag)
        {
            var data = _dbContext.Modeling.Queryable<CRMProduct>()
                .InnerJoin<CRMSaleClueSET>((a, b) => a.StockName == b.MainType)
                .InnerJoin<CRMSaleClue>((a, b, c) => b.ID == b.SaleClueID)
                .Where((a, b, c) => a.IsDelete == 0 && b.IsDelete == 0 && c.IsDelete == 0 && a.CreateUserOrgPathId.StartsWith(setPath) && c.CreateUserOrgPathId.StartsWith(setPath))
                .GroupBy((a, b, c) => new { a.StockName })
                .Select((a, b, c) => new SelectDto
                {
                    Name = a.StockName,
                    Value = a.StockName
                }).ToList();
            return data;
        }

        
        
        
        
        
        public List<ReportBaseDto> GetStageCountBySearch_SET(SearchDto search)
        {
            var data = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .InnerJoin<CRMSaleClueSET>((a, b) => a.ID == b.SaleClueID)
                .Where((a, b) => a.IsDelete == 0 && b.IsDelete == 0 && a.CreateUserOrgPathId.StartsWith(setPath))
                .WhereIF(!string.IsNullOrWhiteSpace(search.ProductType), (a, b) => b.MainType == search.ProductType)
                .WhereIF(!string.IsNullOrWhiteSpace(search.SalesUserId), (a, b) => a.SalesUserId == search.SalesUserId)
                .WhereIF(!string.IsNullOrWhiteSpace(search.MarketUserId), (a, b) => a.MarketUserId == search.MarketUserId)
                .GroupBy((a, b) => new { a.SaleStage, a.SaleStageName })
                .OrderBy((a, b) => a.SaleStage)
                .Select((a, b) => new ReportBaseDto
                {
                    Value = SqlFunc.AggregateCount(a.SaleStage),
                    Name = a.SaleStageName
                }).ToList();
            return data;
        }

        
        
        
        
        
        public BarChartDto GetStageCountBySearch_SET_Bar(SearchDto search)
        {
            var bar = new BarChartDto();
            var data = GetStageCountBySearch_SET(search);
            if (data != null && data.Count > 0)
            {
                bar.Name = data.Select(x => x.Name).ToList();
                bar.Value = data.Select(x => x.Value).ToList();
            }

            return bar;
        }

        
        
        
        
        
        public List<ReportBaseDto> GetMarketCount(string flag)
        {
            var data = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .Where(x => x.IsDelete == 0 && x.Flag == flag && x.CreateUserOrgPathId.StartsWith(setPath) && !string.IsNullOrWhiteSpace(x.MarketUserId))
                .GroupBy(x => new { x.MarketUserId, x.MarketName })
                .Select(x => new ReportBaseDto()
                {
                    Name = x.MarketName,
                    Value = SqlFunc.AggregateCount(x.MarketUserId)
                }).ToList();
            return data;
        }

        
        
        
        
        
        public List<ReportBaseDto> GetSalesCount(string flag)
        {
            var data = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .Where(x => x.IsDelete == 0 && x.Flag == flag && x.CreateUserOrgPathId.StartsWith(setPath) && !string.IsNullOrWhiteSpace(x.SalesUserId))
                .GroupBy(x => new { x.SalesUserId, x.SalesName })
                .Select(x => new ReportBaseDto()
                {
                    Name = x.SalesName,
                    Value = SqlFunc.AggregateCount(x.SalesUserId)
                }).ToList();
            return data;
        }

        
        
        
        
        
        public object GetUsers(string flag)
        {
            var data = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .Where(x => x.IsDelete == 0 && x.Flag == flag).ToList();
            var salesUser = data.GroupBy(x => new { x.SalesUserId, x.SalesName }).Select(x => new SelectDto() { Name = x.Key.SalesName, Value = x.Key.SalesUserId }).ToList();
            var marketUser = data.GroupBy(x => new { x.MarketUserId, x.MarketName }).Select(x => new SelectDto() { Name = x.Key.MarketName, Value = x.Key.MarketUserId }).ToList();
            return new { salesUser = salesUser, marketUser = marketUser };
        }

        
        
        
        
        
        public List<SXTypeCountDto> GetSXTypeProjectCount(string flag)
        {
            
            
            var data = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .LeftJoin<CRMSaleClue>((a, b) => a.ID == b.ID && a.Flag == flag && b.IsDelete == 0 && a.CreateUserOrgPathId.StartsWith(setPath) && !string.IsNullOrWhiteSpace(b.SXType) && b.SaleStage == "SET006")
                .Where((a, b) => a.IsDelete == 0 && !string.IsNullOrWhiteSpace(a.SXType))
                .GroupBy((a, b) => new { a.SXType, a.SXTYpeName })
                 .Select((a, b) => new SXTypeCountDto
                 {
                     Name = a.SXTYpeName,
                     OrderCount = SqlFunc.AggregateCount(b.SXTYpeName),
                     SXTypeCount = SqlFunc.AggregateCount(a.SXTYpeName)
                 }).ToList();
            return data;
        }

        
        
        
        
        
        public List<SXTypeCountDto> GetSXTypeCustomerCount(string flag)
        {
            var data = new List<SXTypeCountDto>();
            var sql = @"select a.SXTypeName,Count(a.SXType) as SXTypeCount,Count(b.SXType) as OrderCount from 
(select CustomerId,SXTypeName,SXType from CRMSaleClue where IsDelete=0 and Flag=@flag and SXType is not null
GROUP BY CustomerId,SXTypeName,SXType) as a left join 
(select CustomerId,SXTypeName,SXType from CRMSaleClue where IsDelete=0 and Flag=@flag and SaleStage='SET006' and SXType is not null
GROUP BY CustomerId,SXTypeName,SXType
) as b on a.CustomerID=b.CustomerID and a.SXTypeName=b.SXTypeName and a.SXType=b.SXType
GROUP BY a.SXTypeName,a.SXType";
            var dt = _dbContext.Modeling.Ado.GetDataTable(sql, new List<SugarParameter>()
                {
                    new SugarParameter("@flag", flag),
                });
            if (dt != null && dt.Rows.Count > 0)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    var item = new SXTypeCountDto()
                    {
                        Name = dr["SXTypeName"].ToString(),
                        OrderCount = Convert.ToInt32(dr["OrderCount"]),
                        SXTypeCount = Convert.ToInt32(dr["SXTypeCount"])
                    };
                    data.Add(item);
                }
            }

            return data;
        }

        
        
        
        
        
        public List<ReportBaseDto> GetProvinceCount(string flag)
        {
            var data = _dbContext.Modeling.Queryable<CRMProvince>().Where(x => x.IsDelete == 0).Select(x => new ReportBaseDto()
            {
                Name = x.Name,
                Value = SqlFunc.Subqueryable<CRMCustomer>().InnerJoin<CRMSaleClue>((a, b) => a.ID == b.CustomerId && b.IsDelete == 0 && b.Flag == flag && b.CreateUserOrgPathId.StartsWith(setPath))
            .Where((a, b) => a.IsDelete == 0 && a.Province == x.Code).Count()
            }).ToList();

            data.Add(new ReportBaseDto()
            {
                Name = "台湾",
                Value = 0
            });
            data.Add(new ReportBaseDto()
            {
                Name = "南海诸岛",
                Value = 0
            });
            return data;
        }

        
        
        
        
        
        public List<User_ProductDto> GetSalesProductCount(string flag)
        {
            var data = new List<User_ProductDto>();
            var sales = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .Where(x => x.IsDelete == 0 && x.Flag == flag && !string.IsNullOrWhiteSpace(x.SalesUserId) && x.CreateUserOrgPathId.StartsWith(setPath))
                .GroupBy(x => new { x.SalesName, x.SalesUserId })
                .Select(x => new
                {
                    SalesName = x.SalesName,
                    SalesUserId = x.SalesUserId
                }).ToList();

            if (sales != null && sales.Count > 0)
            {
                foreach (var u in sales)
                {
                    var product = _dbContext.Modeling.Queryable<CRMProduct>()
                 .LeftJoin<CRMSaleClueSET>((a, b) => a.StockName == b.MainType && b.IsDelete == 0)
                 .LeftJoin<CRMSaleClue>((a, b, c) => b.SaleClueID == c.ID && c.IsDelete == 0 && c.SalesUserId == u.SalesUserId && c.CreateUserOrgPathId.StartsWith(setPath))
                 .Where((a, b, c) => a.IsDelete == 0 && a.CreateUserOrgPathId.StartsWith(setPath))
                 .GroupBy((a, b, c) => new { a.StockName })
                 .Select((a, b, c) => new ReportBaseDto
                 {
                     Name = a.StockName,
                     Value = SqlFunc.AggregateCount(c.SalesUserId)
                 }).ToList();
                    data.Add(new User_ProductDto()
                    {
                        ProductData = product,
                        Total = product.Sum(x => x.Value),
                        UserId = u.SalesUserId,
                        UserName = u.SalesName
                    });
                }
            }

            return data;
        }

        
        
        
        
        
        public List<User_ProductDto> GetMarketProductCount(string flag)
        {
            var data = new List<User_ProductDto>();
            var sales = _dbContext.Modeling.Queryable<CRMSaleClue>()
                .Where(x => x.IsDelete == 0 && x.Flag == flag && !string.IsNullOrWhiteSpace(x.MarketUserId) && x.CreateUserOrgPathId.StartsWith(setPath))
                .GroupBy(x => new { x.MarketName, x.MarketUserId })
                .Select(x => new
                {
                    MarketName = x.MarketName,
                    MarketUserId = x.MarketUserId
                }).ToList();

            if (sales != null && sales.Count > 0)
            {
                foreach (var u in sales)
                {
                    var product = _dbContext.Modeling.Queryable<CRMProduct>()
                 .LeftJoin<CRMSaleClueSET>((a, b) => a.StockName == b.MainType && b.IsDelete == 0)
                 .LeftJoin<CRMSaleClue>((a, b, c) => b.SaleClueID == c.ID && c.IsDelete == 0 && c.MarketUserId == u.MarketUserId && c.CreateUserOrgPathId.StartsWith(setPath))
                 .Where((a, b, c) => a.IsDelete == 0 && a.CreateUserOrgPathId.StartsWith(setPath))
                 .GroupBy((a, b, c) => new { a.StockName })
                 .Select((a, b, c) => new ReportBaseDto
                 {
                     Name = a.StockName,
                     Value = SqlFunc.AggregateCount(c.MarketUserId)
                 }).ToList();
                    data.Add(new User_ProductDto()
                    {
                        ProductData = product,
                        Total = product.Sum(x => x.Value),
                        UserId = u.MarketUserId,
                        UserName = u.MarketName
                    });
                }
            }

            return data;
        }

        
        
        
        
        
        public Tuple<List<string>, DataTable, int> GetDailyReport(DailyReportQueryDto dto)
        {
            var dtable = new DataTable();
            var cols = new List<string>();
            var wheresql = string.Empty;
            if (!string.IsNullOrWhiteSpace(dto.SalesName))
            {
                wheresql += string.Format(" and c.UserName like '%{0}%'", dto.SalesName);
            }

            if (!string.IsNullOrWhiteSpace(dto.DepName))
            {
                wheresql += string.Format(" and c.FullPathText like '%{0}%'", dto.DepName);
            }

            var sql = string.Format(
                @"select distinct C.*,d.ReportDate from (select u.UserLoginId,u.UserName,u.FullPathText 
from boost.roles as r 
inner join boost.roleuserrelations as ru on r.roleId=ru.roleId
inner join boost.users as u on u.UserId=ru.UserId
where RoleCode='CRMOutSales') as c 
left join (
select TBR,TBRCode,group_concat(ReportDate) as ReportDate
from crmdailyreport where ReportDate!='' and ReportDate is not null 
-- and ReportDate>=@StartDate and ReportDate<=@EndDate
group by TBR,TBRCode
) as d on c.UserLoginId=d.TBRCode
where c.UserLoginId!='' {0}
order by c.UserLoginId", wheresql);
            var sd = dto.StartDate.Value;
            var ed = dto.EndDate.Value;
            var data = _dbContext.Modeling.Ado.SqlQuery<DailyReportDto>(sql, new List<SugarParameter>()
                {
                    new SugarParameter("@EndDate", ed.ToString("yyyy-MM-dd")),
                    new SugarParameter("@StartDate", sd.ToString("yyyy-MM-dd"))
                });
            var total = data.Count();
            var dic = new Dictionary<string, int>();
            if (data != null && data.Count > 0)
            {
                dtable.Columns.Add("序号");
                dtable.Columns.Add("组织");
                dtable.Columns.Add("销售担当");
                var diff = Diff(sd, ed);
                for (int i = 0; i <= diff; i++)
                {
                    var date = sd.AddDays(i).ToString("yyyy-MM-dd");
                    cols.Add(date);
                    dtable.Columns.Add(date);
                }

                cols.Add("合计");
                dtable.Columns.Add("合计");
                var k = 0;
                foreach (var item in data)
                {
                    var index = k;
                    if (index >= dto.PageSize)
                    {
                        k = 0;
                    }

                    List<object> colList = new List<object>();
                    colList.Add(++k);
                    colList.Add(item.FullPathText);
                    colList.Add(item.UserName);
                    var reportdatelist = new List<string>();
                    if (!string.IsNullOrWhiteSpace(item.ReportDate))
                    {
                        reportdatelist = item.ReportDate.Split(',').ToList();
                    }

                    var countdays = 0;
                    for (var j = 0; j <= cols.Count - 2; j++)
                    {
                        var col = cols[j];
                        var isexist = reportdatelist.FirstOrDefault(x => x == col);
                        if (isexist != null)
                        {
                            colList.Add("√");
                            countdays += 1;
                            if (dic.ContainsKey(col))
                            {
                                dic[col] += 1;
                            }
                            else
                            {
                                dic.Add(col, 1);
                            }
                        }
                        else
                        {
                            colList.Add("×");
                        }
                    }

                    colList.Add(countdays + "天");
                    dtable.Rows.Add(colList.ToArray());
                }

                if (!dto.IsAll)
                {
                    dtable = dtable.AsEnumerable().Skip((dto.PageIndex - 1) * dto.PageSize).Take(dto.PageSize).CopyToDataTable();
                }

                var totalList = new List<object>();
                totalList.Add(string.Empty);
                totalList.Add(string.Empty);
                totalList.Add("合计");
                for (var j = 0; j <= cols.Count - 2; j++)
                {
                    if (dic.ContainsKey(cols[j]))
                    {
                        totalList.Add(dic[cols[j]] + "人");
                    }
                    else
                    {
                        totalList.Add(0 + "人");
                    }
                }

                dtable.Rows.Add(totalList.ToArray());
            }

            return new Tuple<List<string>, DataTable, int>(cols, dtable, total);
        }

        
        
        
        
        
        
        public int Diff(DateTime dt1, DateTime dt2)
        {
            var diff = dt2 - dt1;
            return diff.Days;
        }

        
        
        
        
        
        public IWorkbook ExportDaily(DailyReportQueryDto dto)
        {
            
            
            
            
            
            
            
            var data = GetDailyReport(dto);
            var cols = data.Item1;
            var dt = data.Item2;
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet("日报统计");
            IRow firstTitle = sheet.CreateRow(0);
            (ICellStyle borderStyle, ICellStyle titleStyle) = GetExcelStyle(workbook);

            
            
            
            var numberCell = firstTitle.CreateCell(0);
            numberCell.SetCellValue("序号");
            numberCell.CellStyle = titleStyle;

            var numberCell1 = firstTitle.CreateCell(1);
            numberCell1.SetCellValue("组织");
            numberCell1.CellStyle = titleStyle;

            var numberCell2 = firstTitle.CreateCell(2);
            numberCell2.SetCellValue("销售担当");
            numberCell2.CellStyle = titleStyle;

            for (int i = 0; i < cols.Count; i++)
            {
                var cell = firstTitle.CreateCell(i + 3);
                cell.SetCellValue(cols[i]);
                cell.CellStyle = titleStyle;
            }

            for (int i = 0; i < dt.Rows.Count; i++)
            {
                var row = sheet.CreateRow(i + 1);
                var number = row.CreateCell(0);
                number.CellStyle = borderStyle;
                number.SetCellValue(i + 1);

                var org = row.CreateCell(1);
                org.CellStyle = borderStyle;
                org.SetCellValue(dt.Rows[i]["组织"].ToString());

                var sales = row.CreateCell(2);
                sales.CellStyle = borderStyle;
                sales.SetCellValue(dt.Rows[i]["销售担当"].ToString());

                for (int j = 0; j < cols.Count; j++)
                {
                    var currentData = dt.Rows[i][cols[j]];
                    var cell = row.CreateCell(3 + j);
                    cell.SetCellValue(currentData.ToString());
                    cell.CellStyle = borderStyle;
                    sheet.SetColumnWidth(j + 1, 20 * 256);
                }
            }

            return workbook;
        }

        
        
        
        
        
        protected virtual (ICellStyle borderStyle, ICellStyle titleStyle) GetExcelStyle(IWorkbook workbook)
        {
            #region 

            
            ICellStyle borderStyle = workbook.CreateCellStyle();
            borderStyle.BorderLeft = BorderStyle.Thin;
            borderStyle.BorderRight = BorderStyle.Thin;
            borderStyle.BorderTop = BorderStyle.Thin;
            borderStyle.BorderBottom = BorderStyle.Thin;
            #endregion

            #region 

            
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;  
            titleStyle.VerticalAlignment = VerticalAlignment.Center;  
            IFont font = workbook.CreateFont(); 
            font.IsBold = true; 
            font.FontHeightInPoints = 10; 
            font.FontName = "微软雅黑"; 
            titleStyle.SetFont(font); 
            titleStyle.FillForegroundColor = HSSFColor.Grey25Percent.Index; 
            titleStyle.FillPattern = FillPattern.SolidForeground;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            #endregion

            return (borderStyle, titleStyle);
        }
    }
}
