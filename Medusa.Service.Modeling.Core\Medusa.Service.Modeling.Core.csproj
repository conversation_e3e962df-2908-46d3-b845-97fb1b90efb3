﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netcoreapp3.1</TargetFramework>
        <CodeAnalysisRuleSet>..\csharp.ruleset</CodeAnalysisRuleSet>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
        <DocumentationFile>bin\Debug\netcoreapp3.1\Medusa.Service.Modeling.Core.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
        <DocumentationFile>bin\Release\netcoreapp3.1\Medusa.Service.Modeling.Core.xml</DocumentationFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="nacos-sdk-csharp-unofficial.AspNetCore" Version="0.8.5" />
        <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Entities\Boost\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\Medusa.Service.Cache\Medusa.Service.Cache.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.BPM.Reactor\MT.Enterprise.BPM.Reactor.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Core\MT.Enterprise.Core.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.SDK\MT.Enterprise.SDK.csproj" />
      <ProjectReference Include="..\..\mt.enterprise.tools.shuiwu\src\MT.Enterprise.Utils\MT.Enterprise.Utils.csproj" />
    </ItemGroup>

</Project>
